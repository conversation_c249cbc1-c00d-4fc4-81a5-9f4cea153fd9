# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%available% van %total% tekens)
dialog.title = Lettertype inbedding
label.individual = Afzonderlijke tekens:
button.loadfont = Lettertype laden van schijf...
filter.ttf = TrueType-lettertypebestanden (*.ttf)
error.invalidfontfile = Ongeldig lettertypebestand
error.cannotreadfontfile = Kan het lettertypebestand niet lezen
installed = Ge\u00efnstalleerd:
ttffile.noselection = TTF-bestand: <selecteer>
ttffile.selection = TTF-bestand: %fontname% (%filename%)
allcharacters = Alle tekens (%available% tekens)
#after 14.0.0
ascentdescentleading = Stel oplopend, aflopend en eerste in
#after 19.1.2
font.name = Lettertypenaam:
font.name.default = Mijn lettertype
font.source = Bron:
