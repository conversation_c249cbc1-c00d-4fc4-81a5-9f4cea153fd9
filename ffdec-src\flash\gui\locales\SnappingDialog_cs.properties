# Copyright (C) 2025 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

dialog.title = P\u0159ichycen\u00ed
button.ok = OK
button.cancel = Storno

snapAlign = P\u0159ichytit k zarovn\u00e1n\u00ed
snapToGrid = P\u0159ichytit k m\u0159\u00ed\u017ece
snapToGuides = P\u0159ichytit k vod\u00edtk\u00e1m
snapToPixels = P\u0159ichytit k pixel\u016fm
snapToObjects = P\u0159ichytit k objekt\u016fm

snapAlign.settings = Nastaven\u00ed p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed
snapAlign.stageBorder = Okraj sc\u00e9ny:
snapAlign.objectSpacing = Mezery mezi objekty:
snapAlign.objectSpacing.horizontal = Horizont\u00e1ln\u00ed:
snapAlign.objectSpacing.vertical = Vertik\u00e1ln\u00ed:
snapAlign.centerAlignment = Zarovn\u00e1n\u00ed st\u0159edu:
snapAlign.centerAlignment.horizontal = Horizont\u00e1ln\u00ed zarovn\u00e1n\u00ed st\u0159edu
snapAlign.centerAlignment.vertical = Vertik\u00e1ln\u00ed zarovn\u00e1n\u00ed st\u0159edu

error.invalidSpacing = Neplatn\u00e1 hodnota mezery. O\u010dek\u00e1v\u00e1no cel\u00e9 \u010d\u00edslo.
error.invalidBorder = Neplatn\u00e1 hodnota okraje. O\u010dek\u00e1v\u00e1no cel\u00e9 \u010d\u00edslo.