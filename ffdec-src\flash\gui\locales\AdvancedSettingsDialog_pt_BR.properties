# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Configura\u00e7\u00f5es Avan\u00e7adas
advancedSettings.restartConfirmation = Voc\u00ea deve reiniciar o programa para que algumas modifica\u00e7\u00f5es tenham efeito. Deseja reinici\u00e1-lo agora?
advancedSettings.columns.name = Nome
advancedSettings.columns.value = Valor
advancedSettings.columns.description = Descri\u00e7\u00e3o
default = default
config.group.name.export = Exportar
config.group.description.export = Configura\u00e7\u00e3o de exporta\u00e7\u00f5es
config.group.name.script = Scripts
config.group.description.script = Relacionado \u00e0 descompila\u00e7\u00e3o do ActionScript
config.group.name.update = Atualiza\u00e7\u00f5es
config.group.description.update = Verificando atualiza\u00e7\u00f5es
config.group.name.format = Formatando
config.group.description.format = Formata\u00e7\u00e3o de c\u00f3digo ActionScript
config.group.name.limit = Limites
config.group.description.limit = Limites de descompila\u00e7\u00e3o para c\u00f3digo ofuscado, etc.
config.group.name.ui = Interface
config.group.description.ui = Configura\u00e7\u00e3o da interface do usu\u00e1rio
config.group.name.debug = Depurar
config.group.description.debug = Configura\u00e7\u00f5es de depura\u00e7\u00e3o
config.group.name.display = Tela
config.group.description.display = Exibi\u00e7\u00e3o de objetos Flash, etc.
config.group.name.decompilation = Descompila\u00e7\u00e3o
config.group.description.decompilation = Fun\u00e7\u00f5es relacionadas \u00e0 descompila\u00e7\u00e3o global
config.group.name.other = Outros
config.group.description.other = Outras configura\u00e7\u00f5es n\u00e3o categorizadas
config.name.openMultipleFiles = Abrir v\u00e1rios arquivos
config.description.openMultipleFiles = Permite abrir v\u00e1rios arquivos de uma s\u00f3 vez em uma janela
config.name.decompile = Mostrar fonte ActionScript
config.description.decompile = Voc\u00ea pode desabilitar a descompila\u00e7\u00e3o AS, ent\u00e3o apenas o P-code \u00e9 mostrado
config.name.dumpView = Visualiza\u00e7\u00e3o de despejo
config.description.dumpView = Ver despejo de dados brutos
config.name.useHexColorFormat = Cores em formato hexadecimal
config.description.useHexColorFormat = Mostra as cores em formato hexadecimal
config.name.parallelSpeedUp = Acelera\u00e7\u00e3o Paralela
config.description.parallelSpeedUp = O paralelismo pode acelerar a descompila\u00e7\u00e3o
config.name.parallelSpeedUpThreadCount = N\u00famero de threads (0 = auto)
config.description.parallelSpeedUpThreadCount = N\u00famero de threads para acelera\u00e7\u00e3o paralela. 0 = processor count - 1.
config.name.autoDeobfuscate = Desofusca\u00e7\u00e3o autom\u00e1tica
config.description.autoDeobfuscate = Executa a desofusca\u00e7\u00e3o em todos os arquivos antes da descompila\u00e7\u00e3o do ActionScript
config.name.cacheOnDisk = Usar cache no disco
config.description.cacheOnDisk = Cache de partes j\u00e1 descompiladas no disco r\u00edgido em vez da mem\u00f3ria
config.name.internalFlashViewer = Usar o pr\u00f3prio visualizador de Flash
config.description.internalFlashViewer = Usa o JPEXS Flash Viewer em vez do Flash Player padr\u00e3o para exibi\u00e7\u00e3o de partes em flash
config.name.gotoMainClassOnStartup = Ir para a classe principal na inicializa\u00e7\u00e3o (AS3)
config.description.gotoMainClassOnStartup = Navega para a classe de documento do arquivo AS3 na abertura do SWF
config.name.autoRenameIdentifiers = Renomea\u00e7\u00e3o autom\u00e1tica de identificadores
config.description.autoRenameIdentifiers = Renomeia automaticamente identificadores inv\u00e1lidos no carregamento de SWF
config.name.offeredAssociation = (Interno) Associa\u00e7\u00e3o com arquivos SWF exibidos
config.description.offeredAssociation = A caixa de di\u00e1logo sobre a associa\u00e7\u00e3o de arquivos j\u00e1 foi exibida
config.name.decimalAddress = Usar endere\u00e7os decimais
config.description.decimalAddress = Usa endere\u00e7os decimais em vez de hexadecimais
config.name.showAllAddresses = Mostrar todos os endere\u00e7os
config.description.showAllAddresses = Exibir todos os endere\u00e7os de instru\u00e7\u00f5es do ActionScript
config.name.useFrameCache = Usar cache de quadros
config.description.useFrameCache = Armazenar em cache antes de renderizar novamente
config.name.useRibbonInterface = Interface da faixa de op\u00e7\u00f5es
config.description.useRibbonInterface = Desmarque para usar a interface cl\u00e1ssica sem o menu da faixa de op\u00e7\u00f5es
config.name.openFolderAfterFlaExport = Abrir pasta ap\u00f3s a exporta\u00e7\u00e3o FLA
config.description.openFolderAfterFlaExport = Exibir diret\u00f3rio de sa\u00edda ap\u00f3s exportar o arquivo FLA
config.name.useDetailedLogging = Registro detalhado do FFDec
config.description.useDetailedLogging = Registre mensagens de erro detalhadas e informa\u00e7\u00f5es para depura\u00e7\u00e3o do FFDec
config.name._debugMode=FFDec no modo de depura\u00e7\u00e3o
config.description._debugMode=Modo para depura\u00e7\u00e3o de FFDec. Ativa o menu de depura\u00e7\u00e3o. Isso n\u00e3o tem nada a ver com a funcionalidade do depurador
config.name.resolveConstants = Resolver constantes no p-code AS1/2
config.description.resolveConstants = Desative isso para mostrar 'constantxx' em vez de valores reais na janela de P-code
config.name.sublimiter = Limite de subs de c\u00f3digo
config.description.sublimiter = Limite de subs de P-codeara c\u00f3digo ofuscado.
config.name.exportTimeout = Tempo limite total de exporta\u00e7\u00e3o (segundos)
config.description.exportTimeout = O descompilador interromper\u00e1 de exportar ap\u00f3s atingir esse tempo
config.name.decompilationTimeoutFile = Tempo limite de descompila\u00e7\u00e3o de arquivo \u00fanico (segundos)
config.description.decompilationTimeoutFile = O descompilador interromper\u00e1 a descompila\u00e7\u00e3o do ActionScript ap\u00f3s atingir esse tempo em um arquivo
config.name.paramNamesEnable = Habilitar nomes de par\u00e2metros em AS3
config.description.paramNamesEnable = Usar nomes de par\u00e2metros na descompila\u00e7\u00e3o pode causar problemas porque programas oficiais como o Flash CS 5.5 inserem \u00edndices de nomes de par\u00e2metros errados
config.name.displayFileName = Mostrar nome do SWF no t\u00edtulo
config.description.displayFileName = Exibir o nome do arquivo/url SWF no t\u00edtulo da janela (voc\u00ea pode fazer capturas de tela ent\u00e3o)
config.name._debugCopy=Depurar recompila\u00e7\u00e3o no FFDec
config.description._debugCopy=Tenta compilar o arquivo SWF novamente logo ap\u00f3s a abertura para garantir que ele produza o mesmo c\u00f3digo bin\u00e1rio. Use apenas para DEPURA\u00c7\u00c3O do FFDec!
config.name.dumpTags = Despejar tags no console
config.description.dumpTags = Despeje as tags no console ao ler o arquivo SWF
config.name.decompilationTimeoutSingleMethod = AS3: Tempo limite de descompila\u00e7\u00e3o de m\u00e9todo \u00fanico (segundos)
config.description.decompilationTimeoutSingleMethod = O descompilador interromper\u00e1 a descompila\u00e7\u00e3o do ActionScript ap\u00f3s atingir esse tempo em um m\u00e9todo
config.name.lastRenameType = (Internal) (Interno) Tipo de \u00faltima renomea\u00e7\u00e3o
config.description.lastRenameType = Tipo de renomea\u00e7\u00e3o de identificadores usados pela \u00faltima vez
config.name.lastSaveDir = (Interno) \u00daltimo diret\u00f3rio salvo
config.description.lastSaveDir = \u00daltimo diret\u00f3rio de salvamento usado
config.name.lastOpenDir = (Interno) \u00daltimo diret\u00f3rio aberto
config.description.lastOpenDir = \u00daltimo diret\u00f3rio aberto usado
config.name.lastExportDir = (Interno) \u00daltimo diret\u00f3rio de exporta\u00e7\u00e3o
config.description.lastExportDir = \u00daltimo diret\u00f3rio de exporta\u00e7\u00e3o usado
config.name.locale = Idioma
config.description.locale = Identificadores de localiza\u00e7\u00f5es
config.name.registerNameFormat = Formato da vari\u00e1vel de registro
config.description.registerNameFormat = Formato de nomes de vari\u00e1veis de registro local. Use %d para n\u00famero de registro.
config.name.maxRecentFileCount = Contagem m\u00e1xima de recentes
config.description.maxRecentFileCount = N\u00famero m\u00e1ximo de arquivos recentes
config.name.recentFiles = (Interno) Arquivos recentes
config.description.recentFiles = Arquivos abertos recentes
config.name.fontPairingMap = (Interno) Pares de fontes para importa\u00e7\u00e3o
config.description.fontPairingMap = Font pairs for importing new characters
config.name.lastUpdatesCheckDate = (Interno) Data de verifica\u00e7\u00e3o da \u00faltima atualiza\u00e7\u00e3o
config.description.lastUpdatesCheckDate = Data da \u00faltima verifica\u00e7\u00e3o de atualiza\u00e7\u00f5es no servidor
config.name.gui.window.width = (Interno) Largura da \u00faltima janela
config.description.gui.window.width = Largura da \u00faltima janela salva
config.name.gui.window.height = (Interno) Altura da \u00faltima janela
config.description.gui.window.height = Altura da \u00faltima janela salva
config.name.gui.window.maximized.horizontal = (Interno) Janela maximizada horizontalmente
config.description.gui.window.maximized.horizontal = Estado da \u00faltima janela - maximizado horizontalmente
config.name.gui.window.maximized.vertical = (Interno) Janela maximizada verticalmente
config.description.gui.window.maximized.vertical = Estado da \u00faltima janela - maximizado verticalmente
config.name.gui.avm2.splitPane.dividerLocationPercent=(Interno) Localiza\u00e7\u00e3o do divisor AS3
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Interno) Localiza\u00e7\u00e3o do divisor AS1/2
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Interno) Visualiza\u00e7\u00e3o do local do divisor
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Internal) Localiza\u00e7\u00e3o do divisor 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Internal) Localiza\u00e7\u00e3o do divisor 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Salvar como modo de escala EXE
config.description.saveAsExeScaleMode = Modo de dimensionamento para exporta\u00e7\u00e3o EXE
config.name.syntaxHighlightLimit = M\u00e1ximo de caracteres para realce de sintaxe
config.description.syntaxHighlightLimit = N\u00famero m\u00e1ximo de caracteres para executar o realce de sintaxe
config.name.guiFontPreviewSampleText = (Interno) Texto de exemplo de visualiza\u00e7\u00e3o da \u00faltima fonte
config.description.guiFontPreviewSampleText = \u00cdndice da lista de texto de exemplo de visualiza\u00e7\u00e3o da \u00faltima fonte
config.name.gui.fontPreviewWindow.width = (Interno) Largura da janela de visualiza\u00e7\u00e3o da \u00faltima fonte
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Interno) Altura da janela de visualiza\u00e7\u00e3o da \u00faltima fonte
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Interno) \u00daltima janela de visualiza\u00e7\u00e3o da fonte X
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Interno) \u00daltima janela de visualiza\u00e7\u00e3o de fonte Y
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Caracteres por indenta\u00e7\u00e3o
config.description.formatting.indent.size = N\u00famero de espa\u00e7os (ou tabula\u00e7\u00f5es) para uma indenta\u00e7\u00e3o
config.name.formatting.indent.useTabs = Tabula\u00e7\u00f5es para indenta\u00e7\u00e3o
config.description.formatting.indent.useTabs = Use tabula\u00e7\u00f5es em vez de espa\u00e7os para recuo
config.name.beginBlockOnNewLine = Chaves na nova linha
config.description.beginBlockOnNewLine = Comece o bloco com chave na nova linha
config.name.check.updates.delay = Atraso de verifica\u00e7\u00e3o de atualiza\u00e7\u00f5es
config.description.check.updates.delay = Tempo m\u00ednimo entre verifica\u00e7\u00f5es autom\u00e1ticas de atualiza\u00e7\u00f5es no in\u00edcio do aplicativo
config.name.check.updates.stable = Verifique se h\u00e1 vers\u00f5es est\u00e1veis
config.description.check.updates.stable = Verificando atualiza\u00e7\u00f5es de vers\u00f5es est\u00e1veis
config.name.check.updates.nightly = Verifique as vers\u00f5es noturnas
config.description.check.updates.nightly = Verificando atualiza\u00e7\u00f5es de vers\u00e3o noturnas
config.name.check.updates.enabled = Verifica\u00e7\u00e3o de atualiza\u00e7\u00f5es ativada
config.description.check.updates.enabled = Verifica\u00e7\u00e3o autom\u00e1tica de atualiza\u00e7\u00f5es no in\u00edcio do aplicativo
config.name.export.formats = (Interno) Formatos de exporta\u00e7\u00e3o
config.description.export.formats = \u00daltimos formatos de exporta\u00e7\u00e3o usados
config.name.textExportSingleFile = Exportar textos para um \u00fanico arquivo
config.description.textExportSingleFile = Exportando textos para um arquivo em vez de v\u00e1rios
config.name.textExportSingleFileSeparator = Separador de textos em um arquivo de exporta\u00e7\u00e3o de texto
config.description.textExportSingleFileSeparator = Texto para inserir entre textos na exporta\u00e7\u00e3o de texto de arquivo \u00fanico
config.name.textExportSingleFileRecordSeparator = Separador de registros em um arquivo de exporta\u00e7\u00e3o de texto
config.description.textExportSingleFileRecordSeparator = Texto a ser inserido entre registros de texto na exporta\u00e7\u00e3o de texto de arquivo \u00fanico
config.name.warning.experimental.as12edit=Avisar na edi\u00e7\u00e3o direta AS1/2
config.description.warning.experimental.as12edit=Mostrar aviso na edi\u00e7\u00e3o direta experimental AS1/2
config.name.warning.experimental.as3edit=Avisar na edi\u00e7\u00e3o direta do AS3
config.description.warning.experimental.as3edit=Mostrar aviso na edi\u00e7\u00e3o direta experimental AS3
config.name.packJavaScripts = Pacote de JavaScript
config.description.packJavaScripts = Executa o empacotador de JavaScript em scripts criados no Canvas Export.
config.name.textExportExportFontFace = Usar font-face na exporta\u00e7\u00e3o SVG
config.description.textExportExportFontFace = Incorporar arquivos de fonte em SVG usando font-face em vez de formas
config.name.lzmaFastBytes = Bytes r\u00e1pidos LZMA (valores v\u00e1lidos: 5-255)
config.description.lzmaFastBytes = Par\u00e2metro de bytes r\u00e1pidos do codificador LZMA
config.name.pluginPath = Caminho do plugin
config.description.pluginPath = -
config.name.showMethodBodyId = Mostrar ID do corpo do m\u00e9todo
config.description.showMethodBodyId = Mostra o id do corpo do m\u00e9todo para importa\u00e7\u00e3o de linha de comando
config.name.export.zoom = (Interno) Zoom de exporta\u00e7\u00e3o
config.description.export.zoom = Zoom de exporta\u00e7\u00e3o usado pela \u00faltima vez
config.name.debuggerPort = Porta do depurador
config.description.debuggerPort = Porta usada para depura\u00e7\u00e3o de soquete
config.name.displayDebuggerInfo = (Interno) Exibir informa\u00e7\u00f5es do depurador
config.description.displayDebuggerInfo = Exibir informa\u00e7\u00f5es sobre o depurador antes de troc\u00e1-lo
config.name.randomDebuggerPackage = Use o nome do pacote aleat\u00f3rio para o Depurador
config.description.randomDebuggerPackage = Isso renomeia o pacote Depurador para string aleat\u00f3ria, o que dificulta a detec\u00e7\u00e3o da presen\u00e7a do depurador pelo ActionScript
config.name.lastDebuggerReplaceFunction = (Interno) \u00daltima substitui\u00e7\u00e3o de rastreamento selecionada
config.description.lastDebuggerReplaceFunction = Nome da fun\u00e7\u00e3o que foi selecionada pela \u00faltima vez em substituir a fun\u00e7\u00e3o de rastreamento pelo depurador
config.name.getLocalNamesFromDebugInfo = AS3: Obtenha nomes de registro local de informa\u00e7\u00f5es de depura\u00e7\u00e3o
config.description.getLocalNamesFromDebugInfo = Se as informa\u00e7\u00f5es de depura\u00e7\u00e3o estiverem presentes, renomear\u00e1 os registros locais de _loc_x_ para nomes reais. Isso pode ser desativado porque alguns ofuscadores usam nomes de registro inv\u00e1lidos.
config.name.tagTreeShowEmptyFolders = Mostrar pastas vazias
config.description.tagTreeShowEmptyFolders = Mostrar pastas vazias na \u00e1rvore de tags.
config.name.autoLoadEmbeddedSwfs = Carregar automaticamente SWFs incorporados
config.description.autoLoadEmbeddedSwfs = Carrega automaticamente os SWFs incorporados das tags DefineBinaryData.
config.name.overrideTextExportFileName = Substituir o nome do arquivo de exporta\u00e7\u00e3o de texto
config.description.overrideTextExportFileName = Voc\u00ea pode personalizar o nome do arquivo do texto exportado. Use o espa\u00e7o reservado {filename} para usar o nome de arquivo do SWF atual.
config.name.showOldTextDuringTextEditing = Mostrar texto antigo durante a edi\u00e7\u00e3o de texto
config.description.showOldTextDuringTextEditing = Mostra o texto original da tag de texto com cor cinza na \u00e1rea de visualiza\u00e7\u00e3o.
config.group.name.import = Importar
config.group.description.import = Configura\u00e7\u00e3o de importa\u00e7\u00f5es
config.name.textImportResizeTextBoundsMode = Modo de redimensionamento de limites de texto
config.description.textImportResizeTextBoundsMode = Modo de redimensionamento de limites de texto ap\u00f3s edi\u00e7\u00e3o de texto.
config.name.showCloseConfirmation = Mostrar novamente a confirma\u00e7\u00e3o de fechamento do SWF
config.description.showCloseConfirmation = Mostrar novamente a confirma\u00e7\u00e3o de fechamento do SWF para arquivos modificados.
config.name.showCodeSavedMessage = Mostrar novamente a mensagemde  salvar do c\u00f3digo
config.description.showCodeSavedMessage = Mostra novamente a mensagem de salvar do c\u00f3digo
config.name.showTraitSavedMessage = Mostrar novamente a mensagem de salvar do trait
config.description.showTraitSavedMessage = Mostra novamente a mensagem de salvar do trait
config.name.updateProxyAddress = Endere\u00e7o proxy HTTP para verificar atualiza\u00e7\u00f5es
config.description.updateProxyAddress = Endere\u00e7o proxy HTTP para verificar atualiza\u00e7\u00f5es. Formato: exemplo.com:8080
config.name.editorMode = Modo Editor
config.description.editorMode = Torna as \u00e1reas de texto edit\u00e1veis automaticamente ao selecionar um n\u00f3 Texto ou Script
config.name.autoSaveTagModifications = Salvar automaticamente as modifica\u00e7\u00f5es de tags
config.description.autoSaveTagModifications = Salva as altera\u00e7\u00f5es ao selecionar uma nova tag na \u00e1rvore
config.name.saveSessionOnExit = Salvar sess\u00e3o ao sair
config.description.saveSessionOnExit = Salva a sess\u00e3o atual e reabre ap\u00f3s a reinicializa\u00e7\u00e3o do FFDec (funciona apenas com arquivos reais)
config.name._showDebugMenu=Mostrar menu de depura\u00e7\u00e3o do FFDec
config.description._showDebugMenu=Mostra o menu de depura\u00e7\u00e3o na faixa de op\u00e7\u00f5es para depura\u00e7\u00e3o do descompilador.
config.name.allowOnlyOneInstance = Permitir apenas uma inst\u00e2ncia FFDec (somente sistema operacional Windows)
config.description.allowOnlyOneInstance = O FFDec pode ser executado apenas uma vez, todos os arquivos abertos ser\u00e3o adicionados a uma janela. Funciona apenas com o sistema operacional Windows.
config.name.scriptExportSingleFile = Exportar scripts para um \u00fanico arquivo
config.description.scriptExportSingleFile = Exportando scripts para um arquivo em vez de v\u00e1rios
config.name.setFFDecVersionInExportedFont = Definir o n\u00famero da vers\u00e3o do FFDec na fonte exportada
config.description.setFFDecVersionInExportedFont = Quando essa configura\u00e7\u00e3o est\u00e1 desabilitada, o FFDec n\u00e3o adicionar\u00e1 o n\u00famero da vers\u00e3o atual do FFDec \u00e0 fonte exportada.
config.name.gui.skin = Tema da interface do usu\u00e1rio
config.description.gui.skin = Olhe e aprecie a skin
config.name.lastSessionFiles = Arquivos da \u00faltima sess\u00e3o
config.description.lastSessionFiles = Cont\u00e9m os arquivos abertos da \u00faltima sess\u00e3o
config.name.lastSessionSelection = Sele\u00e7\u00e3o da \u00faltima sess\u00e3o
config.description.lastSessionSelection = Cont\u00e9m a sele\u00e7\u00e3o da \u00faltima sess\u00e3o
config.name.loopMedia = Repetir sons e sprites
config.description.loopMedia = Reinicia automaticamente a reprodu\u00e7\u00e3o dos sons e sprites
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Interno) Local do divisor de linha do tempo
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Cache de imagens
config.description.cacheImages = Armazena em cache os objetos de imagem decodificados
config.name.swfSpecificConfigs = Configura\u00e7\u00f5es espec\u00edficas de SWF
config.description.swfSpecificConfigs = Cont\u00e9m as configura\u00e7\u00f5es espec\u00edficas do SWF
config.name.exeExportMode = Modo de exporta\u00e7\u00e3o EXE
config.description.exeExportMode = Modo de exporta\u00e7\u00e3o EXE
config.name.ignoreCLikePackages = Ignore FlashCC / Alchemy ou pacotes semelhantes
config.description.ignoreCLikePackages = Os pacotes FlashCC/Alchemy geralmente n\u00e3o podem ser descompilados corretamente. Voc\u00ea pode desativ\u00e1-los para acelerar a descompila\u00e7\u00e3o de outros pacotes.
config.name.overwriteExistingFiles = Substituir os arquivos existentes
config.description.overwriteExistingFiles = Substituir os arquivos existentes durante a exporta\u00e7\u00e3o. Atualmente apenas para scripts AS2/3
config.name.smartNumberFormatting = Use a formata\u00e7\u00e3o de n\u00famero inteligente
config.description.smartNumberFormatting = Formatar n\u00fameros especiais (por exemplo, cores e horas)
config.name.enableScriptInitializerDisplay = (REMOVED) Exibir inicializadores de script
config.description.enableScriptInitializerDisplay = Ative a exibi\u00e7\u00e3o e a edi\u00e7\u00e3o dos inicializadores de script. Essa configura\u00e7\u00e3o pode adicionar uma nova linha a cada arquivo de classe para realce.
config.name.autoOpenLoadedSWFs = Abrir SWFs carregados durante a execu\u00e7\u00e3o (Visualizador externo = apenas WIN)
config.description.autoOpenLoadedSWFs = Abre automaticamente todos os SWFs carregados pelo carregador de classe AS3 executando SWF quando reproduzido no player externo FFDec. Esse recurso \u00e9 apenas para Windows.
config.name.lastSessionFileTitles = T\u00edtulos de arquivo da \u00faltima sess\u00e3o
config.description.lastSessionFileTitles = Cont\u00e9m os t\u00edtulos dos arquivos abertos da \u00faltima sess\u00e3o (por exemplo, quando carregado de URL etc.)
config.group.name.paths = Caminhos
config.group.description.paths = Localiza\u00e7\u00e3o dos arquivos necess\u00e1rios
config.group.tip.paths = Baixe o projetor e o Playerglobal em <a href="%link1%">adobe webpage</a>. Flex SDK pode ser baixado em <a href="%link2%">apache web</a>.
config.group.link.paths = https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Caminho do projetor do Flash Player
config.description.playerLocation = Localiza\u00e7\u00e3o do execut\u00e1vel do flash player aut\u00f4nomo. Usado para a a\u00e7\u00e3o Executar.
config.name.playerDebugLocation = 2) Caminho do depurador de conte\u00fado do projetor do Flash Player
config.description.playerDebugLocation = Localiza\u00e7\u00e3o do execut\u00e1vel de depura\u00e7\u00e3o do flash player aut\u00f4nomo. Usado para a\u00e7\u00e3o de depura\u00e7\u00e3o.
config.name.playerLibLocation = 3) Caminho do PlayerGlobal (.swc)
config.description.playerLibLocation = Localiza\u00e7\u00e3o da biblioteca playerglobal.swc flash player. \u00c9 usado principalmente para compila\u00e7\u00e3o AS3.
config.name.debugHalt = Parar a execu\u00e7\u00e3o no in\u00edcio da depura\u00e7\u00e3o
config.description.debugHalt = Pause o SWF no in\u00edcio da depura\u00e7\u00e3o.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Interno) Localiza\u00e7\u00e3o do divisor do menu de depura\u00e7\u00e3o
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Dica: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Interno) Localiza\u00e7\u00e3o do divisor de menu de depura\u00e7\u00e3o AS1/2
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = Atraso antes de alterar o SWF no player externo em ms
config.description.setMovieDelay = N\u00e3o \u00e9 recomendado alterar este valor abaixo de 1000ms
config.name.warning.svgImport = Avisar na importa\u00e7\u00e3o de SVG
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Usar preenchimento n\u00e3o suavizado quando uma forma for substitu\u00edda por uma imagem
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=AS1/2 no pr\u00f3prio visualizador de flash (Experimental)
config.description.internalFlashViewer.execute.as12=Tenta executar o ActionScript 1/2 durante a reprodu\u00e7\u00e3o de SWF usando o visualizador de flash FFDec
config.name.warning.hexViewNotUpToDate = Mostrar aviso hexadecimal n\u00e3o atualizado
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = Mostrar instru\u00e7\u00f5es \u00a7\u00a7dup
config.description.displayDupInstructions = Mostra instru\u00e7\u00f5es \u00a7\u00a7dup no c\u00f3digo. Sem eles, o P-codeode ser facilmente compilado, mas alguns c\u00f3digos duplicados com efeitos colaterais podem ser executados duas vezes.
config.name.useRegExprLiteral = Descompile RegExp como /pattern/mod literal.
config.description.useRegExprLiteral = Usa a sintaxe /pattern/mod ao descompilar express\u00f5es regulares. new RegExp("pat","mod") \u00e9 usado de outra forma
config.name.handleSkinPartsAutomatically = Lidar com metadados [SkinPart] automaticamente
config.description.handleSkinPartsAutomatically = Descompila e edita os metadados [SkinPart] automaticamente. Quando desativado, o atributo _skinParts e seu m\u00e9todo getter ficam vis\u00edveis e edit\u00e1veis manualmente.
config.name.simplifyExpressions = Simplificar express\u00f5es
config.description.simplifyExpressions = Avalia e simplifique express\u00f5es para tornar o c\u00f3digo mais leg\u00edvel
config.name.resetLetterSpacingOnTextImport = Redefinir o espa\u00e7amento entre letras na importa\u00e7\u00e3o de texto
config.description.resetLetterSpacingOnTextImport = \u00datil para fontes cir\u00edlicas, porque s\u00e3o mais largas
config.name.flexSdkLocation = 4) Caminho do diret\u00f3rio do Flex SDK
config.description.flexSdkLocation = Localiza\u00e7\u00e3o do Adobe Flex SDK. \u00c9 usado principalmente para compila\u00e7\u00e3o AS3.
config.name.useFlexAs3Compiler=Usar o compilador Flex SDK AS3
config.description.useFlexAs3Compiler=Use o compilador AS3 do Flex SDK durante a edi\u00e7\u00e3o direta do ActionScript (o diret\u00f3rio do Flex SDK precisa ser definido)
config.name.showSetAdvanceValuesMessage = Mostrar novamente informa\u00e7\u00f5es sobre a configura\u00e7\u00e3o de valores avan\u00e7ados
config.description.showSetAdvanceValuesMessage = Exibe novamente informa\u00e7\u00f5es sobre a configura\u00e7\u00e3o de valores avan\u00e7ados
config.name.gui.fontSizeMultiplier = Multiplicador de tamanho de fonte
config.description.gui.fontSizeMultiplier = Multiplicador de tamanho de fonte
config.name.graphVizDotLocation = 5) Caminho do execut\u00e1vel GraphViz Dot
config.description.graphVizDotLocation = Caminho para dot.exe (ou similar para Linux) do aplicativo GraphViz para exibir gr\u00e1ficos.
#Do not translate the Font Styles which is in the parenthesis:(Plain,Bold,Italic,BoldItalic)
config.name.gui.sourceFont = Estilo da fonte de origem
config.description.gui.sourceFont = FontName-FontStyle(Plain,Bold,Italic,BoldItalic)-FontSize
#after 11.1.0
config.name.as12DeobfuscatorExecutionLimit=Limite de execu\u00e7\u00e3o do desofuscador AS1/2
config.description.as12DeobfuscatorExecutionLimit=N\u00famero m\u00e1ximo de instru\u00e7\u00f5es processadas durante a desofusca\u00e7\u00e3o de execu\u00e7\u00e3o AS1/2
#option that ignore in 8.0.1 and other versions
config.name.showOriginalBytesInPcodeHex = (Interno) Mostrar bytes originais
config.description.showOriginalBytesInPcodeHex = Exibe bytes originais em c\u00f3digo Pcode em hexadecimal.
config.name.showFileOffsetInPcodeHex = (Interno) Mostrar deslocamento do arquivo
config.description.showFileOffsetInPcodeHex = Exibe o deslocamento do arquivo em c\u00f3digo Pcode em hexadecimal.
config.name._enableFlexExport=(Interno) Habilitar exporta\u00e7\u00e3o Flex
config.description.enableFlexExport = Habilita exporta\u00e7\u00e3o Flex
config.name._ignoreAdditionalFlexClasses=(Interno) Ignorar classes flex adicionais
config.description.ignoreAdditionalFlexClasses = Ignore classes flex adicionais
config.name.hwAcceleratedGraphics = 
config.description.hwAcceleratedGraphics = 
config.name.gui.avm2.splitPane.docs.dividerLocationPercent=(Interno) Localiza\u00e7\u00e3o percentual do divisor de documenta\u00e7\u00e3o de splitPane
config.description.gui.avm2.splitPane.docs.dividerLocationPercent=Localiza\u00e7\u00e3o percentual do divisor de documenta\u00e7\u00e3o de splitPane
config.name.gui.dump.splitPane.dividerLocationPercent = (Interno) Despejar a localiza\u00e7\u00e3o percentual do divisor de splitPan
config.description.gui.dump.splitPane.dividerLocationPercent = Despeja a localiza\u00e7\u00e3o percentual do divisor de splitPane
#after 11.3.0
config.name.useAdobeFlashPlayerForPreviews = (Depreciado) Use o Adobe Flash Player para visualiza\u00e7\u00e3o dos objetos
config.description.useAdobeFlashPlayerForPreviews = Use o Adobe Flash player para visualiza\u00e7\u00e3o de objetos. AVISO: o FlashPlayer foi descontinuado em 12/01/2021
#after 12.0.1
config.name.showLineNumbersInPCodeGraphvizGraph = Mostrar n\u00fameros de linha em gr\u00e1ficos Graphviz
config.description.showLineNumbersInPCodeGraphvizGraph = Mostrar n\u00fameros de linha no gr\u00e1fico P-code do Graphviz.
config.name.padAs3PCodeInstructionName=Adicionar espa\u00e7os aos nomes das instru\u00e7\u00f5es P-code do AS3
config.description.padAs3PCodeInstructionName=Adicionar espa\u00e7os aos nomes das instru\u00e7\u00f5es P-code do AS3
#after 13.0.2
config.name.indentAs3PCode=Indentar P-code do AS3
config.description.indentAs3PCode=Indentar blocos  P-code do AS3 como trait/body/code
config.name.labelOnSeparateLineAs3PCode=Etiqueta no P-code AS3 em linha separada
config.description.labelOnSeparateLineAs3PCode=Cria etiqueta no suporte P-code AS3 em linha separada
config.name.useOldStyleGetSetLocalsAs3PCode=Usar getlocal_x de estilo antigo em vez de getlocalx no P-code AS3
config.description.useOldStyleGetSetLocalsAs3PCode=Usa o estilo antigo de getlocal_x, setlocal_x do FFDec 12.x ou mais antigo
config.name.useOldStyleLookupSwitchAs3PCode=Usar o estilo antigo do switch de pesquisa sem colchetes no P-code AS3
config.description.useOldStyleLookupSwitchAs3PCode=Use o estilo antigo do switch de pesquisa do FFDec 12.x ou mais antigo
#after 13.0.3
config.name.checkForModifications = Verificar se h\u00e1 modifica\u00e7\u00f5es de arquivo fora do FFDec
config.description.checkForModifications = Verifica se h\u00e1 modifica\u00e7\u00f5es de arquivos por outros aplicativos e pe\u00e7a para recarregar
config.name.warning.initializers = Avisar na edi\u00e7\u00e3o de slot/const AS3 sobre inicializadores
config.description.warning.initializers = Mostra aviso no slot/const AS3 sobre inicializadores
config.name.parametersPanelInSearchResults = Mostrar painel de par\u00e2metros nos resultados da pesquisa
config.description.parametersPanelInSearchResults = Mostra painel com par\u00e2metros como texto de pesquisa/ignorar mai\u00fasculas e min\u00fasculas/regexp na janela de resultados de pesquisa
config.name.displayAs3PCodeDocsPanel=Mostrar painel documentos no P-code, AS3
config.description.displayAs3PCodeDocsPanel=Mostra painel com documenta\u00e7\u00e3o de instru\u00e7\u00f5es e estrutura de c\u00f3digo em edi\u00e7\u00e3o e exibi\u00e7\u00e3o de P-code AS3
config.name.displayAs3TraitsListAndConstantsPanel=Mostrar lista de traits AS3 e painel de constantesl
config.description.displayAs3TraitsListAndConstantsPanel=Mostra painel com lista de traits e constantes sob a \u00e1rvore de tags para AS3
#after 14.1.0
config.name.useAsTypeIcons = Usar \u00edcones de script com base no tipo de item
config.description.useAsTypeIcons = Usa \u00edcones diferentes para diferentes tipos de script (class/interface/frame/...)
config.name.limitAs3PCodeOffsetMatching=Limite de correspond\u00eancia de deslocamento de P-code AS3
config.description.limitAs3PCodeOffsetMatching=Limite de instru\u00e7\u00f5es no P-code AS3 que s\u00e3o compat\u00edveis com o script AS3
#after 14.2.1
config.name.showSlowRenderingWarning = Aviso de log quando a renderiza\u00e7\u00e3o \u00e9 muito lenta
config.description.showSlowRenderingWarning = Alerta de logs quando o visualizador de flash interno \u00e9 muito lento para exibir conte\u00fado
#after 14.3.1
config.name.autoCloseQuotes = Fechar aspas simples automaticamente no editor de script
config.description.autoCloseQuotes = Insere automaticamente a segunda aspa simples ' ao digitar a primeira
config.name.autoCloseDoubleQuotes = Fechar aspas duplas automaticamente no editor de script
config.description.autoCloseDoubleQuotes = Insere automaticamente a segunda aspa dupla " ao digitar a primeira
config.name.autoCloseBrackets = Fechar colchetes automaticamente no editor de script
config.description.autoCloseBrackets = Insere automaticamente colchetes de fechamento] na abertura de digita\u00e7\u00e3o [
config.name.autoCloseParenthesis = Fechar par\u00eantese automaticamente no editor de script
config.description.autoCloseParenthesis = Insere automaticamente par\u00eanteses de fechamento) na abertura de digita\u00e7\u00e3o (
config.name.showDialogOnError = Mostrar di\u00e1logo de erros em cada erro
config.description.showDialogOnError = Exibe automaticamente a caixa de di\u00e1logo de erros em cada ocorr\u00eancia de erro
#after 14.4.0
config.name.limitSameChars = Limite dos mesmos caracteres para \\{xx}C (repeat) escape
config.description.limitSameChars = N\u00famero m\u00e1ximo de mesmos caracteres em uma linha em strings de P-code ou nomes ofuscados antes de substituir por \\{xx}C repeat escape
#after 14.5.2
config.name.showImportScriptsInfo = Mostrar informa\u00e7\u00f5es antes de importar scripts
config.description.showImportScriptsInfo = Exibe algumas informa\u00e7\u00f5es sobre como a importa\u00e7\u00e3o de scripts funciona ap\u00f3s clicar em Importar scripts no menu.
config.name.showImportTextInfo = Mostrar informa\u00e7\u00f5es antes de importar texto
config.description.showImportTextInfo = Exibe algumas informa\u00e7\u00f5es sobre como a importa\u00e7\u00e3o de texto funciona ap\u00f3s clicar em Importar texto no menu.
config.name.showImportSymbolClassInfo = Mostrar informa\u00e7\u00f5es antes de importar Symbol-Class
config.description.showImportSymbolClassInfo = Exibe algumas informa\u00e7\u00f5es sobre como a importa\u00e7\u00e3o de Symbol-Class funciona ap\u00f3s clicar em Importar Symbol-Class no menu.
config.name.showImportXmlInfo = Mostrar informa\u00e7\u00f5es antes de importar XML
config.description.showImportXmlInfo = Exibe algumas informa\u00e7\u00f5es sobre como a importa\u00e7\u00e3o de XML funciona ap\u00f3s clicar em Importar XML no menu.
#after 15.1.1
config.name.lastSessionTagListSelection = \u00daltima sele\u00e7\u00e3o na lista de tags da sess\u00e3o
config.description.lastSessionTagListSelection = Cont\u00e9m a sele\u00e7\u00e3o da \u00faltima sess\u00e3o na visualiza\u00e7\u00e3o da lista de sele\u00e7\u00e3o
config.name.lastView = \u00daltima visualiza\u00e7\u00e3o
config.description.lastView = \u00daltimo modo de visualiza\u00e7\u00e3o exibido
config.name.swfSpecificCustomConfigs = Configura\u00e7\u00f5es personalizadas espec\u00edficas para SWF
config.description.swfSpecificCustomConfigs = Cont\u00e9m as configura\u00e7\u00f5es espec\u00edficas para SWF em formato personalizado
config.name.warningOpeningReadOnly = Aviso ao abrir um SWF somente leitura
config.description.warningOpeningReadOnly = Mostrar aviso ao abrir um SWF de uma fonte somente leitura
# after 16.1.0
config.name.showImportImageInfo = Exibir informa\u00e7\u00f5es antes de importar imagens
config.description.showImportImageInfo = Exibe algumas informa\u00e7\u00f5es sobre como a importa\u00e7\u00e3o de imagens funciona ap\u00f3s clicar em "Importar imagens" no menu.
config.name.autoPlaySwfs = Reproduzir pr\u00e9vias de SWF automaticamente
config.description.autoPlaySwfs = Reproduzir automaticamente a pr\u00e9via do SWF ao selecionar o n\u00f3 do SWF
config.name.expandFirstLevelOfTreeOnLoad = Expandir o primeiro n\u00edvel da \u00e1rvore ao carregar o SWF
config.description.expandFirstLevelOfTreeOnLoad = Expande automaticamente o primeiro n\u00edvel de n\u00f3s na \u00e1rvore ao abrir o SWF.
# after 16.2.0
config.name.allowPlacingDefinesIntoSprites = Permitir colocar tags de defini\u00e7\u00e3o em DefineSprite
config.description.allowPlacingDefinesIntoSprites = Permite colocar (mover/copiar/arrastar para dentro) tags do tipo define em DefineSprite.
config.name.allowDragAndDropInTagListTree = Permitir arrastar e soltar na visualiza\u00e7\u00e3o da lista de tags
config.description.allowDragAndDropInTagListTree = Permite mover/copiar tags com arrastar e soltar na \u00e1rvore da visualiza\u00e7\u00e3o da lista de tags.
config.name.allowMiterClipLinestyle = (REMOVIDO) Permitir estilos de linha de clipe de esquadria (LENTO)
config.description.allowMiterClipLinestyle = Permite o uso de um renderizador personalizado que suporta estilos de linha de clipe de esquadria, por\u00e9m \u00e9 lento.
advancedSettings.search = Pesquisar:
# after 16.3.1
config.name.animateSubsprites = Animar subsprites na pr\u00e9-visualiza\u00e7\u00e3o
config.description.animateSubsprites = Permite anima\u00e7\u00e3o de subsprites na pr\u00e9-visualiza\u00e7\u00e3o da linha do tempo.
config.name.autoPlayPreviews = Reprodu\u00e7\u00e3o autom\u00e1tica de pr\u00e9vias
config.description.autoPlayPreviews = Reproduzir pr\u00e9vias automaticamente.
config.name.maxCachedTime = Tempo m\u00e1ximo de cache tempor\u00e1rio
config.description.maxCachedTime = Tempo m\u00e1ximo em milissegundos antes que um item (que n\u00e3o foi acessado desde ent\u00e3o) seja removido do cache. Defina isso como 0 para um armazenamento em cache ilimitado.
config.name.airLibLocation = 6) Caminho da biblioteca AIR (airglobal.swc)
config.description.airLibLocation = Localiza\u00e7\u00e3o da biblioteca AIR airglobal.swc. Pode ser usada principalmente para compila\u00e7\u00e3o em AS3.
config.name.showImportShapeInfo = Exibir informa\u00e7\u00f5es antes de importar formas
config.description.showImportShapeInfo = Exibe algumas informa\u00e7\u00f5es sobre como a importa\u00e7\u00e3o de formas funciona ap\u00f3s clicar em "Importar formas" no menu.
config.name.pinnedItemsTagTreePaths = Caminhos de itens fixados na \u00e1rvore de tags
config.description.pinnedItemsTagTreePaths = Caminhos dos n\u00f3s da \u00e1rvore de tags que est\u00e3o fixados.
config.name.pinnedItemsTagListPaths = Caminhos de itens fixados na visualiza\u00e7\u00e3o em lista da \u00e1rvore de tags
config.description.pinnedItemsTagListPaths = Caminhos dos n\u00f3s da visualiza\u00e7\u00e3o em lista da \u00e1rvore de tags que est\u00e3o fixados.
config.name.flattenASPackages = Agrupar pacotes ActionScript
config.description.flattenASPackages = Criar um item por pacote em vez de uma \u00e1rvore de pacotes.
config.name.gui.scale = Fator de escala da interface do usu\u00e1rio
config.description.gui.scale = Fator de escala da interface gr\u00e1fica. Defina isso como 2.0 em monitores Mac retina. \u00c9 necess\u00e1ria a sa\u00edda real do aplicativo (n\u00e3o apenas a reinicializa\u00e7\u00e3o ap\u00f3s perguntar).
config.name.warning.video.vlc = Avisar sobre a aus\u00eancia do VLC
config.description.warning.video.vlc = Exibe um aviso sobre a necessidade do VLC Media Player ao abrir SWFs com tags DefineVideoStream quando o VLC n\u00e3o est\u00e1 dispon\u00edvel.
config.name.playFrameSounds = Reproduzir sons de quadro
config.description.playFrameSounds = Reproduz sons ao exibir quadros.
config.name.fixAntialiasConflation = 
config.description.fixAntialiasConflation = 
config.name.autoPlaySounds = Reprodu\u00e7\u00e3o autom\u00e1tica de sons
config.description.autoPlaySounds = Reproduz automaticamente os sons (DefineSound) na sele\u00e7\u00e3o do n\u00f3 da \u00e1rvore.
config.name.deobfuscateAs12RemoveInvalidNamesAssignments=Desofusca\u00e7\u00e3o de AS1/2: Remover declara\u00e7\u00f5es de vari\u00e1veis com nomes obfuscados
config.description.deobfuscateAs12RemoveInvalidNamesAssignments=Durante a desofusca\u00e7\u00e3o de AS1/2, remova declara\u00e7\u00f5es de vari\u00e1veis que possuem nomes n\u00e3o padronizados. AVISO: Isso pode danificar SWFs que dependem de nomes obfuscados.
config.name.gui.splitPanePlace.dividerLocationPercent = (Interno) Local de coloca\u00e7\u00e3o do divisor
config.description.gui.splitPanePlace.dividerLocationPercent = 
config.name.gui.splitPaneTransform1.dividerLocationPercent=(Interno) Localiza\u00e7\u00e3o do divisor da transforma\u00e7\u00e3o1
config.description.gui.splitPaneTransform1.dividerLocationPercent=
config.name.gui.splitPaneTransform2.dividerLocationPercent=(Interno) Localiza\u00e7\u00e3o do divisor da transforma\u00e7\u00e3o2
config.description.gui.splitPaneTransform2.dividerLocationPercent=
config.name.gui.transform.lastExpandedCards = (Interno) \u00daltimos cart\u00f5es de transforma\u00e7\u00e3o expandidos
config.description.gui.transform.lastExpandedCards = 
config.name.doubleClickNodeToEdit = Duplo clique para come\u00e7ar a editar
config.description.doubleClickNodeToEdit = Ao clicar duas vezes no n\u00f3 da \u00e1rvore, inicia a edi\u00e7\u00e3o do mesmo.
config.name.warningDeobfuscation = Avisar ao mudar para a desofusca\u00e7\u00e3o
config.description.warningDeobfuscation = Exibe um aviso ao alternar a desofusca\u00e7\u00e3o ligada/desligada.
config.name.warningRenameIdentifiers = Avisar ao alternar a renomea\u00e7\u00e3o autom\u00e1tica de identificadores
config.description.warningRenameIdentifiers = Exibe um aviso ao ativar o recurso de renomea\u00e7\u00e3o autom\u00e1tica de identificadores.
config.name.showImportMovieInfo = Mostrar informa\u00e7\u00f5es antes de importar filmes
config.description.showImportMovieInfo = Exibe algumas informa\u00e7\u00f5es sobre como funciona a importa\u00e7\u00e3o de filmes ap\u00f3s clicar em "Importar filmes" no menu.
config.name.showImportSoundInfo = Mostrar informa\u00e7\u00f5es antes de importar sons
config.description.showImportSoundInfo = Exibe algumas informa\u00e7\u00f5es sobre como funciona a importa\u00e7\u00e3o de sons ap\u00f3s clicar em "Importar sons" no menu.
config.name.svgRetainBounds = Manter os limites da forma durante a exporta\u00e7\u00e3o SVG
config.description.svgRetainBounds = Durante a exporta\u00e7\u00e3o SVG, a posi\u00e7\u00e3o x, y da forma \u00e9 exportada exatamente como no SWF (por exemplo, positiva ou negativa).
config.name.disableBitmapSmoothing = Desativar suaviza\u00e7\u00e3o de bitmap
config.description.disableBitmapSmoothing = Desativa preenchimentos de bitmap suavizados durante a exibi\u00e7\u00e3o - mostra tudo como n\u00e3o suavizado (pixelado). Isto n\u00e3o se aplica a imagens exportadas.
config.name.pinnedItemsScrollPos = Posi\u00e7\u00f5es de rolagem/cursor de itens fixados
config.description.pinnedItemsScrollPos = Posi\u00e7\u00f5es de rolagem ou cursor de itens fixados.
config.name.maxRememberedScrollposItems = N\u00famero m\u00e1ximo de posi\u00e7\u00f5es de rolagem lembradas
config.description.maxRememberedScrollposItems = N\u00famero m\u00e1ximo de itens de posi\u00e7\u00e3o de rolagem lembrados.
config.name.rememberScriptsScrollPos = Lembre-se da posi\u00e7\u00e3o de rolagem/cursor dos scripts
config.description.rememberScriptsScrollPos = A posi\u00e7\u00e3o de rolagem/cursor do script \u00e9 mantida ao alternar itens e salva para itens fixados.
config.name.rememberFoldersScrollPos = Lembrar-se da posi\u00e7\u00e3o de rolagem das pastas
config.description.rememberFoldersScrollPos = A posi\u00e7\u00e3o de rolagem das pastas \u00e9 mantida ao alternar itens e salva para itens fixados.
#after 18.3.6
config.name.warning.initializers.class = Avisar sobre a edi\u00e7\u00e3o de caracter\u00edsticas da classe AS3 sobre o inicializador de script
config.description.warning.initializers.class = Aviso sobre a edi\u00e7\u00e3o do tra\u00e7o da classe AS3 relacionado ao inicializador
#after 18.4.1
config.name.maxCachedNum = N\u00famero m\u00e1ximo de itens armazenados por cache \u00fanico
config.description.maxCachedNum = N\u00famero m\u00e1ximo de itens em cache antes que itens mais antigos sejam removidos do cache. Valor menor = menos mem\u00f3ria, aplicativo mais lento. Valor maior = mais mem\u00f3ria, aplicativo mais r\u00e1pido. Defina como 0 para um cache ilimitado.
config.name.warning.cannotencrypt = Aviso quando n\u00e3o for poss\u00edvel salvar criptografado
config.description.warning.cannotencrypt = Exibe um aviso quando n\u00e3o for poss\u00edvel salvar arquivo SWF que foi criptografado usando a criptografia HARMAN Air.
#after 18.5.0
config.name.lastExportEnableEmbed = \u00daltima configura\u00e7\u00e3o de exporta\u00e7\u00e3o de ativos incorporados
config.description.lastExportEnableEmbed = \u00daltima configura\u00e7\u00e3o de exporta\u00e7\u00e3o de ativos incorporados via metadados [Embed].
config.name.lastFlaExportVersion = \u00daltima vers\u00e3o de exporta\u00e7\u00e3o FLA
config.description.lastFlaExportVersion = \u00daltima vers\u00e3o exportada do FLA
config.name.lastFlaExportCompressed = \u00daltima exporta\u00e7\u00e3o FLA comprimida
config.description.lastFlaExportCompressed = \u00daltima vers\u00e3o do FLA exportada e comprimida
#after 19.0.0
config.name.showImportSpriteInfo = Mostrar informa\u00e7\u00f5es antes de importar sprites
config.description.showImportSpriteInfo = Exibe algumas informa\u00e7\u00f5es sobre como a importa\u00e7\u00e3o de sprites funciona ap\u00f3s clicar em "Importar sprites" no menu.
config.name.displayAs12PCodeDocsPanel=Mostrar painel de documentos no AS1/2 P-code
config.description.displayAs12PCodeDocsPanel=Exibe um painel com documenta\u00e7\u00e3o de a\u00e7\u00f5es na edi\u00e7\u00e3o e exibi\u00e7\u00e3o de AS1/2 P-code 
config.name.gui.action.splitPane.docs.dividerLocationPercent = (Internal) AS 1/2 splitPanedocsdividerLocationPercent
config.description.action.avm2.splitPane.docs.dividerLocationPercent=Percentual de localiza\u00e7\u00e3o do divisor de documentos AS 1/2 splitPane
#after 19.1.2
config.name.rememberLastScreen = Lembrar a \u00faltima tela usada (em v\u00e1rios monitores)
config.description.rememberLastScreen = Lembra a \u00faltima tela usada em configura\u00e7\u00e3o com v\u00e1rios dispositivos de tela (monitores)
config.name.lastMainWindowScreenIndex = \u00cdndice da \u00faltima tela da janela principal
config.description.lastMainWindowScreenIndex = \u00cdndice da \u00faltima tela da janela principal
config.name.lastMainWindowScreenX = \u00daltima tela da janela principal X
config.description.lastMainWindowScreenX = \u00daltima coordenada Y da tela da janela principal
config.name.lastMainWindowScreenY = \u00daltima tela da janela principal Y
config.description.lastMainWindowScreenY = \u00daltima coordenada Y da tela da janela principal
config.name.lastMainWindowScreenWidth = Largura da tela da \u00faltima janela principal
config.description.lastMainWindowScreenWidth = Largura da tela da \u00faltima janela principal
config.name.lastMainWindowScreenHeight = Altura da tela da \u00faltima janela principal
config.description.lastMainWindowScreenHeight = Altura da tela da \u00faltima janela principal
config.name.displayAs12PCodePanel=Mostrar painel de AS1/2 P-code
config.description.displayAs12PCodePanel=Exibe um painel com a\u00e7\u00f5es de c\u00f3digo P desmontado para ActionScript 1 e 2
config.name.displayAs3PCodePanel=Mostrar painel de AS3 P-code
config.description.displayAs3PCodePanel=Exibe um painel com instru\u00e7\u00f5es de c\u00f3digo P desmontado para ActionScript 3
config.name.flaExportUseMappedFontLayout = Exportar FLA - usar layout de fonte mapeada
config.description.flaExportUseMappedFontLayout = Utiliza os valores de avan\u00e7o da fonte atribu\u00edda ao determinar o espa\u00e7amento entre letras quando a fonte real n\u00e3o possui layout durante a exporta\u00e7\u00e3o para FLA.
