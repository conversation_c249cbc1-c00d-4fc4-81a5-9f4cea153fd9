# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
titleFormat = %title%:
shapes = Shapes
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 Canvas
shapes.swf = SWF
texts = Texts
texts.plain = Plain text
texts.formatted = Formatted text
texts.svg = SVG
images = Images
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Movies
movies.flv = FLV (No audio)
sounds = Sounds
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (Audio only)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Scripts
scripts.as = ActionScript
scripts.pcode = P-code
scripts.pcode_hex=P-code with Hex
scripts.hex = Hex
scripts.constants = Constants
scripts.as_method_stubs=ActionScript method stubs
scripts.pcode_graphviz=P-code GraphViz
binaryData = Binary data
binaryData.raw = Raw
dialog.title = Export...
button.ok = OK
button.cancel = Cancel
morphshapes = Morphshapes
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 Canvas
morphshapes.swf = SWF
morphshapes.bmp_start_end=BMP (start, end)
morphshapes.png_start_end=PNG (start, end)
morphshapes.svg_start_end=SVG (start, end)
frames = Frames
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 Canvas
frames.pdf = PDF
frames.bmp = BMP
frames.swf = SWF
sprites = Sprites
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = HTML5 Canvas
sprites.pdf = PDF
sprites.bmp = BMP
sprites.swf = SWF
buttons = Buttons
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
buttons.swf = SWF
fonts = Fonts
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Zoom
zoom.percent = %
zoom.invalid = Invalid zoom value.
symbolclass = Symbol-Class mapping
symbolclass.csv = CSV
#after 18.0.0
images.png_gif_jpeg_alpha=PNG/GIF/JPEG+alpha
#after 18.5.0
fonts4=DefineFont4
fonts4.cff=CFF
embed = Export embedded assets via [Embed]
#after 20.1.0
resampleWav = Resample Wav to 44kHz
transparentFrameBackground = Ignore background color (make transparent)