# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
checkbox.ignorecase = Ignore case
checkbox.regexp = Regular expression
button.ok = OK
button.cancel = Cancel
label.searchtext = Search text:
label.replacementtext = Replacement text:
#dialog.title = ActionScript search
dialog.title = Text search
dialog.title.replace = Text replace
error = Error
error.invalidregexp = Invalid pattern
checkbox.searchText = Search in texts
checkbox.searchAS = Search in AS
checkbox.replaceInParameters = Replace in parameters
checkbox.searchPCode = Search in P-Code
#after 13.0.3
label.scope = Scope:
scope.currentFile = Current SWF
scope.selection = Selection (%selection%)
scope.allFiles = All opened SWFs
scope.selection.items = %count% items
#after 16.3.1
scope.currentFile.abc = Current ABC
