# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = AS vazba
button.ok = OK
button.proceed = Pokra\u010dovat
button.cancel = Storno
identifier = ActionScript identifik\u00e1tor:
classname = ActionScript2 t\u0159\u00edda (pln\u011b kvalifikovan\u00e1):
class.parentname = N\u00e1zev rodi\u010dovsk\u00e9 t\u0159\u00eddy (pln\u011b kvalifikovan\u00fd):
error.alreadyExistsClass = Chyba: Tato t\u0159\u00edda ji\u017e existuje
error.cannotRemoveIdentifierClassExists = Chyba: Nelze odstranit idenitifik\u00e1tor, t\u0159\u00edda ji\u017e existuje
linkage.notfound.exportAssets.where = Kam um\u00edstit data o vazb\u011b:
linkage.notfound.exportAssets.where.existing = Existuj\u00edc\u00ed ExportAssets tag
linkage.notfound.exportAssets.where.new = Nov\u00fd ExportAssets tag