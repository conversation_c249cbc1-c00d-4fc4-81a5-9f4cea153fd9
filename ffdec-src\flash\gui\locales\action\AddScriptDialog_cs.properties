framenum = Frame #:
dialog.title = P\u0159idat skript
button.ok = OK
button.cancel = Storno
type = Typ skriptu:
type.frame = Frame skript hlavn\u00ed timeliny (DoAction)
type.sprite.frame = Frame skript Spritu (DoAction)
type.sprite.init = Inicializa\u010dn\u00ed skript Spritu (DoInitAction)
type.button.event = Ud\u00e1lost tla\u010d\u00edtka (BUTTONCONDACTION)
type.instance.event = Ud\u00e1lost instance (CLIPACTIONRECORD)
type.class = AS2 T\u0159\u00edda
classname = Pln\u011b kvalifikovan\u00fd n\u00e1zev t\u0159\u00eddy
message.classexists = T\u0159\u00edda tohoto n\u00e1zvu ji\u017e existuje. Pros\u00edm zvolte jin\u00fd n\u00e1zev.
# In some cases we display only part of the dialog (like add class), in such case, dialog title is modified
dialog.title.combined = %title% - %type%