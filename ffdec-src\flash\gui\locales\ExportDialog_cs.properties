# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Tvary
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 Canvas
shapes.swf = SWF
texts = Texty
texts.plain = \u010cist\u00fd Text
texts.formatted = Form\u00e1tovan\u00fd text
texts.svg = SVG
images = Obr\u00e1zky
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Videa
movies.flv = FLV (bez zvuku)
sounds = Zvuky
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (pouze zvuk)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Skripty
scripts.as = ActionScript
scripts.pcode = P-k\u00f3d
scripts.pcode_hex=P-k\u00f3d s hex
scripts.hex = Hex
scripts.constants = Konstanty
scripts.as_method_stubs=Z\u00e1klady ActionScript metod
scripts.pcode_graphviz=P-k\u00f3d GraphViz
binaryData = Bin\u00e1rn\u00ed data
binaryData.raw = Raw
dialog.title = Exportovat...
button.ok = OK
button.cancel = Storno
morphshapes = Morphshapes
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 Canvas
morphshapes.swf = SWF
morphshapes.bmp_start_end=BMP (za\u010d\u00e1tek, konec)
morphshapes.png_start_end=PNG (za\u010d\u00e1tek, konec)
morphshapes.svg_start_end=SVG (za\u010d\u00e1tek, konec)
frames = Sn\u00edmky
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 Canvas
frames.pdf = PDF
frames.bmp = BMP
frames.swf = SWF
sprites = Sprity
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = HTML5 Canvas
sprites.pdf = PDF
sprites.bmp = BMP
sprites.swf = SWF
buttons = Tla\u010d\u00edtka
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
buttons.swf = SWF
fonts = P\u00edsma
fonts.ttf = TTF
fonts.woff = WOFF
zoom = P\u0159ibl\u00ed\u017een\u00ed
zoom.percent = %
zoom.invalid = Neplatn\u00e1 hodnota velikost.
symbolclass = Mapov\u00e1n\u00ed symbol\u016f na t\u0159\u00eddy
symbolclass.csv = CSV
#after 18.0.0
images.png_gif_jpeg_alpha=PNG/GIF/JPEG+alpha
#after 18.5.0
fonts4=DefineFont4
fonts4.cff=CFF
embed = Exportovat vlo\u017een\u00e9 zdroje skrze [Embed]
#after 20.1.0
resampleWav = P\u0159evzorkovat Wav na 44kHz
transparentFrameBackground = Ignorovat barvu pozad\u00ed (pou\u017e\u00edt pr\u016fhlednou)