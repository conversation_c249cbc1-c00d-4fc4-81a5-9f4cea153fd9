# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Formes
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = Tela HTML5
texts = Texts
texts.plain = Text planer
texts.formatted = Text formatat
texts.svg = SVG
images = Imatges
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Pel\u00b7l\u00edcules
movies.flv = FLV (sense \u00e0udio)
sounds = Sons
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (nom\u00e9s \u00e0udio)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Scripts
scripts.as = ActionScript
scripts.pcode = Codi P
scripts.pcode_hex=Codi P amb Hex
scripts.hex = Hex
scripts.constants = Constants
scripts.as_method_stubs=Stubs de m\u00e8tode ActionScript
binaryData = Dades bin\u00e0ries
binaryData.raw = Cru
dialog.title = Exporta...
button.ok = B\u00e9
button.cancel = Cancel\u00b7la
morphshapes = Morphshapes
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = Tela HTML5
frames = Marcs
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = Tela HTML5
frames.pdf = PDF
frames.bmp = BMP
sprites = Sprites
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = Tela HTML5
sprites.pdf = PDF
sprites.bmp = BMP
buttons = Buttons
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
fonts = Tipografies
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Zoom
zoom.percent = %
zoom.invalid = Valor de zoom inv\u00e0lid.
symbolclass = Mapejat S\u00edmbol-Classe
symbolclass.csv = CSV
