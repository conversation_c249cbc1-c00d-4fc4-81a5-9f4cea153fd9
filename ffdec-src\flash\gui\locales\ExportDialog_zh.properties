# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = \u5f62\u72b6
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 \u753b\u5e03
shapes.swf = SWF
texts = \u6587\u672c
texts.plain = \u7eaf\u6587\u672c
texts.formatted = \u683c\u5f0f\u5316\u7684\u6587\u672c
texts.svg = SVG
images = \u56fe\u7247
images.png_gif_jpeg = PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = \u5f71\u7247
movies.flv = FLV (\u65e0\u97f3\u9891)
sounds = \u58f0\u97f3
sounds.mp3_wav_flv = MP3/WAV/FLV
sounds.flv = FLV (\u4ec5\u97f3\u9891)
sounds.mp3_wav = MP3/WAV
sounds.wav = WAV
scripts = \u811a\u672c
scripts.as = ActionScript
scripts.pcode = P-code
scripts.pcode_hex = \u5341\u516d\u8fdb\u5236 P-code
scripts.hex = \u5341\u516d\u8fdb\u5236
scripts.constants = \u5e38\u91cf
scripts.as_method_stubs = ActionScript\u65b9\u6cd5\u5b58\u6839
scripts.pcode_graphviz = P-code\u53ef\u89c6\u5316
binaryData = \u4e8c\u8fdb\u5236\u6570\u636e
binaryData.raw = \u539f\u59cb
dialog.title = \u5bfc\u51fa...
button.ok = \u786e\u5b9a
button.cancel = \u53d6\u6d88
morphshapes = \u53d8\u5f62\u5f62\u72b6
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 \u753b\u5e03
morphshapes.swf = SWF
morphshapes.bmp_start_end = BMP (\u5f00\u59cb, \u7ed3\u675f)
morphshapes.png_start_end = PNG (\u5f00\u59cb, \u7ed3\u675f)
morphshapes.svg_start_end = SVG (\u5f00\u59cb, \u7ed3\u675f)
frames = \u5e27
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 \u753b\u5e03
frames.pdf = PDF
frames.bmp = BMP
frames.swf = SWF
sprites = \u7cbe\u7075
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = HTML5 \u753b\u5e03
sprites.pdf = PDF
sprites.bmp = BMP
sprites.swf = SWF
buttons = \u6309\u94ae
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
buttons.swf = SWF
fonts = \u5b57\u4f53
fonts.ttf = TTF
fonts.woff = WOFF
zoom = \u7f29\u653e
zoom.percent = %
zoom.invalid = \u65e0\u6548\u7f29\u653e\u503c\u3002
symbolclass = \u7b26\u53f7\u7c7b\u6620\u5c04
symbolclass.csv = CSV
#after 18.0.0
images.png_gif_jpeg_alpha = PNG/GIF/JPEG+alpha
#after 18.5.0
fonts4 = \u5b9a\u4e49\u5b57\u4f534
fonts4.cff = CFF
embed = \u901a\u8fc7 [Embed] \u6807\u7b7e\u5bfc\u51fa\u5d4c\u5165\u7684\u8d44\u6e90
#after 20.1.0
resampleWav = Wav \u91c7\u6837\u5230 44kHz
transparentFrameBackground = \u5ffd\u7565\u80cc\u666f\u989c\u8272\uff08\u4f7f\u900f\u660e\uff09
