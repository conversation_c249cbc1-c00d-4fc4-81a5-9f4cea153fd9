# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = T\u00fcm s\u0131n\u0131flar\u0131 i\u015fle
dialog.title = PCod kod gizleme
deobfuscation.level = Kod gizleme d\u00fczeyi:
deobfuscation.removedeadcode = \u00d6l\u00fc kodu kald\u0131r
deobfuscation.removetraps = Tuzaklar\u0131 kald\u0131r
deobfuscation.restorecontrolflow = Kontrol ak\u0131\u015f\u0131n\u0131 geri y\u00fckle
button.ok = TAMAM
button.cancel = \u0130ptal
deobfuscation.scope = Kapsam:
deobfuscation.scope.method = Ge\u00e7erli y\u00f6ntem
deobfuscation.scope.script = Ge\u00e7erli komut dosyas\u0131
deobfuscation.scope.swf = B\u00fct\u00fcn SWF
warning.modify = UYARI: Bu eylem, SWF dosyas\u0131n\u0131 de\u011fi\u015ftirir.\r\nG\u00f6r\u00fcnt\u00fcleme i\u00e7in yaln\u0131zca kod gizleme istiyorsan\u0131z, Ayarlarda\r\n"Otomatik kod gizleme" se\u00e7ene\u011fini veya komut dosyas\u0131 d\u00fczenleyicinin \u00fczerindeki k\u00fc\u00e7\u00fck hap simgesini kullan\u0131n\r\n.
