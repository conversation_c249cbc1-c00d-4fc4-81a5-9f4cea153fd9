# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Process all classes
dialog.title = PCode deobfuscation
deobfuscation.level = Code deobfuscation level:
deobfuscation.removedeadcode = Remove dead code
deobfuscation.removetraps = Remove traps
deobfuscation.restorecontrolflow = Restore control flow
button.ok = OK
button.cancel = Cancel
deobfuscation.scope = Scope:
deobfuscation.scope.method = Current method
deobfuscation.scope.script = Current script
deobfuscation.scope.swf = Whole SWF
warning.modify = WARNING: This action will modify the SWF file.\r\nIf you want only deobfuscation for display, then use\r\n"Automatic deobfuscation" option in Settings\r\nor the small pill icon above the script editor.
