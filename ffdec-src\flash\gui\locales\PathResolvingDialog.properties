# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = Path resolving
info = Set directories where assets will be searched when not found in SWFs path.\r\n\
    You can use pipe "|" to separate prefix that the path must have, like "data:|C:\\MyData\\Dir"\
    when you have paths starting with "data:" prefix.\r\nOne path per line. This is currently used only in GFX tags.
button.ok = OK
button.cancel = Cancel