# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%total%-b\u0151l %available% karakter)
dialog.title = Bet\u0171t\u00edpus be\u00e1gyaz\u00e1sa
label.individual = Egyedi karakterek:
button.loadfont = Bet\u0171t\u00edpus bet\u00f6lt\u00e9se a lemezr\u0151l...
filter.ttf = True Type Bet\u0171t\u00edpus f\u00e1jlok (*.ttf)
error.invalidfontfile = \u00c9rv\u00e9nytelen bet\u0171t\u00edpus f\u00e1jl
error.cannotreadfontfile = Bet\u0171t\u00edpus f\u00e1jl nem olvashat\u00f3
installed = Telep\u00edtve: 
ttffile.noselection = TTF f\u00e1jl: <select>
ttffile.selection = TTF f\u00e1jl: %fontname% (%filename%)
allcharacters = Minden karakter (%available% characters)
