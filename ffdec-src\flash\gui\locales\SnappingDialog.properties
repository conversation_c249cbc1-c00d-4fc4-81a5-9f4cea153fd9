# Copyright (C) 2025 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

dialog.title = Snapping
button.ok = OK
button.cancel = Cancel

snapAlign = Snap Align
snapToGrid = Snap to Grid
snapToGuides = Snap to Guides
snapToPixels = Snap to Pixels
snapToObjects = Snap to Objects

snapAlign.settings = Snap align settings
snapAlign.stageBorder = Stage border:
snapAlign.objectSpacing = Object spacing:
snapAlign.objectSpacing.horizontal = Horizontal:
snapAlign.objectSpacing.vertical = Vertical:
snapAlign.centerAlignment = Center alignment:
snapAlign.centerAlignment.horizontal = Horizontal center alignment
snapAlign.centerAlignment.vertical = Vertical center alignment

error.invalidSpacing = Invalid spacing value. Integer expected.
error.invalidBorder = Invalid border value. Integer expected.