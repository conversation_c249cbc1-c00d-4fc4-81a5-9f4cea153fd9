# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = \u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u044b\u0435 \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438
advancedSettings.restartConfirmation = \u0414\u043b\u044f \u043f\u0440\u0438\u043c\u0435\u043d\u0435\u043d\u0438\u044f \u043d\u0435\u043a\u043e\u0442\u043e\u0440\u044b\u0445 \u043d\u0430\u0441\u0442\u0440\u043e\u0435\u043a \u043d\u0435\u043e\u0431\u0445\u043e\u0434\u0438\u043c\u043e \u043f\u0435\u0440\u0435\u0437\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0435. \u041f\u0435\u0440\u0435\u0437\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u0441\u0435\u0439\u0447\u0430\u0441?
advancedSettings.columns.name = \u0418\u043c\u044f
advancedSettings.columns.value = \u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435
advancedSettings.columns.description = \u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435
default = \u043f\u043e \u0443\u043c\u043e\u043b\u0447\u0430\u043d\u0438\u044e
config.group.name.export = \u042d\u043a\u0441\u043f\u043e\u0440\u0442
config.group.description.export = \u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430
config.group.name.script = \u0421\u043a\u0440\u0438\u043f\u0442\u044b
config.group.description.script = \u0412\u0441\u0435, \u0447\u0442\u043e \u0441\u0432\u044f\u0437\u0430\u043d\u043e \u0441 \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u0435\u0439 ActionScript
config.group.name.update = \u041e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u044f
config.group.description.update = \u041f\u0440\u043e\u0432\u0435\u0440\u043a\u0430 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0439
config.group.name.format = \u0424\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435
config.group.description.format = \u0424\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 ActionScript \u043a\u043e\u0434\u0430
config.group.name.limit = \u041e\u0433\u0440\u0430\u043d\u0438\u0447\u0435\u043d\u0438\u044f
config.group.description.limit = \u0422\u0430\u0439\u043c-\u0430\u0443\u0442 \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u0438 \u043e\u0431\u0444\u0443\u0441\u0446\u0438\u0440\u043e\u0432\u0430\u043d\u043d\u043e\u0433\u043e \u043a\u043e\u0434\u0430, \u0438 \u0442.\u0434.
config.group.name.ui = \u0418\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441
config.group.description.ui = \u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044c\u0441\u043a\u043e\u0433\u043e \u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441\u0430
config.group.name.debug = \u041e\u0442\u043b\u0430\u0434\u043a\u0430
config.group.description.debug = \u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u043e\u0442\u043b\u0430\u0434\u043a\u0438
config.group.name.display = \u041e\u0442\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435
config.group.description.display = \u041e\u0442\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435 flash \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432 \u0438 \u0442.\u0434.
config.group.name.decompilation = \u0414\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u044f
config.group.description.decompilation = \u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438, \u0441\u0432\u044f\u0437\u0430\u043d\u043d\u044b\u0435 \u0441 \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u0435\u0439 \u043a\u043e\u0434\u0430
config.group.name.other = \u0420\u0430\u0437\u043d\u043e\u0435
config.group.description.other = \u0420\u0430\u0437\u043d\u044b\u0435 \u043d\u0435\u043a\u043b\u0430\u0441\u0441\u0438\u0444\u0438\u0446\u0438\u0440\u043e\u0432\u0430\u043d\u043d\u044b\u0435 \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438
config.name.openMultipleFiles = \u041e\u0442\u043a\u0440\u044b\u0432\u0430\u0442\u044c \u043d\u0435\u0441\u043a\u043e\u043b\u044c\u043a\u043e \u0444\u0430\u0439\u043b\u043e\u0432
config.description.openMultipleFiles = \u041f\u043e\u0437\u0432\u043e\u043b\u044f\u0435\u0442 \u043e\u0442\u043a\u0440\u044b\u0432\u0430\u0442\u044c \u043d\u0435\u0441\u043a\u043e\u043b\u044c\u043a\u043e \u0444\u0430\u0439\u043b\u043e\u0432 \u0432 \u043e\u0434\u043d\u043e\u043c \u043e\u043a\u043d\u0435
config.name.decompile = \u041e\u0442\u043e\u0431\u0440\u0430\u0436\u0430\u0442\u044c \u0438\u0441\u0445\u043e\u0434\u043d\u044b\u0439 \u043a\u043e\u0434 ActionScript
config.description.decompile = \u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043e\u0442\u043a\u043b\u044e\u0447\u0438\u0442\u044c \u043e\u0442\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435 \u0438\u0441\u0445\u043e\u0434\u043d\u043e\u0433\u043e \u043a\u043e\u0434\u0430, \u043e\u0441\u0442\u0430\u0432\u0438\u0432 \u0442\u043e\u043b\u044c\u043a\u043e P-code
config.name.dumpView = \u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u043e\u0431\u0440\u0430\u0437\u0430
config.description.dumpView = \u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u044b\u0440\u043e\u0433\u043e \u043e\u0431\u0440\u0430\u0437\u0430 \u0434\u0430\u043d\u043d\u044b\u0445
config.name.useHexColorFormat = Hex \u0444\u043e\u0440\u043c\u0430\u0442 \u0446\u0432\u0435\u0442\u0430
config.description.useHexColorFormat = \u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0446\u0432\u0435\u0442\u0430 \u0432 hex \u0444\u043e\u0440\u043c\u0430\u0442\u0435
config.name.parallelSpeedUp = Parallel SpeedUp
config.description.parallelSpeedUp = \u0420\u0430\u0441\u043f\u0430\u0440\u0430\u043b\u043b\u0435\u043b\u0438\u0432\u0430\u043d\u0438\u0435 \u0432\u044b\u0447\u0438\u0441\u043b\u0435\u043d\u0438\u0439 \u043f\u0440\u0438 \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u0438 \u043c\u043e\u0436\u0435\u0442 \u0443\u0432\u0435\u043b\u0438\u0447\u0438\u0442\u044c \u0435\u0435 \u043f\u0440\u043e\u0438\u0437\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u044c\u043d\u043e\u0441\u0442\u044c
config.name.parallelSpeedUpThreadCount = \u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u043f\u043e\u0442\u043e\u043a\u043e\u0432 (0 = auto)
config.description.parallelSpeedUpThreadCount = \u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u043f\u043e\u0442\u043e\u043a\u043e\u0432 \u0434\u043b\u044f \u0440\u0430\u0441\u043f\u0430\u0440\u0430\u043b\u043b\u0435\u043b\u0438\u0432\u0430\u043d\u0438\u044f. 0 = processor count - 1.
config.name.autoDeobfuscate = \u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0430\u044f \u0434\u0435\u043e\u0431\u0444\u0443\u0441\u043a\u0430\u0446\u0438\u044f
config.description.autoDeobfuscate = \u0414\u0435\u043e\u0431\u0444\u0443\u0441\u0446\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043a\u043e\u0434 \u043f\u0435\u0440\u0435\u0434 \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u0435\u0439
config.name.cacheOnDisk = \u041a\u044d\u0448\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043d\u0430 \u0434\u0438\u0441\u043a
config.description.cacheOnDisk = \u041a\u044d\u0448\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u0438\u0440\u043e\u0432\u0430\u043d\u043d\u044b\u0435 \u0443\u0447\u0430\u0441\u0442\u043a\u0438 \u043a\u043e\u0434\u0430 \u043d\u0430 \u0434\u0438\u0441\u043a, \u0432\u043c\u0435\u0441\u0442\u043e RAM
config.name.internalFlashViewer = \u0421\u0432\u043e\u0439 Flash \u043f\u0440\u043e\u0438\u0433\u0440\u044b\u0432\u0430\u0442\u0435\u043b\u044c
config.description.internalFlashViewer = \u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c JPEXS Flash Viewer \u0432\u043c\u0435\u0441\u0442\u043e \u0441\u0442\u0430\u043d\u0434\u0430\u0440\u0442\u043d\u043e\u0433\u043e Flash Player \u0434\u043b\u044f \u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u044d\u043b\u0435\u043c\u0435\u043d\u0442\u043e\u0432 SWF \u0444\u0430\u0439\u043b\u0430
config.name.gotoMainClassOnStartup = \u0412\u044b\u0434\u0435\u043b\u044f\u0442\u044c \u043e\u0441\u043d\u043e\u0432\u043d\u043e\u0439 \u043a\u043b\u0430\u0441\u0441 \u043f\u043e\u0441\u043b\u0435 \u043e\u0442\u043a\u0440\u044b\u0442\u0438\u044f \u0444\u0430\u0439\u043b\u0430 (AS3)
config.description.gotoMainClassOnStartup = \u041f\u0435\u0440\u0435\u0445\u043e\u0434\u0438\u0442 \u043a AS3 \u043a\u043b\u0430\u0441\u0441\u0443 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 SWF \u0444\u0430\u0439\u043b\u0430 \u043f\u043e\u0441\u043b\u0435 \u0435\u0433\u043e \u043e\u0442\u043a\u0440\u044b\u0442\u0438\u044f
config.name.autoRenameIdentifiers = \u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438 \u043f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u044b\u0432\u0430\u0442\u044c \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440\u044b
config.description.autoRenameIdentifiers = \u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438 \u043f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u044b\u0432\u0430\u0442\u044c \u043d\u0435 \u0432\u0430\u043b\u0438\u0434\u043d\u044b\u0435 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440\u044b \u043f\u0440\u0438 \u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0435 SWF
config.name.offeredAssociation = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u0440\u0435\u0434\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0441\u0432\u044f\u0437\u0430\u0442\u044c \u0441 SWF \u0443\u0436\u0435 \u043f\u043e\u043a\u0430\u0437\u0430\u043d\u043e
config.description.offeredAssociation = \u0414\u0438\u0430\u043b\u043e\u0433 \u0441 \u043f\u0440\u0435\u0434\u043b\u043e\u0436\u0435\u043d\u0438\u0435\u043c \u0441\u0432\u044f\u0437\u0430\u0442\u044c \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0442\u043e\u0440 \u0441 SWF \u0444\u0430\u0439\u043b\u0430\u043c\u0438 \u0443\u0436\u0435 \u0431\u044b\u043b \u043f\u043e\u043a\u0430\u0437\u0430\u043d
config.name.decimalAddress = \u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0434\u0435\u0441\u044f\u0442\u0438\u0447\u043d\u044b\u0435 \u0430\u0434\u0440\u0435\u0441\u0430
config.description.decimalAddress = \u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0434\u0435\u0441\u044f\u0442\u0438\u0447\u043d\u044b\u0435 \u0430\u0434\u0440\u0435\u0441\u0430 \u0432\u043c\u0435\u0441\u0442\u043e \u0448\u0435\u0441\u0442\u043d\u0430\u0434\u0446\u0430\u0442\u0435\u0440\u0438\u0447\u043d\u044b\u0445
config.name.showAllAddresses = \u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0432\u0441\u0435 \u0430\u0434\u0440\u0435\u0441\u0430
config.description.showAllAddresses = \u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0430\u0434\u0440\u0435\u0441\u0430 \u0432\u0441\u0435\u0445 ActionScript \u0438\u043d\u0441\u0442\u0440\u0443\u043a\u0446\u0438\u0439
config.name.useFrameCache = \u041a\u044d\u0448\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043a\u0430\u0434\u0440\u044b
config.description.useFrameCache = \u041a\u044d\u0448\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u043a\u0430\u0434\u0440\u044b \u043f\u0435\u0440\u0435\u0434 \u043f\u043e\u0432\u0442\u043e\u0440\u043d\u044b\u043c \u043e\u0442\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435\u043c
config.name.useRibbonInterface = Ribbon \u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441
config.description.useRibbonInterface = \u041e\u0442\u043a\u043b\u044e\u0447\u0438\u0442\u0435 \u0434\u043b\u044f \u0432\u043e\u0437\u0432\u0440\u0430\u0442\u0430 \u043a \u043a\u043b\u0430\u0441\u0441\u0438\u0447\u0435\u0441\u043a\u043e\u043c\u0443 \u0438\u043d\u0442\u0435\u0440\u0444\u0435\u0439\u0441\u0443
config.name.openFolderAfterFlaExport = \u041e\u0442\u043a\u0440\u044b\u0432\u0430\u0442\u044c \u043f\u0430\u043f\u043a\u0443 \u043f\u043e\u0441\u043b\u0435 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430 FLA
config.description.openFolderAfterFlaExport = \u041e\u0442\u043a\u0440\u044b\u0432\u0430\u0442\u044c \u0446\u0435\u043b\u0435\u0432\u0443\u044e \u043f\u0430\u043f\u043a\u0443, \u0432 \u043a\u043e\u0442\u043e\u0440\u0443\u044e \u0431\u044b\u043b \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u0430\u043d FLA \u0444\u0430\u0439\u043b
config.name.useDetailedLogging = \u041f\u043e\u0434\u0440\u043e\u0431\u043d\u044b\u0439 \u043b\u043e\u0433
config.description.useDetailedLogging = \u041f\u0438\u0441\u0430\u0442\u044c \u0432 \u043b\u043e\u0433 \u043f\u043e\u0434\u0440\u043e\u0431\u043d\u044b\u0435 \u0434\u0430\u043d\u043d\u044b\u0435 \u043e \u043f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0435\u043d\u0438\u044f\u0445 \u0438 \u043e\u0448\u0438\u0431\u043a\u0430\u0445, \u0434\u043b\u044f \u043e\u0442\u043b\u0430\u0434\u043e\u0447\u043d\u044b\u0445 \u0446\u0435\u043b\u0435\u0439
config.name.resolveConstants = \u0420\u0435\u0430\u043b\u044c\u043d\u044b\u0435 \u043a\u043e\u043d\u0441\u0442\u0430\u043d\u0442\u044b \u0432 AS1/2 P-code
config.description.resolveConstants = \u041e\u0442\u043a\u043b\u044e\u0447\u0438\u0442\u0435, \u0447\u0442\u043e\u0431\u044b \u0432\u043c\u0435\u0441\u0442\u043e \u0440\u0435\u0430\u043b\u044c\u043d\u044b\u0445 \u043a\u043e\u043d\u0441\u0442\u0430\u043d\u0442 \u043e\u0442\u043e\u0431\u0440\u0430\u0436\u0430\u0442\u044c 'constantxx' \u0432 \u043e\u043a\u043d\u0435 P-code
config.name.sublimiter = \u041b\u0438\u043c\u0438\u0442 \u0437\u0430\u043c\u0435\u043d \u0432 \u043a\u043e\u0434\u0435
config.description.sublimiter = \u041b\u0438\u043c\u0438\u0442 \u0437\u0430\u043c\u0435\u043d \u0432 \u043a\u043e\u0434\u0435 \u0434\u043b\u044f \u043e\u0431\u0444\u0443\u0441\u0446\u0438\u0440\u043e\u0432\u0430\u043d\u043d\u043e\u0433\u043e \u043a\u043e\u0434\u0430.
config.name.exportTimeout = \u0422\u0430\u0439\u043c-\u0430\u0443\u0442 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430 \u043a\u043e\u0434\u0430 (\u0441\u0435\u043a.)
config.description.exportTimeout = \u0414\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0442\u043e\u0440 \u043e\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442 \u044d\u043a\u0441\u043f\u043e\u0440\u0442 \u043f\u043e \u0438\u0441\u0442\u0435\u0447\u0435\u043d\u0438\u0438 \u044d\u0442\u043e\u0433\u043e \u0432\u0440\u0435\u043c\u0435\u043d\u0438
config.name.decompilationTimeoutFile = \u0422\u0430\u0439\u043c-\u0430\u0443\u0442 \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u0438 \u043e\u0434\u043d\u043e\u0433\u043e \u0444\u0430\u0439\u043b\u0430 (\u0441\u0435\u043a.)
config.description.decompilationTimeoutFile = \u0414\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u044f ActionScript \u043a\u043e\u0434\u0430 \u0431\u0443\u0434\u0435\u0442 \u043e\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0430 \u043f\u043e \u0438\u0441\u0442\u0435\u0447\u0435\u043d\u0438\u0438 \u0443\u043a\u0430\u0437\u0430\u043d\u043d\u043e\u0433\u043e \u0432\u0440\u0435\u043c\u0435\u043d\u0438 \u0434\u043b\u044f \u043e\u0434\u043d\u043e\u0433\u043e \u0444\u0430\u0439\u043b\u0430
config.name.paramNamesEnable = \u0412\u043a\u043b\u044e\u0447\u0438\u0442\u044c \u0438\u043c\u0435\u043d\u0430 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u043e\u0432 \u0432 AS3
config.description.paramNamesEnable = \u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u0435 \u0438\u043c\u0435\u043d \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u043e\u0432 \u043c\u043e\u0436\u0435\u0442 \u0432\u044b\u0437\u044b\u0432\u0430\u0442\u044c \u043f\u0440\u043e\u0431\u043b\u0435\u043c\u044b, \u0442.\u043a. \u043e\u0444\u0438\u0446\u0438\u0430\u043b\u044c\u043d\u044b\u0435 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b, \u043d\u0430\u043f\u0440. Flash CS 5.5, \u0443\u043a\u0430\u0437\u044b\u0432\u0430\u044e\u0442 \u043d\u0435\u0432\u0435\u0440\u043d\u044b\u0435 \u0438\u043d\u0434\u0435\u043a\u0441\u044b \u0438\u043c\u0435\u043d \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u043e\u0432
config.name.displayFileName = \u041e\u0442\u043e\u0431\u0440\u0430\u0436\u0430\u0442\u044c \u0438\u043c\u044f SWF \u0444\u0430\u0439\u043b\u0430 \u0432 \u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043a\u0435
config.description.displayFileName = \u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u0438\u043c\u044f \u0444\u0430\u0439\u043b\u0430 \u0438\u043b\u0438 url \u043e\u0442\u043a\u0440\u044b\u0442\u043e\u0433\u043e SWF \u0432 \u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043a\u0435 \u043e\u043a\u043d\u0430 \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u044b
config.name.dumpTags = \u041f\u0438\u0441\u0430\u0442\u044c \u0442\u044d\u0433\u0438 \u0432 \u043a\u043e\u043d\u0441\u043e\u043b\u044c
config.description.dumpTags = \u0421\u0431\u0440\u0430\u0441\u044b\u0432\u0430\u0442\u044c \u0442\u044d\u0433\u0438 \u0432 \u043a\u043e\u043d\u0441\u043e\u043b\u044c \u043f\u0440\u0438 \u0447\u0442\u0435\u043d\u0438\u0438 SWF
config.name.decompilationTimeoutSingleMethod = \u0422\u0430\u0439\u043c-\u0430\u0443\u0442 \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u0438 \u043e\u0434\u043d\u043e\u0433\u043e \u043c\u0435\u0442\u043e\u0434\u0430 AS3 (\u0441\u0435\u043a.)
config.description.decompilationTimeoutSingleMethod = \u0414\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0446\u0438\u044f \u0431\u0443\u0434\u0435\u0442 \u043e\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0430 \u043f\u043e \u0438\u0441\u0442\u0435\u0447\u0435\u043d\u0438\u0438 \u0443\u043a\u0430\u0437\u0430\u043d\u043d\u043e\u0433\u043e \u0432\u0440\u0435\u043c\u0435\u043d\u0438 \u043f\u0440\u0438 \u043e\u0431\u0440\u0430\u0431\u043e\u0442\u043a\u0435 \u043e\u0434\u043d\u043e\u0433\u043e \u043c\u0435\u0442\u043e\u0434\u0430 ActionScript
config.name.lastRenameType = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0439 \u0442\u0438\u043f \u043f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u044f
config.description.lastRenameType = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0439 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u043d\u044b\u0439 \u0442\u0438\u043f \u043f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u044f
config.name.lastSaveDir = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u043f\u0430\u043f\u043a\u0430 \u0434\u043b\u044f \u0441\u043e\u0445\u0440\u0430\u043d\u0435\u043d\u0438\u044f
config.description.lastSaveDir = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u043d\u0430\u044f \u043f\u0430\u043f\u043a\u0430 \u0434\u043b\u044f \u0441\u043e\u0445\u0440\u0430\u043d\u0435\u043d\u0438\u044f
config.name.lastOpenDir = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u043f\u0430\u043f\u043a\u0430 \u043e\u0442\u043a\u0440\u044b\u0442\u0438\u044f
config.description.lastOpenDir = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u043d\u0430\u044f \u043f\u0430\u043f\u043a\u0430 \u043e\u0442\u043a\u0440\u044b\u0442\u0438\u044f
config.name.lastExportDir = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u043f\u0430\u043f\u043a\u0430 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430
config.description.lastExportDir = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u043d\u0430\u044f \u043f\u0430\u043f\u043a\u0430 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430
config.name.locale = \u042f\u0437\u044b\u043a
config.description.locale = \u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 \u043b\u043e\u043a\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u0438
config.name.registerNameFormat = \u0424\u043e\u0440\u043c\u0430\u0442 \u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u044f \u043f\u0435\u0440\u0435\u043c\u0435\u043d\u043d\u044b\u0445
config.description.registerNameFormat = \u0424\u043e\u0440\u043c\u0430\u0442 \u0438\u043c\u0435\u043d \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u044b\u0445 \u043f\u0435\u0440\u0435\u043c\u0435\u043d\u043d\u044b\u0445. \u0418\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u0439\u0442\u0435 %d \u0434\u043b\u044f \u043d\u043e\u043c\u0435\u0440\u0430 \u043f\u0435\u0440\u0435\u043c\u0435\u043d\u043d\u043e\u0439
config.name.maxRecentFileCount = \u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u044c\u043d\u043e\u0435 \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0445 \u0444\u0430\u0439\u043b\u043e\u0432
config.description.maxRecentFileCount = \u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u044c\u043d\u043e\u0435 \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0445 \u0444\u0430\u0439\u043b\u043e\u0432
config.name.recentFiles = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0435 \u0444\u0430\u0439\u043b\u044b
config.description.recentFiles = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0435 \u043e\u0442\u043a\u0440\u044b\u0442\u044b\u0435 \u0444\u0430\u0439\u043b\u044b
config.name.fontPairingMap = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u0430\u0440\u044b \u0448\u0440\u0438\u0444\u0442\u043e\u0432 \u0434\u043b\u044f \u0438\u043c\u043f\u043e\u0440\u0442\u0430
config.description.fontPairingMap = \u041f\u0430\u0440\u044b \u0448\u0440\u0438\u0444\u0442\u043e\u0432 \u0434\u043b\u044f \u0438\u043c\u043f\u043e\u0440\u0442\u0430 \u043d\u043e\u0432\u044b\u0445 \u0441\u0438\u043c\u0432\u043e\u043b\u043e\u0432
config.name.lastUpdatesCheckDate = (\u0412\u043d\u0443\u0442\u0440.) \u0414\u0430\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u044f
config.description.lastUpdatesCheckDate = \u0414\u0430\u0442\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u044f \u043d\u0430 \u0441\u0435\u0440\u0432\u0435\u0440\u0435
config.name.gui.window.width = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0448\u0438\u0440\u0438\u043d\u0430 \u043e\u043a\u043d\u0430
config.description.gui.window.width = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0448\u0438\u0440\u0438\u043d\u0430 \u043e\u043a\u043d\u0430
config.name.gui.window.height = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0432\u044b\u0441\u043e\u0442\u0430 \u043e\u043a\u043d\u0430
config.description.gui.window.height = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0432\u044b\u0441\u043e\u0442\u0430 \u043e\u043a\u043d\u0430
config.name.gui.window.maximized.horizontal = (\u0412\u043d\u0443\u0442\u0440.) \u041e\u043a\u043d\u043e \u0440\u0430\u0437\u0432\u0435\u0440\u043d\u0443\u0442\u043e \u0433\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u044c\u043d\u043e
config.description.gui.window.maximized.horizontal = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u043e\u043a\u043d\u0430 - \u0440\u0430\u0437\u0432\u0435\u0440\u043d\u0443\u0442\u043e \u0433\u043e\u0440\u0438\u0437\u043e\u043d\u0442\u0430\u043b\u044c\u043d\u043e
config.name.gui.window.maximized.vertical = (\u0412\u043d\u0443\u0442\u0440.) \u041e\u043a\u043d\u043e \u0440\u0430\u0437\u0432\u0435\u0440\u043d\u0443\u0442\u043e \u0432\u0435\u0440\u0442\u0438\u043a\u0430\u043b\u044c\u043d\u043e
config.description.gui.window.maximized.vertical = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0435 \u0441\u043e\u0441\u0442\u043e\u044f\u043d\u0438\u0435 \u043e\u043a\u043d\u0430 - \u0440\u0430\u0437\u0432\u0435\u0440\u043d\u0443\u0442\u043e \u0432\u0435\u0440\u0442\u0438\u043a\u0430\u043b\u044c\u043d\u043e
config.name.gui.avm2.splitPane.dividerLocationPercent=(\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0440\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b\u044f AS3
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0440\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b\u044f AS1/2
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0440\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b\u044f \u043e\u0431\u043b\u0430\u0441\u0442\u0438 \u043f\u0440\u0435\u0434\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0440\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b\u044f 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u0440\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b\u044f 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = \u0420\u0435\u0436\u0438\u043c \u0440\u0430\u0441\u0442\u044f\u0433\u0438\u0432\u0430\u043d\u0438\u044f \u0434\u043b\u044f \u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u043a\u0430\u043a EXE
config.description.saveAsExeScaleMode = \u0420\u0435\u0436\u0438\u043c \u0440\u0430\u0441\u0442\u044f\u0433\u0438\u0432\u0430\u043d\u0438\u044f \u043f\u0440\u0438 \u0441\u043e\u0445\u0440\u0430\u043d\u0435\u043d\u0438\u0438 EXE
config.name.syntaxHighlightLimit = \u041c\u0430\u043a\u0441. \u0441\u0438\u043c\u0432\u043e\u043b\u043e\u0432 \u0434\u043b\u044f \u043f\u043e\u0434\u0441\u0432\u0435\u0442\u043a\u0438 \u0441\u0438\u043d\u0442\u0430\u043a\u0441\u0438\u0441\u0430
config.description.syntaxHighlightLimit = \u041c\u0430\u043a\u0441\u0438\u043c\u0430\u043b\u044c\u043d\u043e\u0435 \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u0441\u0438\u043c\u0432\u043e\u043b\u043e\u0432, \u0434\u043b\u044f \u043a\u043e\u0442\u043e\u0440\u044b\u0445 \u0431\u0443\u0434\u0435\u0442 \u0440\u0430\u0431\u043e\u0442\u0430\u0442\u044c \u043f\u043e\u0434\u0441\u0432\u0435\u0442\u043a\u0430 \u0441\u0438\u043d\u0442\u0430\u043a\u0441\u0438\u0441\u0430
config.name.guiFontPreviewSampleText = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0439 \u0442\u0435\u043a\u0441\u0442 \u0434\u043b\u044f \u043f\u0440\u0435\u0434\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u0448\u0440\u0438\u0444\u0442\u0430
config.description.guiFontPreviewSampleText = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0439 \u0442\u0435\u043a\u0441\u0442 \u0434\u043b\u044f \u043f\u0440\u0435\u0434\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u0448\u0440\u0438\u0444\u0442\u0430
config.name.gui.fontPreviewWindow.width = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0448\u0438\u0440\u0438\u043d\u0430 \u043e\u043a\u043d\u0430 \u043f\u0440\u0435\u0434\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u0448\u0440\u0438\u0444\u0442\u0430
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u0432\u044b\u0441\u043e\u0442\u0430 \u043e\u043a\u043d\u0430 \u043f\u0440\u0435\u0434\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u0448\u0440\u0438\u0444\u0442\u0430
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u043a\u043e\u043e\u0440\u0434\u0438\u043d\u0430\u0442\u0430 X \u043e\u043a\u043d\u0430 \u043f\u0440\u0435\u0434\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u0448\u0440\u0438\u0444\u0442\u0430
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (\u0412\u043d\u0443\u0442\u0440.) \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u044f\u044f \u043a\u043e\u043e\u0440\u0434\u0438\u043d\u0430\u0442\u0430 Y \u043e\u043a\u043d\u0430 \u043f\u0440\u0435\u0434\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440\u0430 \u0448\u0440\u0438\u0444\u0442\u0430
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = \u0420\u0430\u0437\u043c\u0435\u0440 \u043e\u0442\u0441\u0442\u0443\u043f\u0430
config.description.formatting.indent.size = \u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u043f\u0440\u043e\u0431\u0435\u043b\u043e\u0432 \u0438\u043b\u0438 \u0442\u0430\u0431\u0443\u043b\u044f\u0446\u0438\u0439 \u0432 \u043e\u0434\u043d\u043e\u043c \u043e\u0442\u0441\u0442\u0443\u043f\u0435
config.name.formatting.indent.useTabs = \u041e\u0442\u0441\u0442\u0443\u043f \u0442\u0430\u0431\u0443\u043b\u044f\u0446\u0438\u0435\u0439
config.description.formatting.indent.useTabs = \u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0442\u0430\u0431\u0443\u043b\u044f\u0446\u0438\u044e \u0432\u043c\u0435\u0441\u0442\u043e \u043f\u0440\u043e\u0431\u0435\u043b\u043e\u0432 \u0434\u043b\u044f \u043e\u0442\u0441\u0442\u0443\u043f\u043e\u0432
config.name.beginBlockOnNewLine = \u0424\u0438\u0433\u0443\u0440\u043d\u0430\u044f \u0441\u043a\u043e\u0431\u043a\u0430 \u043d\u0430 \u043d\u043e\u0432\u043e\u0439 \u0441\u0442\u0440\u043e\u043a\u0435
config.description.beginBlockOnNewLine = \u041d\u0430\u0447\u0438\u043d\u0430\u0442\u044c \u0431\u043b\u043e\u043a \u043a\u043e\u0434\u0430 \u0441\u043e \u0441\u043a\u043e\u0431\u043a\u0438 \u043d\u0430 \u043d\u043e\u0432\u043e\u0439 \u0441\u0442\u0440\u043e\u043a\u0435
config.name.check.updates.delay = \u0417\u0430\u0434\u0435\u0440\u0436\u043a\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0439
config.description.check.updates.delay = \u041c\u0438\u043d\u0438\u043c\u0430\u043b\u044c\u043d\u043e\u0435 \u0432\u0440\u0435\u043c\u044f \u043c\u0435\u0436\u0434\u0443 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0430\u043c\u0438 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0439 \u043f\u043e\u0441\u043b\u0435 \u0437\u0430\u043f\u0443\u0441\u043a\u0430 \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u044f
config.name.check.updates.stable = \u0418\u0441\u043a\u0430\u0442\u044c \u0441\u0442\u0430\u0431\u0438\u043b\u044c\u043d\u044b\u0435 \u0432\u0435\u0440\u0441\u0438\u0438 \u043f\u0440\u0438 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0438
config.description.check.updates.stable = 
config.name.check.updates.nightly = \u0418\u0441\u043a\u0430\u0442\u044c \u0442\u0435\u0441\u0442\u043e\u0432\u044b\u0435 (nightly) \u0432\u0435\u0440\u0441\u0438\u0438 \u043f\u0440\u0438 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0438
config.description.check.updates.nightly = \u0418\u0441\u043a\u0430\u0442\u044c \u0442\u0435\u0441\u0442\u043e\u0432\u044b\u0435 (nightly) \u0432\u0435\u0440\u0441\u0438\u0438 \u043f\u0440\u0438 \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0438
config.name.check.updates.enabled = \u041f\u0440\u043e\u0432\u0435\u0440\u044f\u0442\u044c \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u044f
config.description.check.updates.enabled = \u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438 \u043f\u0440\u043e\u0432\u0435\u0440\u044f\u0442\u044c \u043e\u0431\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u044f \u043f\u0440\u0438 \u0437\u0430\u043f\u0443\u0441\u043a\u0435
config.name.export.formats = (\u0412\u043d\u0443\u0442\u0440.) \u0424\u043e\u0440\u043c\u0430\u0442\u044b \u0434\u043b\u044f \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430
config.description.export.formats = \u041f\u043e\u0441\u043b\u0435\u0434\u043d\u0438\u0435 \u0444\u043e\u0440\u043c\u0430\u0442\u044b \u0434\u043b\u044f \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430
config.name.textExportSingleFile = \u042d\u043a\u0441\u043f\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0442\u0435\u043a\u0441\u0442\u044b \u0432 \u043e\u0434\u0438\u043d \u0444\u0430\u0439\u043b
config.description.textExportSingleFile = \u042d\u043a\u0441\u043f\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0442\u0435\u043a\u0441\u0442\u044b \u0432 \u043e\u0434\u0438\u043d \u0444\u0430\u0439\u043b \u0432\u043c\u0435\u0441\u0442\u043e \u043d\u0435\u0441\u043a\u043e\u043b\u044c\u043a\u0438\u0445
config.name.textExportSingleFileSeparator = \u0420\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b\u044c \u0442\u0435\u043a\u0441\u0442\u043e\u0432 \u043f\u0440\u0438 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0435 \u0432 \u043e\u0434\u0438\u043d \u0444\u0430\u0439\u043b
config.description.textExportSingleFileSeparator = \u0422\u0435\u043a\u0441\u0442, \u043a\u043e\u0442\u043e\u0440\u044b\u0439 \u0441\u043b\u0435\u0434\u0443\u0435\u0442 \u0440\u0430\u0437\u043c\u0435\u0441\u0442\u0438\u0442\u044c \u043c\u0435\u0436\u0434\u0443 \u0442\u0435\u043a\u0441\u0442\u0430\u043c\u0438 \u043f\u0440\u0438 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0435 \u0432 \u043e\u0434\u0438\u043d \u0444\u0430\u0439\u043b
config.name.textExportSingleFileRecordSeparator = \u0420\u0430\u0437\u0434\u0435\u043b\u0438\u0442\u0435\u043b\u044c \u0437\u0430\u043f\u0438\u0441\u0435\u0439 \u043f\u0440\u0438 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0435 \u0432 \u043e\u0434\u0438\u043d \u0444\u0430\u0439\u043b
config.description.textExportSingleFileRecordSeparator = \u0422\u0435\u043a\u0441\u0442, \u043a\u043e\u0442\u043e\u0440\u044b\u0439 \u0441\u043b\u0435\u0434\u0443\u0435\u0442 \u0440\u0430\u0437\u043c\u0435\u0441\u0442\u0438\u0442\u044c \u043c\u0435\u0436\u0434\u0443 \u0442\u0435\u043a\u0441\u0442\u043e\u0432\u044b\u043c\u0438 \u0437\u0430\u043f\u0438\u0441\u044f\u043c\u0438 \u043f\u0440\u0438 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0435 \u0432 \u043e\u0434\u0438\u043d \u0444\u0430\u0439\u043b
config.name.warning.experimental.as12edit=\u041f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0430\u0442\u044c \u043f\u0440\u0438 \u043f\u0440\u044f\u043c\u043e\u043c \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0438 AS1/2
config.description.warning.experimental.as12edit=\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u043f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u043f\u044b\u0442\u043a\u0435 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u044f \u044d\u043a\u0441\u043f\u0435\u0440\u0438\u043c\u0435\u043d\u0442\u0430\u043b\u044c\u043d\u043e\u0433\u043e \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u044f AS1/2
config.name.warning.experimental.as3edit=\u041f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0430\u0442\u044c \u043f\u0440\u0438 \u043f\u0440\u044f\u043c\u043e\u043c \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0438 AS3
config.description.warning.experimental.as3edit=\u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u043f\u0440\u0435\u0434\u0443\u043f\u0440\u0435\u0436\u0434\u0435\u043d\u0438\u0435 \u043f\u0440\u0438 \u043f\u043e\u043f\u044b\u0442\u043a\u0435 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u044f \u044d\u043a\u0441\u043f\u0435\u0440\u0438\u043c\u0435\u043d\u0442\u0430\u043b\u044c\u043d\u043e\u0433\u043e \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u044f AS3
config.name.packJavaScripts = \u0423\u043f\u0430\u043a\u043e\u0432\u044b\u0432\u0430\u0442\u044c JavaScript'\u044b
config.description.packJavaScripts = \u0417\u0430\u043f\u0443\u0441\u043a\u0430\u0442\u044c \u0443\u043f\u0430\u043a\u043e\u0432\u0449\u0438\u043a JavaScript \u043d\u0430 \u0441\u043a\u0440\u0438\u043f\u0442\u0430\u0445, \u0441\u043e\u0437\u0434\u0430\u043d\u043d\u044b\u0445 \u0432 Canvas Export.
config.name.textExportExportFontFace = \u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c font-face \u043f\u0440\u0438 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0435 SVG
config.description.textExportExportFontFace = \u0412\u043a\u043b\u044e\u0447\u0430\u0442\u044c \u0444\u0430\u0439\u043b\u044b \u0448\u0440\u0438\u0444\u0442\u043e\u0432 \u0432 SVG \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u044f font-face \u0432\u043c\u0435\u0441\u0442\u043e \u0432\u0435\u043a\u0442\u043e\u0440\u043d\u044b\u0445 \u0444\u043e\u0440\u043c
config.name.showMethodBodyId = \u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c id \u0442\u0435\u043b\u0430 \u043c\u0435\u0442\u043e\u0434\u0430 (methodbody)
config.description.showMethodBodyId = \u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0435\u0442 id methodbody \u043f\u0440\u0438 \u0438\u043c\u043f\u043e\u0440\u0442\u0435 \u0447\u0435\u0440\u0435\u0437 \u043a\u043e\u043c\u0430\u043d\u0434\u043d\u0443\u044e \u0441\u0442\u0440\u043e\u043a\u0443
config.name.debuggerPort = \u041f\u043e\u0440\u0442 \u043e\u0442\u043b\u0430\u0434\u0447\u0438\u043a\u0430
config.description.debuggerPort = \u041f\u043e\u0440\u0442, \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u0435\u043c\u044b\u0439 \u0434\u043b\u044f \u043e\u0442\u043b\u0430\u0434\u043a\u0438 \u0447\u0435\u0440\u0435\u0437 \u0441\u043e\u043a\u0435\u0442\u043d\u043e\u0435 \u0441\u043e\u0435\u0434\u0438\u043d\u0435\u043d\u0438\u0435
config.name.randomDebuggerPackage = \u0418\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u0441\u043b\u0443\u0447\u0430\u0439\u043d\u043e\u0435 \u0438\u043c\u044f \u043f\u0430\u043a\u0435\u0442\u0430 \u0434\u043b\u044f \u043e\u0442\u043b\u0430\u0434\u0447\u0438\u043a\u0430
config.description.randomDebuggerPackage = \u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u044b\u0432\u0430\u0442\u044c \u043f\u0430\u043a\u0435\u0442 \u043e\u0442\u043b\u0430\u0434\u0447\u0438\u043a\u0430 \u0432 \u0441\u043b\u0443\u0447\u0430\u0439\u043d\u0443\u044e \u0441\u0442\u0440\u043e\u043a\u0443, \u0447\u0442\u043e \u0443\u0441\u043b\u043e\u0436\u043d\u044f\u0435\u0442 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u0435 \u043d\u0430\u043b\u0438\u0447\u0438\u044f \u043e\u0442\u043b\u0430\u0434\u0447\u0438\u043a\u0430 \u0438\u0437 ActionScript
config.name.getLocalNamesFromDebugInfo = AS3: \u041f\u043e\u043b\u0443\u0447\u0438\u0442\u044c \u0438\u043c\u0435\u043d\u0430 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u044b\u0445 \u0440\u0435\u0433\u0438\u0441\u0442\u0440\u043e\u0432 \u0438\u0437 \u043e\u0442\u043b\u0430\u0434\u043e\u0447\u043d\u043e\u0439 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u0438
config.description.getLocalNamesFromDebugInfo = \u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u044b\u0432\u0430\u0435\u0442 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u044b\u0435 \u0440\u0435\u0433\u0438\u0441\u0442\u0440\u044b \u0438\u0437 _loc_x_ \u043d\u0430 \u0440\u0435\u0430\u043b\u044c\u043d\u044b\u0435 \u0438\u043c\u0435\u043d\u0430, \u0435\u0441\u043b\u0438 \u043f\u0440\u0438\u0441\u0443\u0442\u0441\u0442\u0432\u0443\u0435\u0442 \u043e\u0442\u043b\u0430\u0434\u043e\u0447\u043d\u0430\u044f \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f. \u041c\u043e\u0436\u043d\u043e \u043e\u0442\u043a\u043b\u044e\u0447\u0438\u0442\u044c, \u0442.\u043a. \u043d\u0435\u043a\u043e\u0442\u043e\u0440\u044b\u0435 \u043e\u0431\u0444\u0443\u0441\u043a\u0430\u0442\u043e\u0440\u044b \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u044e\u0442 \u0442\u0430\u043c \u043d\u0435\u0432\u0435\u0440\u043d\u044b\u0435 \u0438\u043c\u0435\u043d\u0430 \u0440\u0435\u0433\u0438\u0441\u0442\u0440\u043e\u0432.
config.name.tagTreeShowEmptyFolders = \u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u043f\u0443\u0441\u0442\u044b\u0435 \u043f\u0430\u043f\u043a\u0438
config.description.tagTreeShowEmptyFolders = \u041f\u043e\u043a\u0430\u0437\u044b\u0432\u0430\u0442\u044c \u043f\u0443\u0441\u0442\u044b\u0435 \u043f\u0430\u043f\u043a\u0438 \u0432 \u0434\u0435\u0440\u0435\u0432\u0435 \u0442\u044d\u0433\u043e\u0432.
