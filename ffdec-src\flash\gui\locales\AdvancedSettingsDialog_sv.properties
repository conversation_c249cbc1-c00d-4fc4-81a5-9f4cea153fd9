# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Avancerade Inst\u00e4llningar
advancedSettings.restartConfirmation = Du m\u00e5ste starta om programmet f\u00f6r att vissa modifieringar ska b\u00f6rja g\u00e4lla. Vill du starta om nu?
advancedSettings.columns.name = Namn
advancedSettings.columns.value = V\u00e4rde
advancedSettings.columns.description = Beskrivning
default = standard
config.group.name.export = Exportera
config.group.description.export = konfiguration utav exporteringar
config.group.name.script = Skripts
config.group.description.script = relaterad ActionScript dekompilering
config.group.name.update = Uppdateringar
config.group.description.update = Kollar efter uppdateringar
config.group.name.format = Formatering
config.group.description.format = ActionScript kod formatering
config.group.name.limit = Gr\u00e4nser
config.group.description.limit = Dekompilerings gr\u00e4nser f\u00f6r obfuskerad kod, med mera.
config.group.name.ui = Gr\u00e4nssnitt
config.group.description.ui = Anv\u00e4ndar gr\u00e4nssnitts konfiguration
config.group.name.debug = Fels\u00f6kning
config.group.description.debug = Fels\u00f6knings inst\u00e4llningar
config.group.name.display = Visa
config.group.description.display = Visa Flash objekt, med mera.
config.group.name.decompilation = Dekompilering
config.group.description.decompilation = Global dekompilerings relaterade funktioner
config.group.name.other = Annat
config.group.description.other = Andra okategoriserade inst\u00e4llningar
config.name.openMultipleFiles = \u00d6ppna flera filer
config.description.openMultipleFiles = Till\u00e5ter \u00f6ppning utav flera filer samtidigt i ett f\u00f6nster
config.name.decompile = Visa ActionScripts k\u00e4lla
config.description.decompile = Du kan inaktivera AS dekompilering, och bara P-kod kommer att visas
config.name.dumpView = Dumpnings Vy
config.description.dumpView = Visa r\u00e5 information dumpning
config.name.useHexColorFormat = Hex f\u00e4rg utseende
config.description.useHexColorFormat = Visa f\u00e4rgerna i hex format
config.name.parallelSpeedUp = Parallell Uppsnabbning
config.description.parallelSpeedUp = Parallellitet kan snabba upp dekompileringen
config.name.parallelSpeedUpThreadCount = Antal tr\u00e5dar (0 = auto)
config.description.parallelSpeedUpThreadCount = Antal tr\u00e5dar f\u00f6r parallell uppsnabbning. 0 = processor count - 1.
config.name.autoDeobfuscate = Automatisk deobfuskering
config.description.autoDeobfuscate = K\u00f6r deobfuskering p\u00e5 alla filer innan ActionScript dekompilering
config.name.cacheOnDisk = Anv\u00e4nd cachning p\u00e5 disk
config.description.cacheOnDisk = Cache dekompilerar delar p\u00e5 h\u00e5rddisken ist\u00e4llet f\u00f6r minnet
config.name.internalFlashViewer = Anv\u00e4nd egen Flash visare
config.description.internalFlashViewer = Anv\u00e4nd JPEXS Flash Visare ist\u00e4llet f\u00f6r standard Flash Spelare f\u00f6r flash delar uppvisning
config.name.gotoMainClassOnStartup = G\u00e5 till huvudklass vid start (AS3)
config.description.gotoMainClassOnStartup = Navigerar till dokumentklass utav AS3 fil n\u00e4r SWF \u00f6ppnas
config.name.autoRenameIdentifiers = Automatiskt byt namn p\u00e5 identifierare
config.description.autoRenameIdentifiers = Automatiskt byt namn p\u00e5 ogiltiga identifierare n\u00e4r SWF laddas
config.name.offeredAssociation = (Internal) F\u00f6reningen med SWF-filer visas
config.description.offeredAssociation = Dialog om filen F\u00f6reningen visas redan
config.name.decimalAddress = Anv\u00e4nd decimal adresser
config.description.decimalAddress = Anv\u00e4nd decimal adresser ist\u00e4llet f\u00f6r hexadecimal
config.name.showAllAddresses = Visa alla adresser
config.description.showAllAddresses = Visa alla ActionScript instruktionsadresser
config.name.useFrameCache = Anv\u00e4nd ram cache
config.description.useFrameCache = Cache:a ram innan rendering 
config.name.useRibbonInterface = Band gr\u00e4nssnitt
config.description.useRibbonInterface = Klicka ur  f\u00f6r att anv\u00e4nda klassiskt gr\u00e4nssnitt utan band-meny
config.name.openFolderAfterFlaExport = \u00d6ppna mapp efter FLA exportering
config.description.openFolderAfterFlaExport = Visa utg\u00e5ngs katalog efter exportering utav FLA fil
config.name.useDetailedLogging = Detaljerad Loggning
config.description.useDetailedLogging = Logga detaljerade felmeddelande och information f\u00f6r fels\u00f6knings \u00e4ndam\u00e5l
config.name.resolveConstants = L\u00f6s konstanter i AS1/2 P-kod
config.description.resolveConstants = St\u00e4ng av detta f\u00f6r att visa 'constantxx' ist\u00e4llet f\u00f6r riktiga v\u00e4rden i P-kod f\u00f6nstret
config.name.sublimiter = Gr\u00e4nsen f\u00f6r kod subs
config.description.sublimiter = Gr\u00e4nsen f\u00f6r kod subs f\u00f6r obfuskerad kod.
config.name.exportTimeout = Total exporterings timeout (sekunder)
config.description.exportTimeout = Dekompileringen kommer att stanna exporteringen efter att ha n\u00e5tt den h\u00e4r tiden
config.name.decompilationTimeoutFile = Enstaka fil-dekompilering timeout (sekunder)
config.description.decompilationTimeoutFile = Dekompileraren kommer att stoppa ActionScript dekompileringen efter att ha n\u00e5tt den h\u00e4r tiden i en fil
config.name.paramNamesEnable = Aktivera parameter namn i AS3
config.description.paramNamesEnable = Anv\u00e4nd parameter namn i dekompilering kan orsaka problem eftersom officiella program som Flash CS 5.5 infogar fel parameternamn index
config.name.displayFileName = Visa SWF namn i titeln
config.description.displayFileName = Visa SWF fil/l\u00e4nk namn i f\u00f6nster titeln (Du kan skapa sk\u00e4rmdumpar d\u00e5)
config.name.dumpTags = Dumpa taggar till konsolen
config.description.dumpTags = Dumpa taggar till konsolen efter l\u00e4sning av SWF-fil
config.name.decompilationTimeoutSingleMethod = AS3: Enstaka metod-dekompilering timeout (sekunder)
config.description.decompilationTimeoutSingleMethod = Dekompileraren kommer att stoppa ActionScript dekompileringen efter att ha n\u00e5tt den h\u00e4r tiden i en metod
config.name.lastRenameType = (Internal) Senaste namnbytes typ
config.description.lastRenameType = Senaste namnbytes identifierings typ
config.name.lastSaveDir = (Internal) Senaste sparade katalog
config.description.lastSaveDir = Senaste anv\u00e4ndna sparade katalog
config.name.lastOpenDir = (Internal) Senaste \u00f6ppna katalog
config.description.lastOpenDir = Senaste anv\u00e4ndna \u00f6ppna katalog
config.name.lastExportDir = (Internal) Senaste exporterings katalog
config.description.lastExportDir = Senaste anv\u00e4ndna exporterings katalog
config.name.locale = Spr\u00e5k
config.description.locale = Plats identifierare
config.name.registerNameFormat = Register variabel format
config.description.registerNameFormat = Format av lokalt register variabla namn. Anv\u00e4nd %d f\u00f6r register nummer.
config.name.maxRecentFileCount = Maximala senaste r\u00e4kningen
config.description.maxRecentFileCount = Maximalt nummer av senaste filerna
config.name.recentFiles = (Internal) Senaste filerna
config.description.recentFiles = Senaste \u00f6ppnade filer
config.name.fontPairingMap = (Internal) Typsnittspar f\u00f6r importering
config.description.fontPairingMap = Typsnittspar f\u00f6r att importera nya tecken
config.name.lastUpdatesCheckDate = (Internal) Senaste uppdateringskontrolls datum
config.description.lastUpdatesCheckDate = Datum av senaste kontroll f\u00f6r uppdateringsserver
config.name.gui.window.width = (Internal) Senaste f\u00f6nsterbreddd
config.description.gui.window.width = Senaste sparad f\u00f6nsterbredd
config.name.gui.window.height = (Internal) Senaste f\u00f6nsterh\u00f6jd
config.description.gui.window.height = Senaste sparad f\u00f6nsterh\u00f6jd
config.name.gui.window.maximized.horizontal = (Internal) Maximerat f\u00f6nster horisontellt
config.description.gui.window.maximized.horizontal = Senaste f\u00f6nsterl\u00e4ge - maximerat horisontellt
config.name.gui.window.maximized.vertical = (Internal) Maximerat f\u00f6nter vertikalt
config.description.gui.window.maximized.vertical = Senaste f\u00f6nsterl\u00e4ge - maximerat vertikalt
config.name.gui.avm2.splitPane.dividerLocationPercent=(Internal) AS3 Splitter l\u00e4ge
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Internal) AS1/2 splitter l\u00e4ge
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Internal) F\u00f6rhandsvisa splitter l\u00e4ge
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Internal) Splitter l\u00e4ge 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Internal) Splitter l\u00e4ge 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Spara som EXE skalningsl\u00e4ge
config.description.saveAsExeScaleMode = Skalningsl\u00e4ge f\u00f6r EXE exportering
config.name.syntaxHighlightLimit = Max tecken i syntax highlightning
config.description.syntaxHighlightLimit = Maximalt antal nummmer av texken f\u00f6r att k\u00f6ra syntax hilight p\u00e5
config.name.guiFontPreviewSampleText = (Internal) Senaste typsnitt f\u00f6rhandsvisnings exempeltext
config.description.guiFontPreviewSampleText = Senaste typsnitt f\u00f6rhandsvisning exempeltext index listan
config.name.gui.fontPreviewWindow.width = (Internal) Senaste typsnittsf\u00f6rhandsvisningens f\u00f6nsterbredd
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Internal) Senaste typsnittsf\u00f6rhandsvisningens f\u00f6nsterh\u00f6jd
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Internal) Senaste typsnittsf\u00f6rhandsvisningens f\u00f6nster X
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Internal) Senaste typsnittsf\u00f6rhandsvisningens f\u00f6nster Y
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Tecken per strecksats
config.description.formatting.indent.size = Nummer eller space(eller tabs) f\u00f6r ett indrag
config.name.formatting.indent.useTabs = Tabs f\u00f6r strecksats
config.description.formatting.indent.useTabs = Anv\u00e4nd tabs ist\u00e4llet f\u00f6r spaces f\u00f6r indrag
config.name.beginBlockOnNewLine = klammerparantes p\u00e5 ny rad
config.description.beginBlockOnNewLine = Starta block med klammerparantes p\u00e5 ny rad
config.name.check.updates.delay = Kontrollera Uppdaterings f\u00f6rdr\u00f6jning
config.description.check.updates.delay = Minimal tid mellan automatisk kontroll f\u00f6r uppdateringar vid mjukvaru start
config.name.check.updates.stable = Kontrollera f\u00f6r stabila versioner
config.description.check.updates.stable = Kontrollerar f\u00f6r f\u00f6r stabila uppdateringar
config.name.check.updates.nightly = Kontrollera f\u00f6r nightly versioner
config.description.check.updates.nightly = Checking for nightly uppdateringar
config.name.check.updates.enabled = Uppdaterings kontroll aktiverad
config.description.check.updates.enabled = Automatiskt kolla efter uppdateringar efter mjukvaru start
config.name.export.formats = (Internal) Exporterings format
config.description.export.formats = Senast anv\u00e4ndna exporterings format
config.name.textExportSingleFile = Exportera texter i samma fil
config.description.textExportSingleFile = Exporters texter i en och samma fil ist\u00e4llet f\u00f6r i flera
config.name.textExportSingleFileSeparator = Separerare av texter i en och samma fil till text-exportering
config.description.textExportSingleFileSeparator = Text som ska infogas mellan texter i en och samma fil till text-exportering
config.name.textExportSingleFileRecordSeparator = Separerare av uppgifter i en och samma fil till text-exportering
config.description.textExportSingleFileRecordSeparator = Text som ska infogas mellan text uppgifter i en och samma fil till text-exportering
config.name.warning.experimental.as12edit=Varna p\u00e5 AS1/2 direkt redigering
config.description.warning.experimental.as12edit=Visa varning p\u00e5 AS1/2 experimentell direkt redigering
config.name.warning.experimental.as3edit=Varna p\u00e5 AS3 direkt redigering
config.description.warning.experimental.as3edit=Visa varning p\u00e5 AS3 experimentell direkt redigering
config.name.packJavaScripts = Paket JavaScripts
config.description.packJavaScripts = K\u00f6r JavaScript packare p\u00e5 skripts skapade p\u00e5 Canvas Exportering.
config.name.textExportExportFontFace = Anv\u00e4nd typsnittsyta i SVG exportering
config.description.textExportExportFontFace = B\u00e4dda in typsnitssfiler i SVG, anv\u00e4nder typsnittsyta ist\u00e4llet f\u00f6r former
config.name.lzmaFastBytes = LZMA snabba bytes (Giltiga v\u00e4rden: 5-255)
config.description.lzmaFastBytes = Snabb bytes parametrar av LZMA kodare
config.name.pluginPath = Plugin S\u00f6kv\u00e4g
config.description.pluginPath = -   
config.name.showMethodBodyId = Visa metod kropps id
config.description.showMethodBodyId = Visar id:t utav methodbody f\u00f6r commandline importering
config.name.export.zoom = (Internal) Exportera zoom
config.description.export.zoom = Senast anv\u00e4nd exporterings zoom
config.name.debuggerPort = Debugger port
config.description.debuggerPort = Port anv\u00e4nd f\u00f6r socket debuggning
config.name.displayDebuggerInfo = (Internal) Visa debugger information
config.description.displayDebuggerInfo = Visa information om debuggern innan man v\u00e4xlar det
config.name.randomDebuggerPackage = Anv\u00e4nd slumpm\u00e4ssigt paketnamn f\u00f6r debugger
config.description.randomDebuggerPackage = Detta byter namn p\u00e5 debugger paketet till slumpm\u00e4ssig str\u00e4ng vilket g\u00f6r debuggern n\u00e4rvarar h\u00e5rdare f\u00f6r att uppt\u00e4ckas av ActionScript
config.name.lastDebuggerReplaceFunction = (Internal) Senast valda sp\u00e5r byte
config.description.lastDebuggerReplaceFunction = Funktion-namn vilket var senast vald i ers\u00e4ttningssp\u00e5r funktion vid debugger
config.name.getLocalNamesFromDebugInfo = AS3: F\u00e5 lokala registernamn fr\u00e5n debugger information
config.description.getLocalNamesFromDebugInfo = Om debug information visas och \u00e4r n\u00e4rvarande, namnbyte av lokala register fr\u00e5n _loc_x_ till riktiga namn. Detta kan st\u00e4ngas av eftersom vissa obfuskerare anv\u00e4nder ogiltiga registernamn d\u00e4r.
config.name.tagTreeShowEmptyFolders = Visa tomma mappar
config.description.tagTreeShowEmptyFolders = Visa tomma mappar i tr\u00e4dvyn.
config.name.autoLoadEmbeddedSwfs = Ladda inb\u00e4ddade SWF automatiskt
config.description.autoLoadEmbeddedSwfs = Ladda automatiskt inb\u00e4ddade SWFs fr\u00e5n DefineBinaryData taggar.
config.name.overrideTextExportFileName = \u00d6verskrid text-exporterings filnamnet
config.description.overrideTextExportFileName = Du kan finjustera filnamnet p\u00e5 den exporterade texten. Anv\u00e4nd {filename} platsh\u00e5llare f\u00f6r att anv\u00e4nda filnamnet av nuvaranda SWF.
config.name.showOldTextDuringTextEditing = Visa gammal text vid text \u00e4ndring
config.description.showOldTextDuringTextEditing = Visar orginala texten av text taggen med gr\u00e5 f\u00e4rg i f\u00f6rhandsvisnings omr\u00e5det.
config.group.name.import = Importering
config.group.description.import = Konfiguration av importeringar
config.name.textImportResizeTextBoundsMode = Text gr\u00e4ns \u00e4ndra storlekl\u00e4ge
config.description.textImportResizeTextBoundsMode = Text gr\u00e4ns storlekl\u00e4ge efter text \u00e4ndring.
config.name.showCloseConfirmation = Visa SWF igen efter st\u00e4ng bekr\u00e4ftelse
config.description.showCloseConfirmation = Visa SWF igen efter st\u00e4ng bekr\u00e4ftelse f\u00f6r modifierade filer 
config.name.showCodeSavedMessage = Visa kod igen efter sparade meddelanden
config.description.showCodeSavedMessage = Visa kod igen efter sparade meddelanden
config.name.showTraitSavedMessage = Visa trait igen efter sparade meddelanden
config.description.showTraitSavedMessage = Visa trait igen efter sparade meddelande
config.name.updateProxyAddress = Http Proxy adress f\u00f6r att kolla efter uppdateringar
config.description.updateProxyAddress = Http Proxy adress f\u00f6r att kolla uppdateringar. Format: example.com:8080
config.name.editorMode = \u00c4ndrings Mode
config.description.editorMode = G\u00f6r text omr\u00e5den redigerbara automatiskt n\u00e4r du v\u00e4ljer en Text eller en Skriptnod
config.name.autoSaveTagModifications = Spara automatiskt tag modifieringar
config.description.autoSaveTagModifications = Spara \u00e4ndringarna n\u00e4r du v\u00e4ljer en ny tag i tr\u00e4dvyn
config.name.saveSessionOnExit = Spara session vid avst\u00e4ngning
config.description.saveSessionOnExit = Spara den nuvarande sessionen och \u00f6ppna igen efter FFDEc omstart (fungerar bara med riktiga filer)
config.name.allowOnlyOneInstance = Till\u00e5t bara en FFDec instans (Bara Windows OS)
config.description.allowOnlyOneInstance = FFDec kan sedan bara k\u00f6ras en g\u00e5ng, alla filer som \u00e4r \u00f6ppnade vill bli tillagda till ett f\u00f6nster. Det fungerar bara med Windows operativsystem.
config.name.scriptExportSingleFile = Exportera skript till en enda fil
config.description.scriptExportSingleFile = Exportera skript till en fil ist\u00e4llet f\u00f6r flera
config.name.setFFDecVersionInExportedFont = S\u00e4tt FFDec versionnummer i exporterat typsnitt
config.description.setFFDecVersionInExportedFont = N\u00e4r den h\u00e4r inst\u00e4llningen \u00e4r avaktiverad, FFDec kommer inte att l\u00e4gga till nuvarande FFDec version nummer till det exporterade typsnittet.
config.name.gui.skin = Anv\u00e4ndargr\u00e4nssnitt Tema
config.description.gui.skin = Utseende och k\u00e4nsla Tema
config.name.lastSessionFiles = Senast session filerna
config.description.lastSessionFiles = Inneh\u00e5ller de \u00f6ppnade filerna fr\u00e5n den sista sessionen
config.name.lastSessionSelection = Senaste session val
config.description.lastSessionSelection = Inneh\u00e5ller urval fr\u00e5n den senaste sessionen
config.name.loopMedia = Upprepa ljud och sprites
config.description.loopMedia = Startar automatiskt om spelandet av ljud och sprites
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Internal) TidsLinje platsdelare
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Cache:a bilder
config.description.cacheImages = Cache:a avkodade bildobjekt
config.name.swfSpecificConfigs = SWF specifika konfigurationer
config.description.swfSpecificConfigs = Inneh\u00e5ller SWF specifika konfigurationer
config.name.exeExportMode = EXE exporterings l\u00e4ge
config.description.exeExportMode = EXE exporterings l\u00e4ge
config.name.ignoreCLikePackages = Ignorera FlashCC / Alchemy eller liknande paket
config.description.ignoreCLikePackages = FlashCC/Alchemy paket kan inte bli dekompilerade korrekt. Du kan avaktivera dom f\u00f6r att uppsnabba andra paktets dekompileringar.
config.name.overwriteExistingFiles = Skriv \u00f6ver Nuvarande filer
config.description.overwriteExistingFiles = Skriv \u00f6ver nuvarande filer vid exportering. F\u00f6r n\u00e4rvarande bara AS2/3 skript
config.name.smartNumberFormatting = Anv\u00e4nd smart nummer formatering
config.description.smartNumberFormatting = Formatera speciella nummer (till exempel f\u00e4rger och tider)
config.name.enableScriptInitializerDisplay = (REMOVED) Visa skriptinitialiserare
config.description.enableScriptInitializerDisplay = Aktivera Visa skriptinitialiserare visning och redigering. Den h\u00e4r inst\u00e4llningen kanske kan l\u00e4gga till en ny rad till varje klass fil f\u00f6r highlightning.
config.name.autoOpenLoadedSWFs = \u00d6ppna laddade SWFs under k\u00f6rning (External visare = WIN only)
config.description.autoOpenLoadedSWFs = \u00d6ppnar automatiskt upp alla SWFs som \u00e4r laddade av AS3 Klass Laddare med att k\u00f6ra SWF n\u00e4r den spelas i FFDEC exteneral spelare. Denna funktion \u00e4r f\u00f6r Windows bara.
config.name.lastSessionFileTitles = Senaste filtitlar
config.description.lastSessionFileTitles = Inneh\u00e5ller dom \u00f6ppna fil titlarna fr\u00e5n den senaste sessionen. (Exempel en laddad l\u00e4nk osv.)
config.group.name.paths = S\u00f6kv\u00e4gar
config.group.description.paths = Plats f\u00f6r n\u00f6dv\u00e4ndiga filer
config.group.tip.paths = Ladda ner projector and Playerglobal fr\u00e5n <a href="%link1%">adobe webbsida</a>. Flex SDK kan blir nerladdad fr\u00e5n <a href="%link2%">apache web</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Flash Player projector s\u00f6kv\u00e4g
config.description.playerLocation = Plats f\u00f6r standalone flash player program. Anv\u00e4nds f\u00f6r normalt anv\u00e4ndande.
config.name.playerDebugLocation = 2) Flash Player projector content debugger s\u00f6kv\u00e4g
config.description.playerDebugLocation = Plats f\u00f6r standalone debug flash player program. Anv\u00e4nds f\u00f6r debugging.
config.name.playerLibLocation = 3) PlayerGlobal (.swc) s\u00f6kv\u00e4g
config.description.playerLibLocation = Plats f\u00f6r playerglobal.swc flash player library. Det anv\u00e4nds mestadels f\u00f6r AS3 kompilering.
config.name.debugHalt = Stoppa utf\u00f6ring vid debuggning starten
config.description.debugHalt = Pausa SWF vid start av debuggning.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Internal) Fels\u00f6kningsmenyns delningsplats
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Tips: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Internal) AS1/2 Debugg meny delningsplats
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = F\u00f6rdr\u00f6jning innan byte till SWF i external spelare i ms
config.description.setMovieDelay = Det \u00e4r inte rekomenderat att byta detta v\u00e4rde under 1000ms
config.name.warning.svgImport = Varna vid SVG importering
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Anv\u00e4nd icke-sl\u00e4t fyllning n\u00e4r en form ers\u00e4tts med en bild
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=AS1/2 i egen Flash spelare (Experimentell)
config.description.internalFlashViewer.execute.as12=Testa att k\u00f6ra ActionSkript 1/2 under SWF Try to execute ActionScript 1/2 during SWF uppspelning med FFDec-flashvisare
config.name.warning.hexViewNotUpToDate = Visa Hex vy inte uppdaterad varning
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = Visa \u00a7\u00a7dup instruktioner
config.description.displayDupInstructions = Visa \u00a7\u00a7dup instruktioner i koden. Utan dom, s\u00e5 kan koden bli enkelt kompilerad men n\u00e5gon dupped kod med sideffekter kan bli k\u00f6rd dubbla g\u00e5nger.
config.name.useRegExprLiteral = Dekompilera RegExp som /pattern/mod literal.
config.description.useRegExprLiteral = Anv\u00e4nd /pattern/mod syntax n\u00e4r du decompilerar regular expressions. new RegExp("pat","mod") \u00e4r anv\u00e4nd i vilket fall
config.name.handleSkinPartsAutomatically = Anv\u00e4nd [SkinPart] metadata automatiskt
config.description.handleSkinPartsAutomatically = Dekompilerar och direkt redigeras [SkinPart] metadata automatiskt. Men avst\u00e4ngd, _skinParts v\u00e4rde och getter metod \u00e4r synliga och manuellt redigerbara.
config.name.simplifyExpressions = F\u00f6renkla uttryck
config.description.simplifyExpressions = Utv\u00e4rdera och f\u00f6renkla uttryck f\u00f6r att g\u00f6ra koden mer l\u00e4sbar
config.name.resetLetterSpacingOnTextImport = \u00c5terst\u00e4ll tecken mellanrum vid text importering
config.description.resetLetterSpacingOnTextImport = Anv\u00e4ndbar f\u00f6r kyrilliska teckensnitt, eftersom de \u00e4r bredare
config.name.flexSdkLocation = 4) Flex SDK katalogv\u00e4g
config.description.flexSdkLocation = S\u00f6kv\u00e4g av Adobe Flex SDK. Det anv\u00e4nds mest vid AS3 kompilering.
config.name.useFlexAs3Compiler=Anv\u00e4nd Flex SDK AS3 kompilerare
config.description.useFlexAs3Compiler=Anv\u00e4nd AS3 kompilerare fr\u00e5n Flex SDK medans ActionSkript direkt redigering (Flex SDK katalogv\u00e4g beh\u00f6vs)
config.name.showSetAdvanceValuesMessage = Visa information igen om s\u00e4tta avancerade v\u00e4rden
config.description.showSetAdvanceValuesMessage = Visa information igen om s\u00e4tta avancerade v\u00e4rden
config.name.gui.fontSizeMultiplier = Typsnitts storleks multiplikator
config.description.gui.fontSizeMultiplier = Typsnitts storleks multiplikator
config.name.graphVizDotLocation = 5) GraphViz Dot k\u00f6rnings s\u00f6kv\u00e4g
config.description.graphVizDotLocation = S\u00f6kv\u00e4g till dot.exe (eller liknande till linux) av GraphViz program f\u00f6r att visa grafer.
#Do not translate the Font Styles which is in the parenthesis:(Plain,Bold,Italic,BoldItalic)
config.name.gui.sourceFont = Typsnitsk\u00e4lla stil
config.description.gui.sourceFont = FontName-FontStyle(Plain,Bold,Italic,BoldItalic)-FontSize
#after 11.1.0
config.name.as12DeobfuscatorExecutionLimit=AS1/2 deobfuskering k\u00f6rnings gr\u00e4ns
config.description.as12DeobfuscatorExecutionLimit=Maximum nummer av instruktioner som behandlats under AS1 / 2-deobfuskering
#option that ignore in 8.0.1 and other versions
config.name.showOriginalBytesInPcodeHex = (Internal) Visa originala bytes
config.description.showOriginalBytesInPcodeHex = Visa originala bytes i P-kod Hex
config.name.showFileOffsetInPcodeHex = (Internal) Visa Filoffset
config.description.showFileOffsetInPcodeHex = Visa FilOffset i Pcode Hex
config.name._enableFlexExport=(Internal) enableFlexExport
config.description.enableFlexExport = aktivera Flex Exportering
config.name._ignoreAdditionalFlexClasses=(Internal) ignoreAdditionalFlexClasses
config.description.ignoreAdditionalFlexClasses = ignorera ytterligare flexklasser
config.name.hwAcceleratedGraphics = 
config.description.hwAcceleratedGraphics = 
config.name.gui.avm2.splitPane.docs.dividerLocationPercent=(Internal) splitPanedocsdividerLocationPercent
config.description.gui.avm2.splitPane.docs.dividerLocationPercent=splitPane dokument avdelnings Location Percent
config.name.gui.dump.splitPane.dividerLocationPercent = (Internal) dumpsplitPanedividerLocationPercent
config.description.gui.dump.splitPane.dividerLocationPercent = dump splitPane avdelnings Location Percent
#after 11.3.0
config.name.useAdobeFlashPlayerForPreviews = (Deprecated) Anv\u00e4nd Adobe Flash player f\u00f6r att f\u00f6rhandsgranska objekt
config.description.useAdobeFlashPlayerForPreviews = Anv\u00e4nd Adobe Flash player f\u00f6r att f\u00f6rhandsgranska objekt. VARNING: FlashPlayer blev upph\u00f6rt 2021-01-12
#after 12.0.1
config.name.showLineNumbersInPCodeGraphvizGraph = Visa rad nummer i Graphviz graphs
config.description.showLineNumbersInPCodeGraphvizGraph = Visa rad nummer i P-Kod graphviz diagram.
config.name.padAs3PCodeInstructionName=Pad AS3 P-kod instruktion namn
config.description.padAs3PCodeInstructionName=Pad AS3 P-kod instruktioners namn med mellanslag
#after 13.0.2
config.name.indentAs3PCode=Indent AS3 P-code
config.description.indentAs3PCode=Indent AS3 P-kod block som trait/body/code
config.name.labelOnSeparateLineAs3PCode=Label in AS3 P-kod p\u00e5 separat rad
config.description.labelOnSeparateLineAs3PCode=Skapa label p\u00e5 AS3 P-kod st\u00e5 p\u00e5 separat rad
config.name.useOldStyleGetSetLocalsAs3PCode=Anv\u00e4nd gammal stil getlocal_x ist\u00e4llet f\u00f6r getlocalx i AS3 P-kod
config.description.useOldStyleGetSetLocalsAs3PCode=Anv\u00e4nd gammal stil getlocal_x, setlocal_x fr\u00e5n FFDec 12.x eller \u00e4ldre
config.name.useOldStyleLookupSwitchAs3PCode=Anv\u00e4nd gammal stil lookupswitch utan parantes i AS3 P-kod
config.description.useOldStyleLookupSwitchAs3PCode=Anv\u00e4nd gammal stil lookupswitch fr\u00e5n FFDec 12.x eller \u00e4ldre
#after 13.0.3
config.name.checkForModifications = Kolla efter fil modifiering utanf\u00f6r FFDecc
config.description.checkForModifications = Kolla efter modifiering av filer av andra program och fr\u00e5ga efter ladda om
config.name.warning.initializers = Varna p\u00e5 AS3 slot/const redigering om initialiserare
config.description.warning.initializers = Visa varning p\u00e5 AS3 slot/const redigering om initialiserare
config.name.parametersPanelInSearchResults = Visa parameterpanelen i s\u00f6kresultaten
config.description.parametersPanelInSearchResults = Visa parameterpanelen med parametrar som s\u00f6k text / ignorera case / regexp i s\u00f6kresultatens f\u00f6nster
config.name.displayAs3PCodeDocsPanel=Visa dokument panelen AS3 P-kod
config.description.displayAs3PCodeDocsPanel=Visa panelen om dokumentation om instruktioner och kod struktur i AS3 P-kod redigering och visning
config.name.displayAs3TraitsListAndConstantsPanel=Visa AS3 traits lista och constants panelen
config.description.displayAs3TraitsListAndConstantsPanel=Visa panelen med lista av traits och constants under tag tr\u00e4det for AS3
config.name.useAsTypeIcons = Anv\u00e4nd skriptikoner baserat p\u00e5 artikeltyp 
config.description.useAsTypeIcons = Anv\u00e4nd olika ikoner f\u00f6r annorlunda skript typer (class/interface/frame/...)
config.name.limitAs3PCodeOffsetMatching=Begr\u00e4nsa av AS3 P-kod offset matchning
config.description.limitAs3PCodeOffsetMatching=Begr\u00e4nsa av instruktioner i AS3 P-kod vilket \u00e4r offset-matchad AS3 skript
config.name.showSlowRenderingWarning = Logga varning n\u00e4r rendering \u00e4r f\u00f6r seg
config.description.showSlowRenderingWarning = Loggar varning n\u00e4r internal flash viewer \u00e4r f\u00f6r sl\u00f6 f\u00f6r att visa inneh\u00e5ll
config.name.autoCloseQuotes = St\u00e4ng enstaka citat automatiskt vid redigering av skript
config.description.autoCloseQuotes = Infogar automatiskt andra enskilda citat ' n\u00e4r du skriver f\u00f6rsta
config.name.autoCloseDoubleQuotes = St\u00e4ng dubbla citat automatiskt vid redigering av skript
config.description.autoCloseDoubleQuotes = Infogar automatiskt andra dubbla citat " n\u00e4r du skriver f\u00f6rsta
config.name.autoCloseBrackets = St\u00e4ng parenteser automatiskt vid skriptredigering
config.description.autoCloseBrackets = Infogar automatiskt st\u00e4ngningsf\u00e4stet ] vid skriv\u00f6ppningen [ 
config.name.autoCloseParenthesis = St\u00e4ng parentes automatiskt vid skriptredigering
config.description.autoCloseParenthesis = Infogar automatiskt st\u00e4ngande parentes) vid skriv\u00f6ppningen (
config.name.showDialogOnError = Visa feldialogrutan vid alla felh\u00e4ndelser
config.description.showDialogOnError = Visar automatiskt feldialogrutan vid varje felh\u00e4ndelse
