# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
version = \u0432\u0435\u0440\u0441\u0456\u044f
releasedate = \u0414\u0430\u0442\u0430 \u0432\u0438\u0445\u043e\u0434\u0443:
newversionavailable = \u0414\u043e\u0441\u0442\u0443\u043f\u043d\u0430 \u043d\u043e\u0432\u0430 \u0432\u0435\u0440\u0441\u0456\u044f:
changeslog = \u0417\u043c\u0456\u043d\u0438:
downloadnow = \u0417\u0430\u0432\u0430\u043d\u0442\u0430\u0436\u0438\u0442\u0438 \u0437\u0430\u0440\u0430\u0437?
button.ok = \u0413\u0430\u0440\u0430\u0437\u0434
button.cancel = \u0421\u043a\u0430\u0441\u0443\u0432\u0430\u0442\u0438
dialog.title = \u0414\u043e\u0441\u0442\u0443\u043f\u043d\u0430 \u043d\u043e\u0432\u0430 \u0432\u0435\u0440\u0441\u0456\u044f
newversion = \u041d\u043e\u0432\u0430 \u0432\u0435\u0440\u0441\u0456\u044f
newvermessage = \u0414\u043e\u0441\u0442\u0443\u043f\u043d\u0430 \u043d\u043e\u0432\u0430 \u0432\u0435\u0440\u0441\u0456\u044f %oldAppName%: %newAppName%.\r\n\u0411\u0443\u0434\u044c \u043b\u0430\u0441\u043a\u0430, \u043f\u0435\u0440\u0435\u0439\u0434\u0456\u0442\u044c \u043d\u0430 \u0441\u0430\u0439\u0442 %projectPage% \u0434\u043b\u044f \u0437\u0430\u0432\u0430\u043d\u0442\u0430\u0436\u0435\u043d\u043d\u044f.
#change this only when the date format is wrong in the changelog
#you can use any java date format string, e.g: yyyy.MM.dd
customDateFormat = default
