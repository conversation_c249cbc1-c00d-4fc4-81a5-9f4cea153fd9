# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

library = Library

library.folder.images = images
library.folder.graphics = graphics
library.folder.shapeTweens = shapeTweens
library.folder.texts = texts
library.folder.fonts = fonts
library.folder.movieClips = movieClips
library.folder.buttons = buttons
library.folder.sounds = sounds
library.folder.videos = videos

library.header.name = Name
library.header.asLinkage = AS Linkage

item.image = image
item.graphic = graphic
item.shapeTween = shapeTween
item.text = text
item.font = font
item.movieClip = movieClip
item.button = button
item.sound = sound
item.video = video
item.unknown = unknown

undo = Undo %action%
undo.cannot = Cannot undo
redo = Redo %action%
redo.cannot = Cannot redo

transform = Transform

action.addFrame = Add frame
action.addKeyFrame = Add key frame
action.addKeyFrameWithBlankFrameBefore = Add keyframe with blank frames before
action.removeFrame = Remove frame
action.transform = Transform
action.move = Move
action.addToStage = Add to stage
action.change = Change %item%

action.change.compression = Compression
action.change.swfVersion = SWF version
action.change.encrypted = Encrypted
action.change.swfVersion = SWF version
action.change.gfx = GFX
action.change.frameRate = Frame rate
action.change.width = Width
action.change.height = Height
action.change.colorEffect = Color effect



timeline.main = Main timeline
timeline.item = Timeline of %item%
timeline.item.cancel = Cancel - Click to go to main timeline

properties = Properties
properties.document = Document
properties.instance.single = Instance of %item%
properties.instance.multiple = %count% instances
properties.instance.none = No instance

properties.instance.header.positionSize = Position and size
properties.instance.header.colorEffect = Color effect

property.label = %item%:

property.instance.item = Item
property.instance.colorEffect.alpha = Alpha
property.instance.colorEffect.red = Red
property.instance.colorEffect.green = Green
property.instance.colorEffect.blue = Blue
property.instance.positionSize.x = X
property.instance.positionSize.y = Y
property.instance.positionSize.width = W
property.instance.positionSize.height = H

properties.instance.header.display = Display

property.instance.display.visible = Visible
property.instance.display.blending = Blending

property.instance.display.blending.normal = Normal
property.instance.display.blending.layer = Layer
property.instance.display.blending.multiply = Multiply
property.instance.display.blending.screen = Screen
property.instance.display.blending.lighten = Lighten
property.instance.display.blending.darken = Darken
property.instance.display.blending.difference = Difference
property.instance.display.blending.add = Add
property.instance.display.blending.subtract = Subtract
property.instance.display.blending.invert = Invert
property.instance.display.blending.alpha = Alpha
property.instance.display.blending.erase = Erase
property.instance.display.blending.overlay = Overlay
property.instance.display.blending.hardlight = Hardlight

property.instance.display.cacheAsBitmap = Cache as bitmap
property.instance.display.cacheAsBitmap.transparent = Transparent
property.instance.display.cacheAsBitmap.opaque = Opaque

#after 22.0.2
property.document.backgroundColor = Background color

properties.instance.header.filters = Filters

property.instance.filters.header.property = Property
property.instance.filters.header.value = Value

property.instance.filters.indeterminate = Indeterminate

property.instance.filters = Filters
property.instance.filters.menu.add = Add filter
property.instance.filters.menu.add.removeAll = Remove all
property.instance.filters.menu.remove = Remove filter

filter.bevel = Bevel
filter.blur = Blur
filter.colormatrix = Adjust color
filter.convolution = Convolution
filter.dropshadow = Drop shadow
filter.glow = Glow
filter.gradientbevel = Gradient bevel
filter.gradientglow = Gradient glow

convolution.identity = Identity
convolution.boxBlur = Box blur
convolution.gaussianBlur = Gaussian blur
convolution.sharpen = Sharpen
convolution.edgeDetectionXSobel = Edge detection X (Sobel)
convolution.edgeDetectionYSobel = Edge detection Y (Sobel)
convolution.edgeDetectionXPrewitt = Edge detection X (Prewitt)
convolution.edgeDetectionYPrewitt = Edge detection Y (Prewitt)
convolution.edgeDetectionXScharr = Edge detection X (Scharr)
convolution.edgeDetectionYScharr = Edge detection Y (Scharr)
convolution.laplacian = Laplacian
convolution.emboss = Emboss
convolution.outline = Outline
convolution.motionBlurX = Motion blur X
convolution.motionBlurY = Motion blur Y
convolution.highPass = High pass


property.instance.filters.menu.clipboard = Clipboard
property.instance.filters.menu.clipboard.copySelected = Copy selected
property.instance.filters.menu.clipboard.copyAll = Copy all
property.instance.filters.menu.clipboard.paste = Paste

property.linkXY = Link X and Y property values

property.instance.filters.strength = strength
property.instance.filters.blurX = blur X
property.instance.filters.blurY = blur Y
property.instance.filters.passes = passes
property.instance.filters.gradient = gradient
property.instance.filters.innerShadow = inner shadow
property.instance.filters.compositeSource = composite source
property.instance.filters.onTop = on top
property.instance.filters.dropShadowColor = drop shadow color
property.instance.filters.knockout = knockout
property.instance.filters.distance = distance
property.instance.filters.angle = angle
property.instance.filters.shadowColor = shadow color
property.instance.filters.highlightColor = highlight color
property.instance.filters.brightness = brightness
property.instance.filters.contrast = contrast
property.instance.filters.saturation = saturation
property.instance.filters.hue = hue
property.instance.filters.divisor = divisor
property.instance.filters.bias = bias
property.instance.filters.clamp = clamp
property.instance.filters.defaultColor = default color
property.instance.filters.preserveAlpha = preserve alpha
property.instance.filters.matrix = matrix
property.instance.filters.glowColor = glow color
property.instance.filters.innerGlow = inner glow

property.instance.filters.menu.enable = Enable or disable filter

property.instance.display.ratio = Ratio