# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Procesar todas las clases
dialog.title = Desofuscaci\u00f3n PCode
deobfuscation.level = Nivel de desofuscaci\u00f3n del c\u00f3digo:
deobfuscation.removedeadcode = Remover c\u00f3digo muerto
deobfuscation.removetraps = Remover trampas
deobfuscation.restorecontrolflow = Restaurar el flujo de control
button.ok = OK
button.cancel = Cancelar
