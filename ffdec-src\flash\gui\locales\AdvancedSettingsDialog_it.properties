# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Impostazioni avanzate
advancedSettings.restartConfirmation = Occorre riavviare il programma perch\u00e9 alcune modifiche abbiano effetto. Riavviare ora?
advancedSettings.columns.name = Nome
advancedSettings.columns.value = Valore
advancedSettings.columns.description = Descrizione
default = predefinito
config.group.name.export = Esportazione
config.group.description.export = Impostazioni di esportazione
config.group.name.script = Script
config.group.description.script = Relativi alla decompilazione ActionScript
config.group.name.update = Aggiornamenti
config.group.description.update = Controllo aggiornamenti
config.group.name.format = Formattazione
config.group.description.format = Formattazione del codice ActionScript
config.group.name.limit = Limiti
config.group.description.limit = Limiti nella decompilazione di codice offuscato, ecc.
config.group.name.ui = Interfaccia
config.group.description.ui = Configurazione interfaccia utente
config.group.name.debug = Debug
config.group.description.debug = Opzioni di debug
config.group.name.display = Visualizzazione
config.group.description.display = Visualizzazione oggetti Flash, ecc.
config.group.name.decompilation = Decompilazione
config.group.description.decompilation = Funzioni globali legate alla decompilazione
config.group.name.other = Varie
config.group.description.other = Altre impostazioni 
config.name.openMultipleFiles = Apertura file multipli
config.description.openMultipleFiles = Permette di aprire pi\u00f9 file contemporaneamente nella stessa finestra
config.name.decompile = Visualizza sorgenti ActionScript
config.description.decompile = Disattivando la decompilazione AS viene visualizzato solo il P-code
config.name.dumpView = Visualizza hex dump
config.description.dumpView = Mostra listato esadecimale dei byte nel file
config.name.useHexColorFormat = Formato esadecimale per i colori
config.description.useHexColorFormat = Presenta i colori in formato esadecimale
config.name.parallelSpeedUp = Elaborazione in parallelo
config.description.parallelSpeedUp = Il parallelismo pu\u00f2 ridurre i tempi di decompilazione
config.name.parallelSpeedUpThreadCount = Numero di thread (0 = auto)
config.description.parallelSpeedUpThreadCount = Numero di thread in esecuzione parallela. 0 = processor count - 1.
config.name.autoDeobfuscate = Deoffuscamento automatico
config.description.autoDeobfuscate = Tenta il deoffuscamento su ogni file prima della decompilazione ActionScript
config.name.cacheOnDisk = Utilizza cache su disco
config.description.cacheOnDisk = Memorizza le parti decompilate su disco anzich\u00e9 in memoria
config.name.internalFlashViewer = Utilizza visualizzatore Flash incorporato
config.description.internalFlashViewer = Utilizza JPEXS Flash Viewer invece del Flash Player standard per visualizzare le parti in Flash
config.name.gotoMainClassOnStartup = Vai alla classe principale all'avvio (AS3)
config.description.gotoMainClassOnStartup = Passa alla classe documento del file AS3 all'apertura del SWF
config.name.autoRenameIdentifiers = Autorinomina degli identificatori
config.description.autoRenameIdentifiers = Rinomina identificatori non validi al caricamento del SWF
config.name.offeredAssociation = (Interno) Associazione con file SWF visualizzato
config.description.offeredAssociation = Finestra di dialogo gi\u00e0 visualizzata
config.name.decimalAddress = Mostra indirizzi in decimale
config.description.decimalAddress = Usa indirizzi in base 10 (dec) invece che in base 16 (hex)
config.name.showAllAddresses = Mostra tutti gli indirizzi
config.description.showAllAddresses = Visualizza tutti gli indirizzi di istruzioni ActionScript
config.name.useFrameCache = Usa frame cache
config.description.useFrameCache = Fare caching dei frame prima di ridisegnarli
config.name.useRibbonInterface = Interfaccia con menu multifunzione (Ribbon)
config.description.useRibbonInterface = Deselezionare per usare la GUI classica
config.name.openFolderAfterFlaExport = Apri cartella dopo l'esportazione FLA
config.description.openFolderAfterFlaExport = Visualizza cartella di output dopo l'esportazione FLA
config.name.useDetailedLogging = Log dettagliato
config.description.useDetailedLogging = Log dettagliato dei messaggi di errore e di informazioni a scopo diagnostico
config.name._debugMode=FFDec in modalit\u00e0 diagnostica
config.description._debugMode=Modalit\u00e0 diagnostica per FFDec. Attiva il menu Debug. Non riguarda la funzionalita di debug
config.name.resolveConstants = Risolvere le costanti nel P-code AS1/2
config.description.resolveConstants = Disattivare per mostrare 'constantxx' invece dei valori effettivi nella vista P-code
config.name.sublimiter = Limite su subroutine
config.description.sublimiter = Limite su subroutine per codice offuscato.
config.name.exportTimeout = Tempo massimo di esportazione (secondi)
config.description.exportTimeout = Il decompilatore interromper\u00e0 l'esportazione dopo questo tempo
config.name.decompilationTimeoutFile = Tempo massimo di decompilazione per file (secondi)
config.description.decompilationTimeoutFile = Il decompilatore interromper\u00e0 la decompilazione ActionScript dopo questo tempo per un singolo file
config.name.paramNamesEnable = Abilita nomi di parametro in AS3
config.description.paramNamesEnable = Utilizzare i nomi di parametro nella decompilazione pu\u00f2 causare problemi poich\u00e9 programmi ufficiali come Flash CS 5.5 generano indici di nome errati
config.name.displayFileName = Mostra nome SWF nel titolo della finestra
config.description.displayFileName = Visualizza nome/URL del file SWF nel titolo (\u00e8 possibile fare screenshot)
config.name._debugCopy=Diagnostica ricompilazione FFDec
config.description._debugCopy=Prova a ricompilare il file SWF subito dopo l'apertura per garantire che generi lo stesso codice binario. Da usare SOLO per la diagnostica di FFDec!
config.name.dumpTags = Dump dei tag su console
config.description.dumpTags = Scrive i tag su console alla lettura del file SWF
config.name.decompilationTimeoutSingleMethod = AS3: Tempo massimo di decompilazione per singolo metodo (secondi)
config.description.decompilationTimeoutSingleMethod = Il decompilatore interromper\u00e0 la decompilazione ActionScript dopo questo tempo per un singolo metodo
config.name.lastRenameType = (Interno) Ultimo tipo di rinomina
config.description.lastRenameType = Ultimo tipo di rinomina identificatore utilizzato
config.name.lastSaveDir = (Interno) Ultima cartella di salvataggio
config.description.lastSaveDir = Ultima directory di salvataggio usata
config.name.lastOpenDir = (Interno) Ultima cartella aperta
config.description.lastOpenDir = Ultima directory aperta utilizzata
config.name.lastExportDir = (Interno) Ultima cartella di esportazione
config.description.lastExportDir = Ultima directory di esportazione usata
config.name.locale = Lingua
config.description.locale = Identificatore per la localizzazione
config.name.registerNameFormat = Formato delle variabili di registro
config.description.registerNameFormat = Formato nome delle variabili registro locali. Utilizzare %d per specificare il numero di registro.
config.name.maxRecentFileCount = Limite elenco file recenti
config.description.maxRecentFileCount = Numero massimo di file recenti
config.name.recentFiles = (Interno) File recenti
config.description.recentFiles = File aperti di recente
config.name.fontPairingMap = (Interno) Coppie di font per l'importazione
config.description.fontPairingMap = Coppie di font per l'importazione di nuovi caratteri
config.name.lastUpdatesCheckDate = (Interno) Data ultimo controllo aggiornamenti
config.description.lastUpdatesCheckDate = Data ultimo controllo aggiornamenti su server
config.name.gui.window.width = (Interno) Ultima larghezza finestra
config.description.gui.window.width = Ultima larghezza salvata della finestra
config.name.gui.window.height = (Interno) Ultima altezza della finestra
config.description.gui.window.height = Ultima altezza della finestra salvata
config.name.gui.window.maximized.horizontal = (Interno) Finestra ingrandita orizzontalmente
config.description.gui.window.maximized.horizontal = Ultima stato della finestra - massimizzata orizzontalmente
config.name.gui.window.maximized.vertical = (Interno) Finestra ingrandita verticalmente
config.description.gui.window.maximized.vertical = Ultima stato della finestra - massimizzata verticalmente
config.name.gui.avm2.splitPane.dividerLocationPercent=(Interno) Posizione divisorio AS3
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Interno) Posizione divisorio AS1/2
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Interno) Posizione divisorio dell'anteprima
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Interno) Posizione 1 divisorio
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Interno) Posizione 2 divisorio
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Modalit\u00e0 scala per creazione EXE
config.description.saveAsExeScaleMode = Modalit\u00e0 di scala per esportazione EXE
config.name.syntaxHighlightLimit = Numero massimo caratteri evidenziamento sintassi
config.description.syntaxHighlightLimit = Numero massimo di caratteri su cui eseguire evidenziamento sintattico
config.name.guiFontPreviewSampleText = (Interno) Ultimo testo di esempio per anteprima font
config.description.guiFontPreviewSampleText = Indice lista ultimo testo di esempio per anteprima font
config.name.gui.fontPreviewWindow.width = (Interno) Ultima larghezza finestra di anteprima font
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Interno) Ultima altezza finestra di anteprima font
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Interno) Ultima ascissa X finestra di anteprima font
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Interno) Ultima ordinata Y finestra di anteprima font
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Numero caratteri per indentazione
config.description.formatting.indent.size = Numero di spazi (o tab) per un rientro
config.name.formatting.indent.useTabs = Indentare con tab
config.description.formatting.indent.useTabs = Utilizzare tab invece degli spazi per l'indentazione
config.name.beginBlockOnNewLine = Parentesi graffa su riga successiva
config.description.beginBlockOnNewLine = Iniziare un blocco di parentesi graffe dopo un a-capo
config.name.check.updates.delay = Intervallo tra controllo aggiornamenti
config.description.check.updates.delay = Tempo minimo tra controlli automatici per aggiornamenti all'avvio
config.name.check.updates.stable = Cercare versioni stabili
config.description.check.updates.stable = Cercare aggiornamenti di versione stabili
config.name.check.updates.nightly = Cercare versioni di sviluppo (nightly)
config.description.check.updates.nightly = Controllo aggiornamenti per versioni di sviluppo
config.name.check.updates.enabled = Attiva controllo aggiornamenti
config.description.check.updates.enabled = Controllo aggiornamenti automatico all'avvio
config.name.export.formats = (Interno) Formati di esportazione
config.description.export.formats = Ultimi formati di esportazione utilizzati
config.name.textExportSingleFile = Esportare i testi in un file unico
config.description.textExportSingleFile = Esporta testi verso un file unico invece che diversi
config.name.textExportSingleFileSeparator = Separatore di testo per esportazione test verso file unico
config.description.textExportSingleFileSeparator = Stringa da inserire tra i testi nel file di esportazione
config.name.textExportSingleFileRecordSeparator = Separatore di record nel file unico di esportazione testo
config.description.textExportSingleFileRecordSeparator = Stringa da inserire tra i record di testo nel file di esportazione
config.name.warning.experimental.as12edit=Avvisa su modifiche dirette AS1/2
config.description.warning.experimental.as12edit=Mostra avviso su modifica diretta sperimentale ad AS1/2 
config.name.warning.experimental.as3edit=Avvisa su modifiche dirette AS3
config.description.warning.experimental.as3edit=Mostra avviso su modifica diretta sperimentale AS3
config.name.packJavaScripts = Compatta il codice JavaScript
config.description.packJavaScripts = Esegue il JavaScript packer su script creati su esportazione del canvas.
config.name.textExportExportFontFace = Utilizzare font-face nell'esportazione SVG 
config.description.textExportExportFontFace = Incorpora font in SVG utilizzando font-face invece che le forme
config.name.lzmaFastBytes = Byte LZMA veloci (valori validi: 5-255)
config.description.lzmaFastBytes = Parametro byte veloci del codificatore LZMA
config.name.pluginPath = Percorso Plugin
config.description.pluginPath = -
config.name.showMethodBodyId = Mostra id corpo del metodo
config.description.showMethodBodyId = Mostra id del corpo del metodo per importazione da riga di comando
config.name.export.zoom = (Interno) Zoom per esportazione
config.description.export.zoom = Ultimo zoom usato per l'esportazione
config.name.debuggerPort = Porta del debugger
config.description.debuggerPort = Numero di porta usato per il socket debugging
config.name.displayDebuggerInfo = (Interno) Mostra info debugger
config.description.displayDebuggerInfo = Visualizzare informazioni sul debugger prima di attivarlo
config.name.randomDebuggerPackage = Utilizzare un nome package casuale per il Debugger
config.description.randomDebuggerPackage = Rinomina casualmente il package del Debugger per meglio occultare la sua presenza al codice ActionScript
config.name.lastDebuggerReplaceFunction = (Interno) Ultima sostituzione di trace selezionata
config.description.lastDebuggerReplaceFunction = Nome dell'ultima funzione selezionata per sostituire la funzione di trace con debugger
config.name.getLocalNamesFromDebugInfo = AS3: Prendere i nomi di registro locali dalle informazioni di debug
config.description.getLocalNamesFromDebugInfo = Se sono presenti informazioni di debug, rinomina i registri locali da _loc_x_ ai nomi reali.\r\nQuesto pu\u00f2 essere disattivato perch\u00e9 alcuni offuscatori usano nomi di registro non validi.
config.name.tagTreeShowEmptyFolders = Visualizza cartelle vuote
config.description.tagTreeShowEmptyFolders = Visualizza cartelle vuote nell'albero dei tag
config.name.autoLoadEmbeddedSwfs = Caricamento automatico SWF incorporati
config.description.autoLoadEmbeddedSwfs = Carica in automatico i file SWF incorporati con i tag DefineBinaryData
config.name.overrideTextExportFileName = Personalizza nome file di esportazione testo
config.description.overrideTextExportFileName = \u00c8 possibile personalizzare il nome del file di testo esportato.\r\nUsare il segnaposto {filename} per indicare il nome del file SWF corrente
config.name.showOldTextDuringTextEditing = Mostra vecchio testo durante la modifica
config.description.showOldTextDuringTextEditing = Mostra il testo originale della variabile in grigio nell'area di anteprima
config.group.name.import = Importazione
config.group.description.import = Impostazioni di importazione
config.name.textImportResizeTextBoundsMode = Modalit\u00e0 ridimensionamento margini di testo 
config.description.textImportResizeTextBoundsMode = Modalit\u00e0 ridimensionamento limiti di testo dopo la modifica del testo
config.name.showCloseConfirmation = Mostra conferma chiusura SWF
config.description.showCloseConfirmation = Mostra conferma chiusura SWF per i file modificati.
config.name.showCodeSavedMessage = Mostra avviso salvataggio codice
config.description.showCodeSavedMessage = Mostra avviso salvataggio codice
config.name.showTraitSavedMessage = Mostra avviso salvataggio trait
config.description.showTraitSavedMessage = Mostra avviso salvataggio trait
config.name.updateProxyAddress = Indirizzo http proxy per il controllo degli aggiornamenti
config.description.updateProxyAddress = Indirizzo http proxy per il controllo degli aggiornamenti. Formato: example.com:8080
config.name.editorMode = Modalit\u00e0 editor
config.description.editorMode = Rende modificabili le aree di testo in automatico alla selezione di un nodo text o script
config.name.autoSaveTagModifications = Salvataggio automatico modifiche ai tag
config.description.autoSaveTagModifications = Salva le modifiche quando si seleziona un nuovo tag nella struttura
config.name.saveSessionOnExit = Salva sessione in uscita
config.description.saveSessionOnExit = Salva la sessione corrente e la riapre dopo il riavvio di FFDec (funziona solo con file reali)
config.name._showDebugMenu=Mostra il menu diagnostico di FFDec
config.description._showDebugMenu=Mostra il menu Debug nel Ribbon per la diagnostica del decompilatore.
config.name.allowOnlyOneInstance = Permetti una sola istanza di FFDec (sotto Windows)
config.description.allowOnlyOneInstance = FFDec pu\u00f2 essere avviato solo una volta, tutti i file aperti verranno aggiunti alla stessa finestra.\nFunziona solo sotto sistema operativo Windows
config.name.scriptExportSingleFile = Esporta script verso un unico file
config.description.scriptExportSingleFile = Esportazione script verso un unico file invece che file multipli
config.name.setFFDecVersionInExportedFont = Imposta il numero di versione di FFDec nel font esportato
config.description.setFFDecVersionInExportedFont = Disattivando questa impostazione FFDec non aggiunge il numero di versione di FFDec al font esportato.
config.name.gui.skin = Aspetto interfaccia utente
config.description.gui.skin = Aspetto
config.name.lastSessionFiles = File ultima sessione
config.description.lastSessionFiles = Contiene i file aperti nell'ultima sessione
config.name.lastSessionSelection = Selezione ultima sessione
config.description.lastSessionSelection = Contiene la selezione dell'ultima sessione
config.name.loopMedia = Riproduzione ciclica suoni e sprite
config.description.loopMedia = Riproduce ciclicamente audio e animazioni
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Interno) Posizione divisorio del timeline
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Cache immagini
config.description.cacheImages = Caching degli oggetti immagine decodificati
config.name.swfSpecificConfigs = Configurazioni specifiche SWF
config.description.swfSpecificConfigs = Contiene le configurazioni specifiche agli SWF
config.name.exeExportMode = Modalit\u00e0 di esportazione EXE
config.description.exeExportMode = Modalit\u00e0 di esportazione EXE
config.name.ignoreCLikePackages = Ignora FlashCC / Alchemy o package simili
config.description.ignoreCLikePackages = I package FlashCC / Alchemy non possono solitamente essere decompilati correttamente.\r\n\u00c8 possibile disattivarli per velocizzare la decompilazione di altri package.
config.name.overwriteExistingFiles = Sovrascrivere i file esistenti
config.description.overwriteExistingFiles = Sovrascrivere i file esistenti durante l'esportazione. Attualmente solo per script AS2/3 
config.name.smartNumberFormatting = Usa formattazione intelligente dei numeri
config.description.smartNumberFormatting = Formatta numeri speciali (ad esempio colori e istanti temporali)
config.name.enableScriptInitializerDisplay = (REMOVED) Visualizza inizializzatori script
config.description.enableScriptInitializerDisplay = Abilita inizializzatori script e modifiche. Questa impostazione pu\u00f2 aggiungere un accapo a ciascuna classe per evidenziare.
config.name.autoOpenLoadedSWFs = Apri SWF caricati durante l'esecuzione (visualizzatore esterno = solo su Windows)
config.description.autoOpenLoadedSWFs = Apri in automatico tutti gli SWF caricati dalla classe AS3 Loader eseguendo lo SWF quando riprodotto nel player esterno di FFDec. Disponibile solo su Windows.
config.name.lastSessionFileTitles = Titoli file ultima sessione
config.description.lastSessionFileTitles = Contiene i titoli dei file aperti nell'ultima sessione (ad es. quelli caricati da URL ecc.)
config.group.name.paths = Percorsi
config.group.description.paths = Ubicazione dei file richiesti
#config.group.tip.paths =  possibile ottenere questi file dal sito Adobe
#TODO: translate again:
config.group.tip.paths = Download projector and Playerglobal on <a href="%link1%">adobe webpage</a>. Flex SDK can be downloaded on <a href="%link2%">apache web</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Percorso Flash Player
config.description.playerLocation = Posizione eseguibile Flash Player. Utilizzato per l'azione Esegui.
config.name.playerDebugLocation = 2) Percorso Flash Player per il debug
config.description.playerDebugLocation = Posizione eseguibile Flash Player per il debug. Utilizzato per l'azione Debug.
config.name.playerLibLocation = 3) Percorso PlayerGlobal (.swc)
config.description.playerLibLocation = Posizione della libreria playerglobal.swc utilizzata perlopi\u00f9 per compilare AS3.
config.name.debugHalt = Pausa esecuzione all'avvio del debug
config.description.debugHalt = Pausa SWF all'avvio del debug.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Interno) Posizione divisorio del menu Debug
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Suggerimento: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Interno) Posizione divisorio del menu Debug AS1/2
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = Ritardo in ms prima di cambiare il file SWF nel riproduttore esterno
config.description.setMovieDelay = Si sconsigliano valori inferiori a 1000ms
config.name.warning.svgImport = Avvisa all'importazione SVG
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Utilizza riempimento non smussato nel sostituire una forma con un'immagine
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=AS1/2 nel visualizzatore Flash interno (Sperimentale)
config.description.internalFlashViewer.execute.as12=Tenta esecuzione codice ActionScript 1/2 nella riproduzione di SWF utilizzando il visualizzatore Flash FFDec
config.name.warning.hexViewNotUpToDate = Avvisa se Hex View non aggiornata
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = Mostra istruzioni \u00a7\u00a7dup
config.description.displayDupInstructions = Visualizza istruzioni \u00a7\u00a7dup nel codice. In loro assenza il codice pu\u00f2 essere facilmente compilato ma i dup con effetti collaterali potrebbero essere eseguiti due volte.
config.name.useRegExprLiteral = Decompila RegExp nella forma /pattern/mod.
config.description.useRegExprLiteral = Usa la sintassi /pattern/mod nella decompilazione di espressioni regolari. Altrimenti, utilizza new RegExp(\"pat\",\"mod\")
config.name.handleSkinPartsAutomatically = Gestisce metadati [SkinPart] in automatico
config.description.handleSkinPartsAutomatically = Decompila e modifica direttamente i metadati [SkinPart] in automatico. Se disattivato, l'attributo _skinParts ed il suo metodo getter sono visibili e modificabili dall'utente.
config.name.simplifyExpressions = Semplifica le espressioni
config.description.simplifyExpressions = Valuta and semplifica le espressioni per rendere il codice piu leggibile
config.name.resetLetterSpacingOnTextImport = Ripristina spaziatura lettere all'importazione di testo
config.description.resetLetterSpacingOnTextImport = Utile per i font cirillici, essendo piu ampi
