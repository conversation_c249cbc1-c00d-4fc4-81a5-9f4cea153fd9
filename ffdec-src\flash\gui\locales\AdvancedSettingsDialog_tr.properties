# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Geli\u015fmi\u015f Ayarlar
advancedSettings.restartConfirmation = Baz\u0131 de\u011fi\u015fikliklerin etkili olmas\u0131 i\u00e7in program\u0131 yeniden ba\u015flatman\u0131z gerekir. \u015eimdi yeniden ba\u015flatmak istiyor musunuz?
advancedSettings.columns.name = Ad
advancedSettings.columns.value = De\u011fer
advancedSettings.columns.description = A\u00e7\u0131klama
default = varsay\u0131lan
config.group.name.export = D\u0131\u015fa aktar
config.group.description.export = D\u0131\u015fa aktarma yap\u0131land\u0131rmas\u0131
config.group.name.script = Senaryolar
config.group.description.script = ActionScript ayr\u0131\u015ft\u0131rmas\u0131 ile ilgili
config.group.name.update = G\u00fcncellemeler
config.group.description.update = G\u00fcncellemeleri kontrol et
config.group.name.format = Bi\u00e7imlendirme
config.group.description.format = ActionScript kod bi\u00e7imlendirmesi
config.group.name.limit = S\u0131n\u0131rlar
config.group.description.limit = Karart\u0131lm\u0131\u015f kod i\u00e7in ayr\u0131\u015ft\u0131rma s\u0131n\u0131rlar\u0131, vb.
config.group.name.ui = Aray\u00fcz
config.group.description.ui = Kullan\u0131c\u0131 aray\u00fcz\u00fc yap\u0131land\u0131rmas\u0131
config.group.name.debug = Hata ay\u0131kla
config.group.description.debug = Hata ay\u0131klama ayarlar\u0131
config.group.name.display = G\u00f6r\u00fcnt\u00fcle
config.group.description.display = Flash nesneleri g\u00f6r\u00fcnt\u00fcleme, vb.
config.group.name.decompilation = Ayr\u0131\u015ft\u0131rma
config.group.description.decompilation = K\u00fcresel ayr\u0131\u015ft\u0131rma ile ilgili i\u015flevler
config.group.name.other = Di\u011fer
config.group.description.other = Di\u011fer kategorize edilmemi\u015f yap\u0131land\u0131rmalar
config.name.openMultipleFiles = Birden \u00e7ok dosya a\u00e7
config.description.openMultipleFiles = Bir pencerede ayn\u0131 anda birden fazla dosya a\u00e7maya izin verir
config.name.decompile = ActionScript kayna\u011f\u0131n\u0131 g\u00f6ster
config.description.decompile = AS ayr\u0131\u015ft\u0131rmas\u0131n\u0131 devre d\u0131\u015f\u0131 b\u0131rakabilirsiniz, o zaman sadece P kodu g\u00f6sterilir
config.name.dumpView = D\u00f6k\u00fcm G\u00f6r\u00fcn\u00fcm\u00fc
config.description.dumpView = Ham veri d\u00f6k\u00fcm\u00fcn\u00fc g\u00f6r\u00fcnt\u00fcle
config.name.useHexColorFormat = Onalt\u0131l\u0131k renk bi\u00e7imi
config.description.useHexColorFormat = Renkleri onalt\u0131l\u0131k bi\u00e7imde g\u00f6ster
config.name.parallelSpeedUp = Paralel H\u0131zland\u0131rma
config.description.parallelSpeedUp = Paralellik, ayr\u0131\u015ft\u0131rmay\u0131 h\u0131zland\u0131rabilir
config.name.parallelSpeedUpThreadCount = \u0130\u015f par\u00e7ac\u0131\u011f\u0131 say\u0131s\u0131 (0 = auto)
config.description.parallelSpeedUpThreadCount = Paralel h\u0131zland\u0131rma i\u00e7in i\u015f par\u00e7ac\u0131\u011f\u0131 say\u0131s\u0131. 0 = processor count - 1.
config.name.autoDeobfuscate = Otomatik a\u00e7\u0131\u011fa \u00e7\u0131karma
config.description.autoDeobfuscate = ActionScript ayr\u0131\u015ft\u0131rmas\u0131ndan \u00f6nce a\u00e7\u0131\u011fa \u00e7\u0131karma \u00f6\u011fesini her dosyada \u00e7al\u0131\u015ft\u0131r\u0131n
config.name.cacheOnDisk = Diskte \u00f6nbelle\u011fe almay\u0131 kullan
config.description.cacheOnDisk = \u00d6nceden ayr\u0131\u015ft\u0131r\u0131lm\u0131\u015f par\u00e7alar\u0131 bellek yerine sabit diskte \u00f6nbelle\u011fe al\u0131n
config.name.internalFlashViewer = Kendi Fla\u015f g\u00f6r\u00fcnt\u00fcleyicisini kullan
config.description.internalFlashViewer = Flash par\u00e7alar\u0131 g\u00f6r\u00fcnt\u00fclemek i\u00e7in standart Flash Player yerine JPEXS Flash Viewer kullan\u0131n
config.name.gotoMainClassOnStartup = Ba\u015flang\u0131\u00e7ta ana s\u0131n\u0131fa git (AS3)
config.description.gotoMainClassOnStartup = SWF a\u00e7\u0131ld\u0131\u011f\u0131nda AS3 dosyas\u0131n\u0131n belge s\u0131n\u0131f\u0131na gider
config.name.autoRenameIdentifiers = Otomatik yeniden adland\u0131rma tan\u0131mlay\u0131c\u0131lar\u0131
config.description.autoRenameIdentifiers = SWF y\u00fcklemesinde ge\u00e7ersiz tan\u0131mlay\u0131c\u0131lar\u0131 otomatik olarak yeniden adland\u0131r\u0131n
config.name.offeredAssociation = (Dahili) G\u00f6r\u00fcnt\u00fclenen SWF dosyalar\u0131 ile ili\u015fkilendirme
config.description.offeredAssociation = Dosya ili\u015fkilendirmesi hakk\u0131nda ileti\u015fim kutusu zaten g\u00f6r\u00fcnt\u00fclendi
config.name.decimalAddress = Ondal\u0131k adresleri kullan
config.description.decimalAddress = Onalt\u0131l\u0131k yerine ondal\u0131k adresler kullan\u0131n
config.name.showAllAddresses = T\u00fcm adresleri g\u00f6ster
config.description.showAllAddresses = T\u00fcm ActionScript komut adreslerini g\u00f6r\u00fcnt\u00fcle
config.name.useFrameCache = \u00c7er\u00e7eve \u00f6nbelle\u011fini kullan
config.description.useFrameCache = \u00c7er\u00e7eveleri yeniden olu\u015fturmadan \u00f6nce \u00f6nbelle\u011fe al\u0131n
config.name.useRibbonInterface = \u015eerit aray\u00fcz\u00fc
config.description.useRibbonInterface = \u015eerit men\u00fc olmadan klasik aray\u00fcz\u00fc kullanmak i\u00e7in i\u015fareti kald\u0131r\u0131n
config.name.openFolderAfterFlaExport = FLA d\u0131\u015fa aktar\u0131m\u0131ndan sonra klas\u00f6r\u00fc a\u00e7
config.description.openFolderAfterFlaExport = FLA dosyas\u0131n\u0131 d\u0131\u015fa aktard\u0131ktan sonra \u00e7\u0131kt\u0131 dizinini g\u00f6r\u00fcnt\u00fcle
config.name.useDetailedLogging = FFDec ayr\u0131nt\u0131l\u0131 G\u00fcnl\u00fck Kayd\u0131
config.description.useDetailedLogging = FFDec'in hata ay\u0131klamas\u0131 i\u00e7in ayr\u0131nt\u0131l\u0131 hata mesajlar\u0131n\u0131 ve bilgileri g\u00fcnl\u00fc\u011fe kaydedin
config.name._debugMode = FFDec hata ay\u0131klama modunda
config.description._debugMode = FFDec hata ay\u0131klama modu. Hata ay\u0131klama men\u00fcs\u00fcn\u00fc a\u00e7ar. Bunun hata ay\u0131klay\u0131c\u0131 i\u015flevselli\u011fi ile hi\u00e7bir ilgisi yoktur
config.name.resolveConstants = AS1/2 p kodundaki sabitleri \u00e7\u00f6z
config.description.resolveConstants = P kodu penceresinde ger\u00e7ek de\u011ferler yerine 'sabitxx' g\u00f6stermek i\u00e7in bunu kapat\u0131n
config.name.sublimiter = Kod alt s\u0131n\u0131r\u0131
config.description.sublimiter = Karart\u0131lm\u0131\u015f kod i\u00e7in kod alt s\u0131n\u0131r\u0131.
config.name.exportTimeout = Toplam d\u0131\u015fa aktarma zaman a\u015f\u0131m\u0131 (saniye)
config.description.exportTimeout = Decompiler bu s\u00fcreye ula\u015ft\u0131ktan sonra d\u0131\u015fa aktarmay\u0131 durduracakt\u0131r
config.name.decompilationTimeoutFile = Tek dosya ayr\u0131\u015ft\u0131rma zaman a\u015f\u0131m\u0131 (saniye)
config.description.decompilationTimeoutFile = Decompiler, tek bir dosyada bu s\u00fcreye ula\u015ft\u0131ktan sonra ActionScript ayr\u0131\u015ft\u0131rmas\u0131n\u0131 durduracakt\u0131r
config.name.paramNamesEnable = AS3'te parametre adlar\u0131n\u0131 etkinle\u015ftir
config.description.paramNamesEnable = Flash CS 5.5 gibi resmi programlar yanl\u0131\u015f parametre adlar\u0131 indeksleri ekledi\u011finden ayr\u0131\u015ft\u0131rma i\u015fleminde parametre adlar\u0131n\u0131n kullan\u0131lmas\u0131 sorunlara neden olabilir
config.name.displayFileName = SWF ad\u0131n\u0131 ba\u015fl\u0131kta g\u00f6ster
config.description.displayFileName = SWF dosya/url ad\u0131n\u0131 pencere ba\u015fl\u0131\u011f\u0131nda g\u00f6r\u00fcnt\u00fcleyin (Daha sonra ekran g\u00f6r\u00fcnt\u00fcs\u00fc alabilirsiniz)
config.name._debugCopy = FFDec hata ay\u0131klama yeniden derlemesi
config.description._debugCopy = Ayn\u0131 ikili kodu \u00fcretti\u011finden emin olmak i\u00e7in SWF dosyas\u0131n\u0131 a\u00e7t\u0131ktan hemen sonra yeniden derlemeye \u00e7al\u0131\u015f\u0131r. Sadece FFDec'i HATA AYIKLAMA i\u00e7in kullan\u0131n!
config.name.dumpTags = Etiketleri konsola d\u00f6k
config.description.dumpTags = SWF dosyas\u0131 okunurken etiketlerin konsola d\u00f6k
config.name.decompilationTimeoutSingleMethod = AS3: Tek y\u00f6ntemli ayr\u0131\u015ft\u0131rma zaman a\u015f\u0131m\u0131 (saniye)
config.description.decompilationTimeoutSingleMethod = Decompiler, bir y\u00f6ntemde bu s\u00fcreye ula\u015ft\u0131ktan sonra ActionScript ayr\u0131\u015ft\u0131rmas\u0131n\u0131 durduracakt\u0131r
config.name.lastRenameType = (Dahili) Son yeniden adland\u0131rma t\u00fcr\u00fc
config.description.lastRenameType = Son kullan\u0131lan yeniden adland\u0131rma tan\u0131mlay\u0131c\u0131lar\u0131 t\u00fcr\u00fc
config.name.lastSaveDir = (Dahili) Son kay\u0131t dizini
config.description.lastSaveDir = Son kullan\u0131lan kay\u0131t dizini
config.name.lastOpenDir = (Dahili) Son a\u00e7\u0131k dizin
config.description.lastOpenDir = Son kullan\u0131lan a\u00e7\u0131k dizin
config.name.lastExportDir = (Dahili) son d\u0131\u015fa aktarma dizini
config.description.lastExportDir = Son kullan\u0131lan d\u0131\u015fa aktarma dizini
config.name.locale = Dil
config.description.locale = Yereller tan\u0131mlay\u0131c\u0131s\u0131
config.name.registerNameFormat = Kay\u0131t de\u011fi\u015fkeni bi\u00e7imi
config.description.registerNameFormat = Yerel kay\u0131t de\u011fi\u015fkeni adlar\u0131n\u0131n bi\u00e7imi. Kay\u0131t numaras\u0131 i\u00e7in %d kullan\u0131n.
config.name.maxRecentFileCount = Maksimum son say\u0131
config.description.maxRecentFileCount = Son dosyalar\u0131n maksimum say\u0131s\u0131
config.name.recentFiles = (Dahili) Son dosyalar
config.description.recentFiles = Son a\u00e7\u0131lan dosyalar
config.name.fontPairingMap = (Dahili) \u0130\u00e7e aktarma i\u00e7in yaz\u0131 tipi \u00e7iftleri
config.description.fontPairingMap = Yeni karakterleri i\u00e7e aktarmak i\u00e7in yaz\u0131 tipi \u00e7iftleri
config.name.lastUpdatesCheckDate = (Dahili) Son g\u00fcncelleme kontrol tarihi
config.description.lastUpdatesCheckDate = Sunucuda g\u00fcncellemeler i\u00e7in son kontrol tarihi
config.name.gui.window.width = (Dahili) Son pencere geni\u015fli\u011fi
config.description.gui.window.width = Son kaydedilen pencere geni\u015fli\u011fi
config.name.gui.window.height = (Dahili) Son pencere y\u00fcksekli\u011fi
config.description.gui.window.height = Son kaydedilen pencere y\u00fcksekli\u011fi
config.name.gui.window.maximized.horizontal = (Dahili) Pencere yatay olarak maksimize edildi
config.description.gui.window.maximized.horizontal = Son pencere durumu - yatay olarak maksimize edildi
config.name.gui.window.maximized.vertical = (Dahili) Pencere dikey olarak maksimize edildi
config.description.gui.window.maximized.vertical = Son pencere durumu - dikey olarak maksimize edildi
config.name.gui.avm2.splitPane.dividerLocationPercent = (Dahili) AS3 Ay\u0131r\u0131c\u0131 konumu
config.description.gui.avm2.splitPane.dividerLocationPercent = 
config.name.gui.actionSplitPane.dividerLocationPercent = (Dahili) AS1/2 ay\u0131r\u0131c\u0131 konumu
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Dahili) \u00d6nizleme ay\u0131r\u0131c\u0131 konumu
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent = (Dahili) Ay\u0131r\u0131c\u0131 konumu 1
config.description.gui.splitPane1.dividerLocationPercent = 
config.name.gui.splitPane2.dividerLocationPercent = (Dahili) Ay\u0131r\u0131c\u0131 konumu 2
config.description.gui.splitPane2.dividerLocationPercent = 
config.name.saveAsExeScaleMode = EXE olarak kaydetme \u00f6l\u00e7ek modu
config.description.saveAsExeScaleMode = EXE d\u0131\u015fa aktar\u0131m\u0131 i\u00e7in \u00f6l\u00e7ekleme modu
config.name.syntaxHighlightLimit = S\u00f6zdizimi vurgulama maksimum karakter say\u0131s\u0131
config.description.syntaxHighlightLimit = S\u00f6zdizimi vurgulaman\u0131n \u00e7al\u0131\u015ft\u0131r\u0131laca\u011f\u0131 maksimum karakter say\u0131s\u0131
config.name.guiFontPreviewSampleText = (Dahili) Son yaz\u0131 tipi \u00f6nizleme \u00f6rnek metni
config.description.guiFontPreviewSampleText = Son yaz\u0131 tipi \u00f6nizlemesi \u00f6rnek metin listesi dizini
config.name.gui.fontPreviewWindow.width = (Dahili) Son yaz\u0131 tipi \u00f6nizleme penceresinin geni\u015fli\u011fi
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Dahili) Son yaz\u0131 tipi \u00f6nizleme penceresinin y\u00fcksekli\u011fi
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Dahili) Son yaz\u0131 tipi \u00f6nizleme penceresi X
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Dahili) Son yaz\u0131 tipi \u00f6nizleme penceresi Y
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Girinti ba\u015f\u0131na karakterler
config.description.formatting.indent.size = Bir girinti i\u00e7in bo\u015fluk (veya sekme) say\u0131s\u0131
config.name.formatting.indent.useTabs = Girinti i\u00e7in sekmeler
config.description.formatting.indent.useTabs = Girinti i\u00e7in bo\u015fluk yerine sekmeleri kullan\u0131n
config.name.beginBlockOnNewLine = Yeni sat\u0131rda k\u00fcme parantez
config.description.beginBlockOnNewLine = Blo\u011fu yeni sat\u0131rda k\u00fcme paranteziyle ba\u015flat\u0131n
config.name.check.updates.delay = G\u00fcncelleme kontrol\u00fc gecikmesi
config.description.check.updates.delay = Uygulama ba\u015flang\u0131c\u0131nda g\u00fcncellemeler i\u00e7in otomatik kontroller aras\u0131ndaki minimum s\u00fcre
config.name.check.updates.stable = Kararl\u0131 s\u00fcr\u00fcmleri kontrol et
config.description.check.updates.stable = Kararl\u0131 s\u00fcr\u00fcm g\u00fcncellemelerini kontrol etme
config.name.check.updates.nightly = Gece s\u00fcr\u00fcmlerini kontrol et
config.description.check.updates.nightly = Gece s\u00fcr\u00fcm g\u00fcncellemelerini kontrol etme
config.name.check.updates.enabled = G\u00fcncellemelerin kontrol\u00fc etkin
config.description.check.updates.enabled = Uygulama ba\u015flang\u0131c\u0131nda g\u00fcncellemeler i\u00e7in otomatik kontrol etme
config.name.export.formats = (Dahili) D\u0131\u015fa aktarma bi\u00e7imleri
config.description.export.formats = Son kullan\u0131lan d\u0131\u015fa aktarma bi\u00e7imleri
config.name.textExportSingleFile = Metinleri tek bir dosyaya aktar
config.description.textExportSingleFile = Metinleri birden fazla dosya yerine tek bir dosyaya aktarma
config.name.textExportSingleFileSeparator = Tek bir dosya metin d\u0131\u015fa aktar\u0131m\u0131ndaki metinlerin ay\u0131r\u0131c\u0131s\u0131
config.description.textExportSingleFileSeparator = Tek dosya metin d\u0131\u015fa aktar\u0131m\u0131nda metinler aras\u0131na eklenecek metinmetin
config.name.textExportSingleFileRecordSeparator = Tek biir dosya metin d\u0131\u015fa aktar\u0131m\u0131ndaki kay\u0131tlar\u0131n ay\u0131r\u0131c\u0131s\u0131
config.description.textExportSingleFileRecordSeparator = Tek dosya metin d\u0131\u015fa aktar\u0131m\u0131nda metin kay\u0131tlar\u0131 aras\u0131na eklenecek metin
config.name.warning.experimental.as12edit = AS1/2 do\u011frudan d\u00fczenlemesinde uyar
config.description.warning.experimental.as12edit = AS1/2 deneysel do\u011frudan d\u00fczenlemesinde uyar\u0131 g\u00f6ster
config.name.warning.experimental.as3edit = AS3 do\u011frudan d\u00fczenlemesinde uyar
config.description.warning.experimental.as3edit = AS3 deneysel do\u011frudan d\u00fczenlemesinde uyar\u0131 g\u00f6ster
config.name.packJavaScripts = JavaScripts Paketi
config.description.packJavaScripts = Canvas Export'da olu\u015fturulan komut dosyalar\u0131nda JavaScript paketleyiciyi \u00e7al\u0131\u015ft\u0131r\u0131n.
config.name.textExportExportFontFace = SVG d\u0131\u015fa aktarmada yaz\u0131 tipi y\u00fcz\u00fcn\u00fc kullan
config.description.textExportExportFontFace = \u015eekiller yerine yaz\u0131 tipi y\u00fcz\u00fcn\u00fc kullanarak yaz\u0131 tipi dosyalar\u0131n\u0131 SVG'ye g\u00f6m\u00fcn
config.name.lzmaFastBytes = LZMA h\u0131zl\u0131 baytlar\u0131 (ge\u00e7erli de\u011ferler: 5-255)
config.description.lzmaFastBytes = LZMA kodlay\u0131c\u0131n\u0131n h\u0131zl\u0131 bayt parametresi
config.name.pluginPath = Eklenti Yolu
config.description.pluginPath = -
config.name.showMethodBodyId = Y\u00f6ntem g\u00f6vdesi kimli\u011fini g\u00f6ster
config.description.showMethodBodyId = Komut sat\u0131r\u0131 i\u00e7e aktar\u0131m\u0131 i\u00e7in y\u00f6ntem g\u00f6vdesinin kimli\u011fini g\u00f6sterir
config.name.export.zoom = (Dahili) Yak\u0131nla\u015ft\u0131rmay\u0131 d\u0131\u015fa aktar
config.description.export.zoom = Son kullan\u0131lan d\u0131\u015fa aktarma yak\u0131nla\u015ft\u0131rmas\u0131
config.name.debuggerPort = Hata ay\u0131klay\u0131c\u0131 ba\u011flant\u0131 noktas\u0131
config.description.debuggerPort = Soket hata ay\u0131klamas\u0131 i\u00e7in kullan\u0131lan ba\u011flant\u0131 noktas\u0131
config.name.displayDebuggerInfo = (Dahili) Hata ay\u0131klay\u0131c\u0131 bilgisi g\u00f6r\u00fcnt\u00fcle
config.description.displayDebuggerInfo = De\u011fi\u015ftirmeden \u00f6nce hata ay\u0131klay\u0131c\u0131 hakk\u0131nda bilgi g\u00f6r\u00fcnt\u00fcle
config.name.randomDebuggerPackage = Hata Ay\u0131klay\u0131c\u0131 i\u00e7in rastgele paket ad\u0131 kullan
config.description.randomDebuggerPackage = Bu, Hata Ay\u0131klay\u0131c\u0131 paketini rastgele bir dizeye yeniden adland\u0131rarak hata ay\u0131klay\u0131c\u0131n\u0131n varl\u0131\u011f\u0131n\u0131n ActionScript taraf\u0131ndan alg\u0131lanmas\u0131n\u0131 zorla\u015ft\u0131r\u0131r
config.name.lastDebuggerReplaceFunction = (Dahili) Son se\u00e7ilen iz de\u011fi\u015fimi
config.description.lastDebuggerReplaceFunction = Hata ay\u0131klay\u0131c\u0131 ile izleme i\u015flevini de\u011fi\u015ftir \u00fczerinde en son se\u00e7ilen i\u015flev ad\u0131
config.name.getLocalNamesFromDebugInfo = AS3: Hata ay\u0131klama bilgilerinden yerel kay\u0131t adlar\u0131n\u0131 al
config.description.getLocalNamesFromDebugInfo = Hata ay\u0131klama bilgisi mevcutsa, yerel kay\u0131tlar\u0131 _loc_x_'dan ger\u00e7ek adlara yeniden adland\u0131r\u0131r. Baz\u0131 karart\u0131c\u0131lar orada ge\u00e7ersiz kay\u0131t adlar\u0131 kulland\u0131\u011f\u0131ndan bu kapat\u0131labilir.
config.name.tagTreeShowEmptyFolders = Bo\u015f klas\u00f6rleri g\u00f6ster
config.description.tagTreeShowEmptyFolders = Bo\u015f klas\u00f6rleri etiket a\u011fac\u0131nda g\u00f6ster.
config.name.autoLoadEmbeddedSwfs = G\u00f6m\u00fcl\u00fc SWF'leri otomatik y\u00fckle
config.description.autoLoadEmbeddedSwfs = G\u00f6m\u00fcl\u00fc SWF'leri DefineBinaryData etiketlerinden otomatik olarak y\u00fckleyin.
config.name.overrideTextExportFileName = Metin d\u0131\u015fa aktarma dosya ad\u0131n\u0131 ge\u00e7ersiz k\u0131l
config.description.overrideTextExportFileName = D\u0131\u015fa aktar\u0131lan metnin dosya ad\u0131n\u0131 \u00f6zelle\u015ftirebilirsiniz. Ge\u00e7erli SWF'nin dosya ad\u0131n\u0131 kullanmak i\u00e7in {filename} yer tutucusunu kullan\u0131n.
config.name.showOldTextDuringTextEditing = Metin d\u00fczenleme s\u0131ras\u0131nda eski metni g\u00f6ster
config.description.showOldTextDuringTextEditing = Metin etiketinin orijinal metnini \u00f6nizleme alan\u0131nda gri renkle g\u00f6sterir.
config.group.name.import = \u0130\u00e7e aktar
config.group.description.import = \u0130\u00e7e aktarma yap\u0131land\u0131rmas\u0131
config.name.textImportResizeTextBoundsMode = Metin s\u0131n\u0131rlar\u0131n\u0131 yeniden boyutland\u0131rma modu
config.description.textImportResizeTextBoundsMode = Metin d\u00fczenleme sonras\u0131nda metin s\u0131n\u0131rlar\u0131 yeniden boyutland\u0131rma modu.
config.name.showCloseConfirmation = SWF kapatma onay\u0131n\u0131 tekrar g\u00f6ster
config.description.showCloseConfirmation = De\u011fi\u015ftirilen dosyalar i\u00e7in SWF kapatma onay\u0131n\u0131 tekrar g\u00f6sterin.
config.name.showCodeSavedMessage = Kod kaydedildi mesaj\u0131n\u0131 tekrar g\u00f6ster
config.description.showCodeSavedMessage = Kod kaydedildi mesaj\u0131n\u0131 tekrar g\u00f6ster
config.name.showTraitSavedMessage = \u00d6zellik kaydedildi mesaj\u0131n\u0131 tekrar g\u00f6ster
config.description.showTraitSavedMessage = \u00d6zellik kaydedildi mesaj\u0131n\u0131 tekrar g\u00f6ster
config.name.updateProxyAddress = G\u00fcncellemeleri kontrol etmek i\u00e7in Http Proxy adresi
config.description.updateProxyAddress = G\u00fcncellemeleri kontrol etmek i\u00e7in Http Proxy adresi. Bi\u00e7im: \u00f6rnek.com:8080
config.name.editorMode = D\u00fczenleyici Modu
config.description.editorMode = Bir Metin veya Komut Dosyas\u0131 d\u00fc\u011f\u00fcm\u00fc se\u00e7ti\u011finizde metin alanlar\u0131n\u0131 otomatik olarak d\u00fczenlenebilir hale getirin
config.name.autoSaveTagModifications = Etiket de\u011fi\u015fikliklerini otomatik kaydet
config.description.autoSaveTagModifications = A\u011fa\u00e7ta yeni bir etiket se\u00e7ti\u011finizde de\u011fi\u015fiklikleri kaydedin
config.name.saveSessionOnExit = \u00c7\u0131k\u0131\u015fta oturumu kaydet
config.description.saveSessionOnExit = Ge\u00e7erli oturumu kaydeder ve FFDec yeniden ba\u015flat\u0131ld\u0131ktan sonra yeniden a\u00e7ar (yaln\u0131zca ger\u00e7ek dosyalarla \u00e7al\u0131\u015f\u0131r)
config.name._showDebugMenu = FFDec hata ay\u0131klama men\u00fcs\u00fcn\u00fc g\u00f6ster
config.description._showDebugMenu = Kod \u00e7\u00f6z\u00fcc\u00fcn\u00fcn hata ay\u0131klamas\u0131 i\u00e7in \u015feritte hata ay\u0131klama men\u00fcs\u00fcn\u00fc g\u00f6sterir.
config.name.allowOnlyOneInstance = Yaln\u0131zca bir FFDec \u00f6rne\u011fine izin ver (Yaln\u0131zca Windows \u0130\u015fletim Sistemi)
config.description.allowOnlyOneInstance = FFDec daha sonra yaln\u0131zca bir kez \u00e7al\u0131\u015ft\u0131r\u0131labilir, a\u00e7\u0131lan t\u00fcm dosyalar bir pencereye eklenir. Yaln\u0131zca Windows i\u015fletim sistemi ile \u00e7al\u0131\u015f\u0131r.
config.name.scriptExportSingleFile = Komut dosyalar\u0131n\u0131 tek dosyaya aktar
config.description.scriptExportSingleFile = Komut dosyalar\u0131n\u0131 birden \u00e7ok dosya yerine tek bir dosyaya d\u0131\u015fa aktar
config.name.setFFDecVersionInExportedFont = D\u0131\u015fa aktar\u0131lan yaz\u0131 tipinde FFDec s\u00fcr\u00fcm numaras\u0131n\u0131 ayarla
config.description.setFFDecVersionInExportedFont = Bu ayar devre d\u0131\u015f\u0131 b\u0131rak\u0131ld\u0131\u011f\u0131nda, FFDec ge\u00e7erli FFDec s\u00fcr\u00fcm numaras\u0131n\u0131 d\u0131\u015fa aktar\u0131lan yaz\u0131 tipine eklemeyecektir.
config.name.gui.skin = Kullan\u0131c\u0131 Aray\u00fcz\u00fc D\u0131\u015f G\u00f6r\u00fcn\u00fcm\u00fc
config.description.gui.skin = D\u0131\u015f g\u00f6r\u00fcn\u00fcm\u00fc g\u00f6r\u00fcn ve hissedin
config.name.lastSessionFiles = Son oturum dosyalar\u0131
config.description.lastSessionFiles = Son oturumdan itibaren a\u00e7\u0131lan dosyalar\u0131 i\u00e7erir
config.name.lastSessionSelection = Son oturum se\u00e7imi
config.description.lastSessionSelection = Son oturumdaki se\u00e7imi i\u00e7erir
config.name.loopMedia = D\u00f6ng\u00fc sesleri ve hareketli grafikler
config.description.loopMedia = Seslerin ve hareketli grafiklerin oynat\u0131lmas\u0131n\u0131 otomatik olarak yeniden ba\u015flat\u0131r
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Dahili) Zaman \u00c7izelgesi B\u00f6l\u00fcc\u00fc konumu
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = G\u00f6r\u00fcnt\u00fcleri \u00f6nbelle\u011fe al
config.description.cacheImages = Kodu \u00e7\u00f6z\u00fclm\u00fc\u015f g\u00f6r\u00fcnt\u00fc nesnelerini \u00f6nbelle\u011fe al
config.name.swfSpecificConfigs = SWF'e \u00f6zel yap\u0131land\u0131rmalar
config.description.swfSpecificConfigs = SWF'e \u00f6zel yap\u0131land\u0131rmalar\u0131 i\u00e7erir
config.name.exeExportMode = EXE d\u0131\u015fa aktarma modu
config.description.exeExportMode = EXE d\u0131\u015fa aktarma modu
config.name.ignoreCLikePackages = FlashCC / Alchemy veya benzeri paketleri yoksay
config.description.ignoreCLikePackages = FlashCC/Alchemy paketleri genellikle do\u011fru \u015fekilde derlenemez. Di\u011fer paketlerin derlenmesini h\u0131zland\u0131rmak i\u00e7in bunlar\u0131 devre d\u0131\u015f\u0131 b\u0131rakabilirsiniz.
config.name.overwriteExistingFiles = Varolan dosyalar\u0131n \u00fczerine yaz
config.description.overwriteExistingFiles = D\u0131\u015fa aktarma s\u0131ras\u0131nda mevcut dosyalar\u0131n \u00fczerine yaz\u0131n. \u015eu anda sadece AS2/3 komut dosyalar\u0131 i\u00e7in
config.name.smartNumberFormatting = Ak\u0131ll\u0131 say\u0131 bi\u00e7imlendirmesini kullan
config.description.smartNumberFormatting = \u00d6zel say\u0131lar\u0131 bi\u00e7imlendirin (\u00f6rne\u011fin renkler ve zamanlar)
config.name.enableScriptInitializerDisplay = (REMOVED) Komut ba\u015flat\u0131c\u0131lar\u0131n\u0131 g\u00f6r\u00fcnt\u00fcle
config.description.enableScriptInitializerDisplay = Kod ba\u015flat\u0131c\u0131lar\u0131n\u0131n g\u00f6r\u00fcnt\u00fclenmesini ve d\u00fczenlenmesini etkinle\u015ftirin. Bu ayar, vurgulama i\u00e7in her s\u0131n\u0131f dosyas\u0131na bir yeni sat\u0131r ekleyebilir.
config.name.autoOpenLoadedSWFs = \u00c7al\u0131\u015ft\u0131r\u0131l\u0131rken y\u00fcklenen SWF'leri a\u00e7 (Harici g\u00f6r\u00fcnt\u00fcleyici = yaln\u0131zca WIN)
config.description.autoOpenLoadedSWFs = FFDec harici oynat\u0131c\u0131da oynat\u0131ld\u0131\u011f\u0131nda SWF'i \u00e7al\u0131\u015ft\u0131rarak AS3 s\u0131n\u0131f Y\u00fckleyici taraf\u0131ndan y\u00fcklenen t\u00fcm SWF'leri otomatik olarak a\u00e7ar. Bu \u00f6zellik sadece Windows i\u00e7indir.
config.name.lastSessionFileTitles = Son oturum dosya ba\u015fl\u0131klar\u0131
config.description.lastSessionFileTitles = Son oturumdan itibaren a\u00e7\u0131lan dosya ba\u015fl\u0131klar\u0131n\u0131 i\u00e7erir (\u00f6rne\u011fin URL'den y\u00fcklendi\u011finde vb.)
config.group.name.paths = Yollar
config.group.description.paths = Gerekli dosyalar\u0131n konumu
config.group.tip.paths = Projekt\u00f6r\u00fc ve Playerglobal'\u0131 <a href="%link1%">adobe web sayfas\u0131ndan</a> indirin. Flex SDK, <a href="%link2%">apache web</a> adresinden indirilebilir.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Flash Player projekt\u00f6r yolu
config.description.playerLocation = Ba\u011f\u0131ms\u0131z flash player y\u00fcr\u00fct\u00fclebilir dosyas\u0131n\u0131n konumu. \u00c7al\u0131\u015ft\u0131r eylemi i\u00e7in kullan\u0131l\u0131r.
config.name.playerDebugLocation = 2) Flash Player projekt\u00f6r i\u00e7eri\u011fi hata ay\u0131klay\u0131c\u0131 yolu
config.description.playerDebugLocation = Ba\u011f\u0131ms\u0131z hata ay\u0131klama flash player y\u00fcr\u00fct\u00fclebilir dosyas\u0131n\u0131n konumu. Hata Ay\u0131klama eylemi i\u00e7in kullan\u0131l\u0131r.
config.name.playerLibLocation = 3) PlayerGlobal (.swc) yolu
config.description.playerLibLocation = Playerglobal.swc flash player k\u00fct\u00fcphanesinin konumu. \u00c7o\u011funlukla AS3 derlemesi i\u00e7in kullan\u0131l\u0131r.
config.name.debugHalt = Hata ay\u0131klama ba\u015flang\u0131c\u0131nda y\u00fcr\u00fctmeyi durdur
config.description.debugHalt = Hata ay\u0131klama i\u015fleminin ba\u015flang\u0131c\u0131nda SWF'i duraklat\u0131n.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent = (Dahili) Hata ay\u0131klama men\u00fcs\u00fc ay\u0131r\u0131c\u0131 konumu
config.description.gui.avm2.splitPane.vars.dividerLocationPercent = 
tip = \u0130pucu:
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Dahili) AS1/2 Hata ay\u0131klama men\u00fcs\u00fc ay\u0131r\u0131c\u0131 konumu
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = Harici oynat\u0131c\u0131daki SWF'i ms cinsinden de\u011fi\u015ftirmeden \u00f6nceki gecikme
config.description.setMovieDelay = Bu de\u011ferin 1000 ms'nin alt\u0131nda de\u011fi\u015ftirilmesi \u00f6nerilmez
config.name.warning.svgImport = SVG i\u00e7e aktarmas\u0131nda uyar
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Bir \u015fekil bir g\u00f6r\u00fcnt\u00fc ile de\u011fi\u015ftirildi\u011finde yumu\u015fat\u0131lmam\u0131\u015f dolgu kullan
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12 = Kendi flash g\u00f6r\u00fcnt\u00fcleyicisinde AS1/2 (Deneysel)
config.description.internalFlashViewer.execute.as12 = FFDec flash g\u00f6r\u00fcnt\u00fcleyici kullanarak SWF oynatma s\u0131ras\u0131nda ActionScript 1/2'yi \u00e7al\u0131\u015ft\u0131rmay\u0131 deneyin
config.name.warning.hexViewNotUpToDate = Onalt\u0131l\u0131k G\u00f6r\u00fcn\u00fcm g\u00fcncel de\u011fil uyar\u0131s\u0131n\u0131 g\u00f6ster
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = \u00a7\u00a7dup talimatlar\u0131n\u0131 g\u00f6ster
config.description.displayDupInstructions = Kodda \u00a7\u00a7dup talimatlar\u0131n\u0131 g\u00f6r\u00fcnt\u00fcleyin. Bunlar olmadan, kod kolayca derlenebilir ancak yan etkileri olan baz\u0131 \u00e7o\u011falt\u0131lm\u0131\u015f kodlar iki kez \u00e7al\u0131\u015ft\u0131r\u0131labilir.
config.name.useRegExprLiteral = RegExp'i /pattern/mod sabit de\u011feri olarak derleyin.
config.description.useRegExprLiteral = D\u00fczenli ifadeleri derlerken /pattern/mod s\u00f6zdizimini kullan\u0131n. Aksi halde yeni RegExp("pat","mod") kullan\u0131l\u0131r
config.name.handleSkinPartsAutomatically = [SkinPart] meta verilerini otomatik olarak i\u015fle
config.description.handleSkinPartsAutomatically = [SkinPart] meta verilerini otomatik olarak ayr\u0131\u015ft\u0131r\u0131r ve do\u011frudan d\u00fczenler. Kapat\u0131ld\u0131\u011f\u0131nda, _skinParts \u00f6zniteli\u011fi ve al\u0131c\u0131 y\u00f6ntemi g\u00f6r\u00fcn\u00fcr ve manuel olarak d\u00fczenlenebilir.
config.name.simplifyExpressions = \u0130fadeleri basitle\u015ftir
config.description.simplifyExpressions = Kodu daha okunakl\u0131 hale getirmek i\u00e7in ifadeleri de\u011ferlendirin ve basitle\u015ftirin
config.name.resetLetterSpacingOnTextImport = Metin i\u00e7e aktarmada Harf Aral\u0131\u011f\u0131n\u0131 S\u0131f\u0131rla
config.description.resetLetterSpacingOnTextImport = Kiril yaz\u0131 tipleri i\u00e7in kullan\u0131\u015fl\u0131d\u0131r, \u00e7\u00fcnk\u00fc daha geni\u015ftirler
config.name.flexSdkLocation = 4) Flex SDK dizin yolu
config.description.flexSdkLocation = Adobe Flex SDK'n\u0131n konumu. \u00c7o\u011funlukla AS3 derlemesi i\u00e7in kullan\u0131l\u0131r.
config.name.useFlexAs3Compiler = Flex SDK AS3 derleyicisini kullan
config.description.useFlexAs3Compiler = ActionScript do\u011frudan d\u00fczenlenirken Flex SDK'dan AS3 derleyicisini kullan\u0131n (Flex SDK dizininin ayarlanmas\u0131 gerekir)
config.name.showSetAdvanceValuesMessage = Geli\u015fmi\u015f de\u011ferlerin ayarlanmas\u0131yla ilgili bilgileri tekrar g\u00f6ster
config.description.showSetAdvanceValuesMessage = Geli\u015fmi\u015f de\u011ferlerin ayarlanmas\u0131yla ilgili bilgileri tekrar g\u00f6ster
config.name.gui.fontSizeMultiplier = Yaz\u0131 tipi boyutu \u00e7arpan\u0131
config.description.gui.fontSizeMultiplier = Yaz\u0131 tipi boyutu \u00e7arpan\u0131
config.name.graphVizDotLocation = 5) GraphViz Dot y\u00fcr\u00fct\u00fclebilir yolu
config.description.graphVizDotLocation = Grafikleri g\u00f6r\u00fcnt\u00fclemek i\u00e7in GraphViz uygulamas\u0131n\u0131n dot.exe (veya linux i\u00e7in benzeri) yolu.
#Do not translate the Font Styles which is in the parenthesis:(Plain,Bold,Italic,BoldItalic)
config.name.gui.sourceFont = Kaynak yaz\u0131 tipi stili
config.description.gui.sourceFont = FontName-FontStyle(Plain,Bold,Italic,BoldItalic)-FontSize
#after 11.1.0
config.name.as12DeobfuscatorExecutionLimit = AS1/2 kod gizleyici y\u00fcr\u00fctme s\u0131n\u0131r\u0131
config.description.as12DeobfuscatorExecutionLimit = AS1/2 y\u00fcr\u00fctme kod gizlemesi s\u0131ras\u0131nda i\u015flenen maksimum talimat say\u0131s\u0131
#option that ignore in 8.0.1 and other versions
config.name.showOriginalBytesInPcodeHex = (Dahili) Orijinal baytlar\u0131 g\u00f6ster
config.description.showOriginalBytesInPcodeHex = P kodu Onalt\u0131l\u0131da Orijinal Baytlar\u0131 g\u00f6ster
config.name.showFileOffsetInPcodeHex = (Dahili) Dosya ofsetini g\u00f6ster
config.description.showFileOffsetInPcodeHex = P kodu Onalt\u0131l\u0131da Dosya Ofsetini G\u00f6ster
config.name._enableFlexExport = (Dahili) Esnek D\u0131\u015fa Aktar\u0131m\u0131 etkinle\u015ftir
config.description.enableFlexExport = Esnek D\u0131\u015fa Aktar\u0131m\u0131 etkinle\u015ftir
config.name._ignoreAdditionalFlexClasses = (Dahili) Ek Esnek S\u0131n\u0131flar\u0131 yoksay
config.description.ignoreAdditionalFlexClasses = Ek Esnek S\u0131n\u0131flar\u0131 yoksay
config.name.hwAcceleratedGraphics = (Dahili) hw H\u0131zland\u0131r\u0131lm\u0131\u015f Grafikler
config.description.hwAcceleratedGraphics = hw H\u0131zland\u0131r\u0131lm\u0131\u015f Grafikler
config.name.gui.avm2.splitPane.docs.dividerLocationPercent = (Dahili) splitPane belgeleri b\u00f6l\u00fcc\u00fc Konum Y\u00fczdesi
config.description.gui.avm2.splitPane.docs.dividerLocationPercent = splitPane belgeleri b\u00f6l\u00fcc\u00fc Konum Y\u00fczdesi
config.name.gui.dump.splitPane.dividerLocationPercent = (Dahilil) dump splitPane b\u00f6l\u00fcc\u00fc Konum Y\u00fczdesi
config.description.gui.dump.splitPane.dividerLocationPercent = dump splitPane b\u00f6l\u00fcc\u00fc Konum Y\u00fczdesi
#after 11.3.0
config.name.useAdobeFlashPlayerForPreviews = (Kullan\u0131mdan kald\u0131r\u0131ld\u0131) Nesnelerin \u00f6nizlemesi i\u00e7in Adobe Flash player kullan\u0131n
config.description.useAdobeFlashPlayerForPreviews = Nesnelerin \u00f6nizlemesi i\u00e7in Adobe Flash player kullan\u0131n. UYARI: FlashPlayer 2021-01-12 tarihinde kullan\u0131mdan kald\u0131r\u0131lm\u0131\u015ft\u0131r
#after 12.0.1
config.name.showLineNumbersInPCodeGraphvizGraph = Graphviz grafiklerinde sat\u0131r numaralar\u0131n\u0131 g\u00f6ster
config.description.showLineNumbersInPCodeGraphvizGraph = P kodu graphviz grafi\u011finde sat\u0131r numaralar\u0131n\u0131 g\u00f6sterin.
config.name.padAs3PCodeInstructionName = Pad AS3 P kodu komut adlar\u0131
config.description.padAs3PCodeInstructionName = AS3 P kodu komut adlar\u0131n\u0131 bo\u015fluklarla doldurun
#after 13.0.2
config.name.indentAs3PCode = AS3 P kodunu girintile
config.description.indentAs3PCode = AS3 P kodu bloklar\u0131n\u0131 trait/body/code gibi girintileyin
config.name.labelOnSeparateLineAs3PCode = AS3 P kodunda ayr\u0131 bir sat\u0131rda etiket
config.description.labelOnSeparateLineAs3PCode = AS3 P kodundaki etiketin ayr\u0131 bir sat\u0131rda durmas\u0131n\u0131 sa\u011flay\u0131n
config.name.useOldStyleGetSetLocalsAs3PCode = AS3 P kodunda getlocalx yerine eski stil getlocal_x kullan\u0131n
config.description.useOldStyleGetSetLocalsAs3PCode = FFDec 12.x veya daha eski s\u00fcr\u00fcmlerden eski stil getlocal_x, setlocal_x kullan\u0131n
config.name.useOldStyleLookupSwitchAs3PCode = AS3 P kodunda k\u00f6\u015feli parantez olmadan eski stil arama anahtar\u0131n\u0131 kullan\u0131n
config.description.useOldStyleLookupSwitchAs3PCode = FFDec 12.x veya daha eski s\u00fcr\u00fcmlerden eski stil arama anahtar\u0131n\u0131 kullan\u0131n
#after 13.0.3
config.name.checkForModifications = FFDec d\u0131\u015f\u0131ndaki dosya de\u011fi\u015fikliklerini kontrol edin
config.description.checkForModifications = Dosyalar\u0131n di\u011fer uygulamalar taraf\u0131ndan de\u011fi\u015ftirilip de\u011fi\u015ftirilmedi\u011fini kontrol edin ve yeniden y\u00fcklemeyi isteyin
config.name.warning.initializers = AS3 slot/const d\u00fczenlemesinde ba\u015flat\u0131c\u0131lar hakk\u0131nda uyar
config.description.warning.initializers = AS3 slot/const d\u00fczenlemelerinde ba\u015flat\u0131c\u0131lar hakk\u0131nda uyar\u0131 g\u00f6ster
config.name.parametersPanelInSearchResults = Arama sonu\u00e7lar\u0131nda parametreler panelini g\u00f6ster
config.description.parametersPanelInSearchResults = Arama sonu\u00e7lar\u0131 penceresinde arama metni / b\u00fcy\u00fck/k\u00fc\u00e7\u00fck harfi yoksay / normal ifade gibi parametreler i\u00e7eren paneli g\u00f6ster
config.name.displayAs3PCodeDocsPanel = AS3 P kodunda belgeler panelini g\u00f6ster
config.description.displayAs3PCodeDocsPanel = AS3 P kodu d\u00fczenleme ve g\u00f6r\u00fcnt\u00fclemede komutlar\u0131n ve kod yap\u0131s\u0131n\u0131n belgelendi\u011fi paneli g\u00f6ster
config.name.displayAs3TraitsListAndConstantsPanel = AS3 \u00f6zellikler listesini ve sabitler panelini g\u00f6ster
config.description.displayAs3TraitsListAndConstantsPanel = AS3 i\u00e7in etiket a\u011fac\u0131n\u0131n alt\u0131nda \u00f6zelliklerin ve sabitlerin listesini i\u00e7eren paneli g\u00f6ster
#after 14.1.0
config.name.useAsTypeIcons = \u00d6\u011fe t\u00fcr\u00fcne g\u00f6re komut dosyas\u0131 simgelerini kullan
config.description.useAsTypeIcons = Farkl\u0131 komut dosyas\u0131 t\u00fcrleri i\u00e7in farkl\u0131 simgeler kullan\u0131n (s\u0131n\u0131f/aray\u00fcz/\u00e7er\u00e7eve/...)
config.name.limitAs3PCodeOffsetMatching = AS3 P kodu ofset e\u015fle\u015ftirme s\u0131n\u0131r\u0131
config.description.limitAs3PCodeOffsetMatching = AS3 komut dosyas\u0131yla ofset olarak e\u015fle\u015fen AS3 P kodundaki komutlar\u0131n s\u0131n\u0131r\u0131
#after 14.2.1
config.name.showSlowRenderingWarning = \u0130\u015fleme \u00e7ok yava\u015f oldu\u011funda g\u00fcnl\u00fck uyar\u0131s\u0131
config.description.showSlowRenderingWarning = Dahili flash g\u00f6r\u00fcnt\u00fcleyici i\u00e7eri\u011fi g\u00f6r\u00fcnt\u00fclemek i\u00e7in \u00e7ok yava\u015f oldu\u011funda uyar\u0131 g\u00fcnl\u00fc\u011f\u00fc tutar
#after 14.3.1
config.name.autoCloseQuotes = Kod d\u00fczenlemede tek t\u0131rnaklar\u0131 otomatik kapat
config.description.autoCloseQuotes = Birincisini yazarken otomatik olarak ikinci tek t\u0131rna\u011f\u0131 ' ekler
config.name.autoCloseDoubleQuotes = Kod d\u00fczenlemede \u00e7ift t\u0131rnaklar\u0131 otomatik kapat
config.description.autoCloseDoubleQuotes = Birincisini yazarken otomatik olarak ikinci \u00e7ift t\u0131rna\u011f\u0131 " ekler
config.name.autoCloseBrackets = Kod d\u00fczenlemede parantezleri otomatik kapat
config.description.autoCloseBrackets = A\u00e7\u0131l\u0131\u015f [ yaz\u0131ld\u0131\u011f\u0131nda otomatik olarak kapan\u0131\u015f parantezini ] ekler
config.name.autoCloseParenthesis = Kod d\u00fczenlemede parantezi otomatik kapat
config.description.autoCloseParenthesis = A\u00e7\u0131l\u0131\u015f ( yaz\u0131ld\u0131\u011f\u0131nda otomatik olarak kapan\u0131\u015f parantezini ) ekler
config.name.showDialogOnError = Her hatada hata ileti\u015fim kutusunu g\u00f6ster
config.description.showDialogOnError = Her hata olu\u015fumunda hata ileti\u015fim kutusunu otomatik olarak g\u00f6r\u00fcnt\u00fcler
#after 14.4.0
config.name.limitSameChars = \\{xx}C (tekrar) ka\u00e7\u0131\u015f\u0131 i\u00e7in ayn\u0131 karakterlerin s\u0131n\u0131r\u0131
config.description.limitSameChars = \\{xx}C tekrar ka\u00e7\u0131\u015f ile de\u011fi\u015ftirilmeden \u00f6nce P kodu dizelerinde veya gizlenmi\u015f adlarda bir sat\u0131rdaki maksimum ayn\u0131 karakter say\u0131s\u0131
#after 14.5.2
config.name.showImportScriptsInfo = Komut dosyalar\u0131n\u0131 i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportScriptsInfo = Men\u00fcdeki Komut dosyalar\u0131n\u0131 i\u00e7e aktar se\u00e7ene\u011fine t\u0131klad\u0131ktan sonra komut dosyalar\u0131n\u0131 i\u00e7e aktarman\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131 hakk\u0131nda baz\u0131 bilgiler g\u00f6r\u00fcnt\u00fcler.
config.name.showImportTextInfo = Metni i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportTextInfo = Men\u00fcde Metni i\u00e7e aktar se\u00e7ene\u011fine t\u0131klad\u0131ktan sonra metni i\u00e7e aktarman\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131 hakk\u0131nda baz\u0131 bilgiler g\u00f6r\u00fcnt\u00fcler.
config.name.showImportSymbolClassInfo = Sembol-S\u0131n\u0131f\u0131 i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportSymbolClassInfo = Men\u00fcde Sembol-S\u0131n\u0131f\u0131 \u0130\u00e7e Aktar t\u0131kland\u0131ktan sonra Sembol-S\u0131n\u0131f\u0131 i\u00e7e aktarman\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131 hakk\u0131nda baz\u0131 bilgiler g\u00f6r\u00fcnt\u00fcler.
config.name.showImportXmlInfo = XML'i i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportXmlInfo = Men\u00fcde XML \u0130\u00e7e Aktar se\u00e7ene\u011fine t\u0131klad\u0131ktan sonra XML i\u00e7e aktarman\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131 hakk\u0131nda baz\u0131 bilgiler g\u00f6r\u00fcnt\u00fcler.
#after 15.1.1
config.name.lastSessionTagListSelection = Son oturum etiket listesi se\u00e7imi
config.description.lastSessionTagListSelection = Liste se\u00e7imi g\u00f6r\u00fcn\u00fcm\u00fcndeki son oturumdaki se\u00e7imi i\u00e7erir
config.name.lastView = Son g\u00f6r\u00fcn\u00fcm
config.description.lastView = Son g\u00f6r\u00fcnt\u00fclenen g\u00f6r\u00fcn\u00fcm modu
config.name.swfSpecificCustomConfigs = SWF'ye \u00f6zg\u00fc \u00f6zel yap\u0131land\u0131rmalar
config.description.swfSpecificCustomConfigs = \u00d6zel bi\u00e7imde SWF'ye \u00f6zg\u00fc yap\u0131land\u0131rmalar\u0131 i\u00e7erir
config.name.warningOpeningReadOnly = Salt okunur SWF a\u00e7\u0131l\u0131rken uyar
config.description.warningOpeningReadOnly = SWF'yi salt okunur kaynaktan a\u00e7arken uyar\u0131 g\u00f6ster
# after 16.1.0
config.name.showImportImageInfo = G\u00f6r\u00fcnt\u00fcleri i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportImageInfo = Men\u00fcdeki G\u00f6r\u00fcnt\u00fcleri i\u00e7e aktar se\u00e7ene\u011fine t\u0131klad\u0131ktan sonra g\u00f6r\u00fcnt\u00fcleri i\u00e7e aktarman\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131na dair baz\u0131 bilgiler g\u00f6r\u00fcnt\u00fcler.
config.name.autoPlaySwfs = SWF \u00f6nizlemelerini otomatik oynat
config.description.autoPlaySwfs = SWF d\u00fc\u011f\u00fcm\u00fc se\u00e7iminde SWF \u00f6nizlemesini otomatik olarak oynat\u0131n.
config.name.expandFirstLevelOfTreeOnLoad = SWF y\u00fcklendi\u011finde a\u011fac\u0131n ilk seviyesini geni\u015flet
config.description.expandFirstLevelOfTreeOnLoad = SWF a\u00e7\u0131ld\u0131\u011f\u0131nda a\u011fa\u00e7taki d\u00fc\u011f\u00fcmlerin ilk seviyesini otomatik olarak geni\u015fletir.
# after 16.2.0
config.name.allowPlacingDefinesIntoSprites = DefineSprite i\u00e7ine tan\u0131mlama etiketlerinin yerle\u015ftirilmesine izin ver
config.description.allowPlacingDefinesIntoSprites = DefineSprite i\u00e7ine tan\u0131mlama t\u00fcr\u00fc etiketlerinin yerle\u015ftirilmesini (ta\u015f\u0131nmas\u0131n\u0131/kopyalanmas\u0131n\u0131/s\u00fcr\u00fcklenmesini) sa\u011flar.
config.name.allowDragAndDropInTagListTree = Etiket listesi g\u00f6r\u00fcn\u00fcm\u00fcnde s\u00fcr\u00fcklemeye ve b\u0131rakmaya izin ver
config.description.allowDragAndDropInTagListTree = Etiket listesi g\u00f6r\u00fcn\u00fcm\u00fcn\u00fcn a\u011fac\u0131nda s\u00fcr\u00fckle ve b\u0131rak ile etiketlerin ta\u015f\u0131nmas\u0131na / kopyalanmas\u0131na izin verir.
config.name.allowMiterClipLinestyle = (REMOVED) G\u00f6nye klipsi \u00e7izgi stillerine izin ver (YAVA\u015e)
config.description.allowMiterClipLinestyle = G\u00f6nye klipsi \u00e7izgi stillerini destekleyen ancak yava\u015f olan \u00f6zel olu\u015fturucunun kullan\u0131lmas\u0131na izin verin.
advancedSettings.search = Ara:
# after 16.3.1
config.name.animateSubsprites = Alt hareketli grafikleri \u00f6nizlemede canland\u0131r
config.description.animateSubsprites = Zaman \u00e7izelgesi \u00f6n izlemesinde alt hareketli grafik animasyonuna izin verin.
config.name.autoPlayPreviews = \u00d6nizlemeleri otomatik oynat
config.description.autoPlayPreviews = \u00d6nizlemeleri otomatik olarak oynat\u0131n.
config.name.maxCachedTime = Maksimum ge\u00e7ici \u00f6nbellek s\u00fcresi
config.description.maxCachedTime = \u00d6\u011fenin (o zamandan beri eri\u015filmeyen) \u00f6nbellekten kald\u0131r\u0131lmas\u0131ndan \u00f6nceki milisaniye cinsinden maksimum s\u00fcre. S\u0131n\u0131rs\u0131z \u00f6nbelle\u011fe alma i\u00e7in bunu 0 olarak ayarlay\u0131n.
config.name.airLibLocation = 6) AIR kitapl\u0131\u011f\u0131 yolu (airglobal.swc)
config.description.airLibLocation = airglobal.swc AIR kitapl\u0131\u011f\u0131n\u0131n konumu. \u00c7o\u011funlukla AS3 derlemesi i\u00e7in kullan\u0131labilir.
config.name.showImportShapeInfo = \u015eekilleri i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportShapeInfo = Men\u00fcde \u015eekilleri i\u00e7e aktar se\u00e7ene\u011fine t\u0131klad\u0131ktan sonra \u015fekillerin i\u00e7e aktar\u0131lmas\u0131n\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131 hakk\u0131nda baz\u0131 bilgiler g\u00f6r\u00fcnt\u00fcler.
config.name.pinnedItemsTagTreePaths = Etiket a\u011fac\u0131ndaki sabitlenmi\u015f \u00f6\u011fe yollar\u0131
config.description.pinnedItemsTagTreePaths = Etiket a\u011fac\u0131n\u0131n sabitlenmi\u015f d\u00fc\u011f\u00fcmlerinin yollar\u0131.
config.name.pinnedItemsTagListPaths = Etiket listesi g\u00f6r\u00fcn\u00fcm a\u011fac\u0131nda sabitlenmi\u015f \u00f6\u011fe yollar\u0131
config.description.pinnedItemsTagListPaths = Etiket listesi g\u00f6r\u00fcn\u00fcm\u00fc a\u011fac\u0131n\u0131n sabitlenmi\u015f d\u00fc\u011f\u00fcmlerinin yollar\u0131.
config.name.flattenASPackages = ActionScript paketlerini d\u00fczle\u015ftir
config.description.flattenASPackages = Paket a\u011fac\u0131 yerine paket ba\u015f\u0131na bir \u00f6\u011fe yap\u0131n.
config.name.gui.scale = Kullan\u0131c\u0131 aray\u00fcz\u00fc \u00f6l\u00e7ek fakt\u00f6r\u00fc
config.description.gui.scale = Grafik aray\u00fcz\u00fcn\u00fcn \u00f6l\u00e7eklendirme fakt\u00f6r\u00fc. Mac retina ekranlar\u0131nda bunu 2.0 olarak ayarlay\u0131n. Uygulamadan ger\u00e7ek \u00e7\u0131k\u0131\u015f (sadece sorduktan sonra yeniden ba\u015flatma de\u011fil) gereklidir.
config.name.warning.video.vlc = Eksik VLC konusunda uyar
config.description.warning.video.vlc = VLC mevcut olmad\u0131\u011f\u0131nda DefineVideoStream etiketli SWF'leri a\u00e7arken VLC medya oynat\u0131c\u0131s\u0131n\u0131n gerekli oldu\u011funa dair uyar\u0131 g\u00f6sterin.
config.name.playFrameSounds = \u00c7er\u00e7eve seslerini oynat
config.description.playFrameSounds = G\u00f6r\u00fcnt\u00fclenen \u00e7er\u00e7evelerde sesler oynat\u0131n.
config.name.fixAntialiasConflation = Kenar yumu\u015fatma birle\u015fimini d\u00fczeltmek i\u00e7in \u015fekil alan\u0131n\u0131 geni\u015fletin
config.description.fixAntialiasConflation = Kenar yumu\u015fatma nedeniyle biti\u015fik \u015fekiller aras\u0131nda olu\u015fan kar\u0131\u015fma sorunlar\u0131n\u0131, \u015feklin konturunu yar\u0131m piksel uzatarak d\u00fczeltir.
config.name.autoPlaySounds = Sesleri otomatik oynat
config.description.autoPlaySounds = Treenode se\u00e7iminde sesleri otomatik olarak oynat\u0131n (DefineSound).
config.name.deobfuscateAs12RemoveInvalidNamesAssignments = AS1/2 karartma: Karart\u0131lm\u0131\u015f adlara sahip de\u011fi\u015fken bildirimlerini kald\u0131r
config.description.deobfuscateAs12RemoveInvalidNamesAssignments = AS1/2'nin karartma i\u015flemi s\u0131ras\u0131nda, standart olmayan adlara sahip de\u011fi\u015fken bildirimlerini kald\u0131r\u0131n. UYARI: Bu, gizlenmi\u015f adlara dayanan SWF'lere zarar verebilir.
config.name.gui.splitPanePlace.dividerLocationPercent = (Dahili) Ay\u0131r\u0131c\u0131 yer konumu
config.description.gui.splitPanePlace.dividerLocationPercent = 
config.name.gui.splitPaneTransform1.dividerLocationPercent = (Dahili) Ay\u0131r\u0131c\u0131 d\u00f6n\u00fc\u015f\u00fcm\u00fc1 konumu
config.description.gui.splitPaneTransform1.dividerLocationPercent = 
config.name.gui.splitPaneTransform2.dividerLocationPercent = (Dahili) Ay\u0131r\u0131c\u0131 d\u00f6n\u00fc\u015f\u00fcm\u00fc2 konumu
config.description.gui.splitPaneTransform2.dividerLocationPercent = 
config.name.gui.transform.lastExpandedCards = (Dahili) Son geni\u015fletilmi\u015f d\u00f6n\u00fc\u015f\u00fcm kartlar\u0131
config.description.gui.transform.lastExpandedCards = 
config.name.doubleClickNodeToEdit = D\u00fczenlemeye ba\u015flamak i\u00e7in \u00e7ift t\u0131kla
config.description.doubleClickNodeToEdit = A\u011fa\u00e7 d\u00fc\u011f\u00fcm\u00fcne \u00e7ift t\u0131kland\u0131\u011f\u0131nda d\u00fczenleme ba\u015flat\u0131l\u0131r.
config.name.warningDeobfuscation = Karartmay\u0131 de\u011fi\u015ftirmede uyar
config.description.warningDeobfuscation = Karartmay\u0131 a\u00e7arken/kapat\u0131rken uyar\u0131 g\u00f6ster.
config.name.warningRenameIdentifiers = Tan\u0131mlay\u0131c\u0131lar\u0131 otomatik yeniden adland\u0131rma de\u011fi\u015ftirildi\u011finde uyar
config.description.warningRenameIdentifiers = Tan\u0131mlay\u0131c\u0131lar\u0131 otomatik yeniden adland\u0131rma \u00f6zelli\u011fini a\u00e7arken uyar\u0131 g\u00f6ster.
config.name.showImportMovieInfo = Filmleri i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportMovieInfo = Men\u00fcde Filmleri i\u00e7e aktar se\u00e7ene\u011fine t\u0131klad\u0131ktan sonra film i\u00e7e aktarman\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131 hakk\u0131nda baz\u0131 bilgiler g\u00f6r\u00fcnt\u00fcler.
config.name.showImportSoundInfo = Sesleri i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportSoundInfo = Men\u00fcde Sesleri i\u00e7e aktar se\u00e7ene\u011fine t\u0131klad\u0131ktan sonra sesleri i\u00e7e aktarman\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131 hakk\u0131nda baz\u0131 bilgiler g\u00f6r\u00fcnt\u00fcler.
config.name.svgRetainBounds = SVG d\u0131\u015fa aktar\u0131m\u0131 s\u0131ras\u0131nda \u015fekil s\u0131n\u0131rlar\u0131n\u0131 koru
config.description.svgRetainBounds = SVG d\u0131\u015fa aktar\u0131m\u0131 s\u0131ras\u0131nda \u015feklin x, y konumu aynen SWF'de oldu\u011fu gibi d\u0131\u015fa aktar\u0131l\u0131r (\u00f6rn. pozitif veya negatif).
config.name.disableBitmapSmoothing = Bitmap yumu\u015fatmay\u0131 devre d\u0131\u015f\u0131 b\u0131rak
config.description.disableBitmapSmoothing = G\u00f6r\u00fcnt\u00fcleme s\u0131ras\u0131nda yumu\u015fat\u0131lm\u0131\u015f bitmap dolgular\u0131n\u0131 devre d\u0131\u015f\u0131 b\u0131rak\u0131n - t\u00fcm\u00fcn\u00fc yumu\u015fat\u0131lmam\u0131\u015f (pikselli) olarak g\u00f6sterin. Bu, d\u0131\u015fa aktar\u0131lan g\u00f6r\u00fcnt\u00fcler i\u00e7in ge\u00e7erli de\u011fildir.
config.name.pinnedItemsScrollPos = Sabitlenmi\u015f \u00f6\u011feler kayd\u0131rma / \u015fapka konumlar\u0131
config.description.pinnedItemsScrollPos = Sabitlenmi\u015f \u00f6\u011felerin kayd\u0131rma veya \u015fapka konumlar\u0131.
config.name.maxRememberedScrollposItems = Maksimum hat\u0131rlanan kayd\u0131rma konumu say\u0131s\u0131
config.description.maxRememberedScrollposItems = Hat\u0131rlanan kayd\u0131rma konumu \u00f6\u011felerinin maksimum say\u0131s\u0131.
config.name.rememberScriptsScrollPos = Komut dosyalar\u0131n\u0131n kayd\u0131rma/\u015fapka konumunu hat\u0131rla
config.description.rememberScriptsScrollPos = \u00d6\u011feler de\u011fi\u015ftirilirken komut dosyas\u0131 kayd\u0131rma/\u015fapka konumu korunur ve sabitlenmi\u015f \u00f6\u011feler i\u00e7in kaydedilir.
config.name.rememberFoldersScrollPos = Klas\u00f6r kayd\u0131rma konumunu hat\u0131rla
config.description.rememberFoldersScrollPos = Klas\u00f6r kayd\u0131rma konumu, \u00f6\u011feler de\u011fi\u015ftirilirken korunur ve sabitlenen \u00f6\u011feler i\u00e7in kaydedilir.
#after 18.3.6
config.name.warning.initializers.class = AS3 s\u0131n\u0131f \u00f6zelli\u011fi d\u00fczenlemesinde komut dosyas\u0131 ba\u015flat\u0131c\u0131 hakk\u0131nda uyar
config.description.warning.initializers.class = AS3 s\u0131n\u0131f \u00f6zelli\u011fi d\u00fczenlemesinde ba\u015flat\u0131c\u0131 hakk\u0131nda uyar\u0131 g\u00f6ster
#after 18.4.1
config.name.maxCachedNum = Tek \u00f6nbellek ba\u015f\u0131na maksimum \u00f6nbelle\u011fe al\u0131nan \u00f6\u011fe say\u0131s\u0131
config.description.maxCachedNum = Eski \u00f6\u011feler \u00f6nbellekten kald\u0131r\u0131lmadan \u00f6nce \u00f6nbelle\u011fe al\u0131nan maksimum \u00f6\u011fe say\u0131s\u0131. Daha d\u00fc\u015f\u00fck de\u011fer = daha az bellek, daha yava\u015f uygulama. Daha y\u00fcksek de\u011fer = daha fazla bellek, daha h\u0131zl\u0131 uygulama. S\u0131n\u0131rs\u0131z \u00f6nbelle\u011fe alma i\u00e7in bunu 0 olarak ayarlay\u0131n.
config.name.warning.cannotencrypt = \u015eifrelenmi\u015f olarak kaydedilemedi\u011finde uyar
config.description.warning.cannotencrypt = HARMAN Air \u015fifrelemesi kullan\u0131larak \u015fifrelenmi\u015f SWF dosyas\u0131 kaydedilemedi\u011finde uyar\u0131 g\u00f6ster.
#after 18.5.0
config.name.lastExportEnableEmbed = G\u00f6m\u00fcl\u00fc varl\u0131klar\u0131 d\u0131\u015fa aktarman\u0131n son ayar\u0131
config.description.lastExportEnableEmbed = G\u00f6m\u00fcl\u00fc varl\u0131klar\u0131 [Embed] meta verileri arac\u0131l\u0131\u011f\u0131yla d\u0131\u015fa aktarman\u0131n son ayar\u0131.
config.name.lastFlaExportVersion = Son FLA d\u0131\u015fa aktarma s\u00fcr\u00fcm\u00fc
config.description.lastFlaExportVersion = Son d\u0131\u015fa aktar\u0131lan FLA s\u00fcr\u00fcm\u00fc
config.name.lastFlaExportCompressed = S\u0131k\u0131\u015ft\u0131r\u0131lm\u0131\u015f son FLA d\u0131\u015fa aktar\u0131m\u0131
config.description.lastFlaExportCompressed = S\u0131k\u0131\u015ft\u0131r\u0131lm\u0131\u015f son d\u0131\u015fa aktar\u0131lan FLA s\u00fcr\u00fcm\u00fc
#after 19.0.0
config.name.showImportSpriteInfo = Hareketli grafikleri i\u00e7e aktarmadan \u00f6nce bilgileri g\u00f6ster
config.description.showImportSpriteInfo = Men\u00fcdeki Hareketli Grafikleri \u0130\u00e7e Aktar'\u0131 t\u0131klad\u0131ktan sonra hareketli grafikleri i\u00e7e aktarman\u0131n nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131yla ilgili baz\u0131 bilgileri g\u00f6r\u00fcnt\u00fcler.
config.name.displayAs12PCodeDocsPanel = AS1/2 P-kodunda dok\u00fcmanlar panelini g\u00f6ster
config.description.displayAs12PCodeDocsPanel = AS1/2 P kodu d\u00fczenleme ve g\u00f6r\u00fcnt\u00fclemedeki eylemlerin dok\u00fcmantasyonunu i\u00e7eren paneli g\u00f6ster
config.name.gui.action.splitPane.docs.dividerLocationPercent = (Dahili) AS 1/2 b\u00f6l\u00fcnm\u00fc\u015f Panel belgeleri ay\u0131r\u0131c\u0131 Konum Y\u00fczdesi
config.description.action.avm2.splitPane.docs.dividerLocationPercent = AS 1/2 b\u00f6l\u00fcnm\u00fc\u015f Panel belgeleri ay\u0131r\u0131c\u0131 Konum Y\u00fczdesi
#after 19.1.2
config.name.rememberLastScreen = Son kullan\u0131lan ekran\u0131 hat\u0131rla (birden fazla monit\u00f6rde)
config.description.rememberLastScreen = Birden fazla ekran ayg\u0131t\u0131 (monit\u00f6r) i\u00e7eren yap\u0131land\u0131rmada son kullan\u0131lan ekran\u0131 hat\u0131rla
config.name.lastMainWindowScreenIndex = Son ana pencere ekran dizini
config.description.lastMainWindowScreenIndex = Son ana pencere ekran dizini
config.name.lastMainWindowScreenX = Son ana pencere ekran\u0131 X
config.description.lastMainWindowScreenX = Son ana pencere ekran\u0131 X koordinat\u0131
config.name.lastMainWindowScreenY = Son ana pencere ekran\u0131 Y
config.description.lastMainWindowScreenY = Son ana pencere ekran\u0131 Y koordinat\u0131
config.name.lastMainWindowScreenWidth = Son ana pencere ekran geni\u015fli\u011fi
config.description.lastMainWindowScreenWidth = Son ana pencere ekran geni\u015fli\u011fi
config.name.lastMainWindowScreenHeight = Son ana pencere ekran geni\u015fli\u011fi
config.description.lastMainWindowScreenHeight = Son ana pencere ekran geni\u015fli\u011fi
config.name.displayAs12PCodePanel = AS1/2 P kodu panelini g\u00f6ster
config.description.displayAs12PCodePanel = ActionScript 1 ve 2 i\u00e7in s\u00f6k\u00fclm\u00fc\u015f P kodu eylemleri i\u00e7eren paneli g\u00f6ster
config.name.displayAs3PCodePanel = AS3 P kodu panelini g\u00f6ster
config.description.displayAs3PCodePanel = ActionScript 3 i\u00e7in s\u00f6k\u00fclm\u00fc\u015f P kodu talimatlar\u0131 i\u00e7eren paneli g\u00f6ster
config.name.flaExportUseMappedFontLayout = FLA d\u0131\u015fa aktar - e\u015flenen yaz\u0131 tipi d\u00fczenini kullan
config.description.flaExportUseMappedFontLayout = FLA d\u0131\u015fa aktarma s\u0131ras\u0131nda ger\u00e7ek yaz\u0131 tipinin d\u00fczeni olmad\u0131\u011f\u0131nda harf aral\u0131\u011f\u0131n\u0131 belirlerken atanm\u0131\u015f kaynak yaz\u0131 tipi ilerleme de\u011ferlerini kullan\u0131n.
#after 20.0.0
config.name.formatting.tab.size = Sekme boyutu
config.description.formatting.tab.size = Sekme ba\u015f\u0131na bo\u015fluk say\u0131s\u0131
config.name.boxBlurPixelsLimit = Kutu bulan\u0131kla\u015ft\u0131rma filtresi piksel s\u0131n\u0131r\u0131
config.description.boxBlurPixelsLimit = Kutu bulan\u0131kla\u015ft\u0131rma filtresini hesaplamak i\u00e7in maksimum piksel say\u0131s\u0131. Ger\u00e7ek s\u0131n\u0131r, bu say\u0131n\u0131n 10000 ile \u00e7arp\u0131lmas\u0131d\u0131r. Piksel say\u0131s\u0131 daha fazlaysa, bulan\u0131kl\u0131kX ve bulan\u0131kl\u0131kY azalt\u0131l\u0131r.
config.name.as3ExportNamesUseClassNamesOnly = D\u0131\u015fa aktar\u0131lan varl\u0131klar\u0131n adlar\u0131 yaln\u0131zca s\u0131n\u0131flara dayan\u0131r (AS3)
config.description.as3ExportNamesUseClassNamesOnly = D\u0131\u015fa aktar\u0131lan varl\u0131k dosyalar\u0131 (g\u00f6r\u00fcnt\u00fcler, ses, ...) yaln\u0131zca SymbolClass etiketinden - atanm\u0131\u015f s\u0131n\u0131flar\u0131ndan - ad al\u0131r. Karakter kimli\u011fi eklenmez. Ayr\u0131ca, ayn\u0131 varl\u0131\u011fa birden fazla s\u0131n\u0131f atand\u0131\u011f\u0131nda, birden \u00e7ok kez d\u0131\u015fa aktar\u0131l\u0131r. (ActionScript 3 SWF'leri i\u00e7in)
config.name.jnaTempDirectory = JNA Ge\u00e7ici dizini
config.description.jnaTempDirectory = JNA DLL'leri vb. i\u00e7in ge\u00e7ici dizin yolu. Bunun, herhangi bir Unicode karakteri i\u00e7ermeyen bir yola ayarlanmas\u0131 gerekir. Ayarlanmad\u0131\u011f\u0131nda, ge\u00e7erli kullan\u0131c\u0131 TEMP dizini kullan\u0131l\u0131r.
config.name.flaExportFixShapes = FLA d\u0131\u015fa aktar - \u015fekilleri d\u00fczelt (yava\u015f)
config.description.flaExportFixShapes = Baz\u0131 \u015fekillerdeki eksik dolgular\u0131 d\u00fczeltmek i\u00e7in \u00f6rt\u00fc\u015fen kenarlar\u0131 b\u00f6lme prosed\u00fcr\u00fcn\u00fc uygulay\u0131n. Bu, baz\u0131 karma\u015f\u0131k \u015fekillerde \u00e7ok yava\u015f olabilir.
config.name.lastExportResampleWav = WAV'\u0131 yeniden \u00f6rneklemenin son ayar\u0131
config.description.lastExportResampleWav = Wav'\u0131 44kHz'e yeniden \u00f6rneklemenin son ayar\u0131
config.name.previewResampleSound = Ses \u00f6nizlemelerinde yeniden \u00f6rnekleme
config.description.previewResampleSound = Ses \u00f6nizlemelerinde 44kHz'e yeniden \u00f6rnekleme
config.name.lastExportTransparentBackground = \u00c7er\u00e7eve d\u0131\u015fa aktarmada arka plan rengini yoksayman\u0131n son ayar\u0131
config.description.lastExportTransparentBackground = Arka plan saydaml\u0131\u011f\u0131n\u0131 sa\u011flamak i\u00e7in \u00e7er\u00e7eve d\u0131\u015fa aktar\u0131m\u0131nda arka plan rengini yoksayman\u0131n son ayar\u0131
config.name.warningAbcClean = ABC temizleme eyleminde uyar
config.description.warningAbcClean = ABC temizleme eylemini yapmadan \u00f6nce uyar\u0131 g\u00f6ster
config.name.warningAddFunction = AS3 P kodunda yeni i\u015flev eklerken uyar
config.description.warningAddFunction = AS3 P kodunda yeni i\u015flev olu\u015fturmadan \u00f6nce uyar\u0131 g\u00f6ster. Ayr\u0131ca eylemin nas\u0131l \u00e7al\u0131\u015ft\u0131\u011f\u0131na dair baz\u0131 bilgiler de g\u00f6sterir.
#after 21.0.2
config.name.linkAllClasses = T\u00fcm s\u0131n\u0131flara (ses, yaz\u0131 tipi, resim) ba\u011flant\u0131 ekle
config.description.linkAllClasses = SWF'deki t\u00fcm (ses, yaz\u0131 tipi, resim) s\u0131n\u0131flar\u0131 ba\u011flayan \u00f6zel bir komut dosyas\u0131 ekle. Bu, ba\u015fka hi\u00e7bir komut dosyas\u0131 bunlar\u0131 ba\u011flamad\u0131\u011f\u0131nda ve derlenmi\u015f dosyada hala mevcut oldu\u011funda yararl\u0131d\u0131r.
#after 21.1.0
config.name.recentColors = Son renkler
config.description.recentColors = Renk ileti\u015fim kutusundaki son renkler
#after 21.1.1
config.name.gui.splitPaneEasyVertical.dividerLocationPercent = (Dahili) Basit UI dikey ay\u0131r\u0131c\u0131 konumu
config.description.gui.splitPaneEasyVertical.dividerLocationPercent = 
config.name.gui.splitPaneEasyHorizontal.dividerLocationPercent = (Dahili) Basit UI yatay ay\u0131r\u0131c\u0131 konumu
config.description.gui.splitPaneEasyHorizontal.dividerLocationPercent = 
config.name.lastSessionEasySwf = Son Basit d\u00fczenleyici oturum dosyas\u0131
config.description.lastSessionEasySwf = Basit d\u00fczenleyicideki son oturumdan se\u00e7ilen SWF'yi i\u00e7erir
config.name.maxScriptLineLength = Maksimum komut sat\u0131r\u0131 uzunlu\u011fu
config.description.maxScriptLineLength = Sat\u0131r kayd\u0131rma ger\u00e7ekle\u015fmeden \u00f6nce komut dosyas\u0131 d\u00fczenleyicisinde en fazla sat\u0131r uzunlu\u011fu. 0 = s\u0131n\u0131rs\u0131z. Linux'ta \u00e7ok b\u00fcy\u00fck sat\u0131rlar\u0131n g\u00f6r\u00fcnt\u00fclenmesinde sorunlar olabilir, bu nedenle varsay\u0131lan olarak s\u0131n\u0131rl\u0131d\u0131r.
#after 21.1.3
config.name.lastSolEditorDirectory = Son SOL d\u00fczenleyici dizini
config.description.lastSolEditorDirectory = Son SOL dosyas\u0131n\u0131n a\u00e7\u0131ld\u0131\u011f\u0131/kaydedildi\u011fi dizin
#after 22.0.2
config.name.halfTransparentParentLayersEasy = Basit d\u00fczenleyicide yar\u0131 saydam \u00fcst katmanlar
config.description.halfTransparentParentLayersEasy = Alt hareketli klipleri yar\u0131 saydam olarak d\u00fczenlerken \u00fcst katmanlar\u0131 g\u00f6sterir. False = \u00fcst katmanlar\u0131 hi\u00e7 g\u00f6sterme.
config.name.showRuler = Cetveli g\u00f6ster
config.description.showRuler = SWF nesnelerini g\u00f6r\u00fcnt\u00fclerken/d\u00fczenlerken cetveli g\u00f6ster.
config.name.snapToGuides = K\u0131lavuzlara tuttur
config.description.snapToGuides = Nesneleri hareket ettirirken imleci t\u00fcm piksellere tutturmay\u0131 etkinle\u015ftirir.
config.name.snapToObjects = Nesnelere tuttur
config.name.snapToPixels = Piksellere tuttur
config.name.snapAlign = Hizala tuttur
config.description.snapAlign = Nesneleri hareket ettirirken imle\u00e7 hizalama sat\u0131rlar\u0131na tutturmay\u0131 etkinle\u015ftirir.
config.name.showGuides = K\u0131lavuzlar\u0131 g\u00f6ster
config.description.showGuides = K\u0131lavuzlar\u0131 gizlemek i\u00e7in bunu false olarak ayarlay\u0131n.
config.name.lockGuides = K\u0131lavuzlar\u0131 kilitle
config.description.lockGuides = K\u0131lavuzlar\u0131n hareket etmesini devre d\u0131\u015f\u0131 b\u0131rak\u0131r, b\u00f6ylece konumlar\u0131n\u0131 korurlar ve hareket ettirilemezler.
config.name.showGrid = Izgaray\u0131 g\u00f6ster
config.description.showGrid = Sahnede \u0131zgaray\u0131 g\u00f6sterir
config.name.gridVerticalSpace = Izgara dikey aral\u0131\u011f\u0131 (px)
config.description.gridVerticalSpace = Izgara \u00e7izgileri aras\u0131ndaki dikey bo\u015fluk (piksel cinsinden).
config.name.gridHorizontalSpace = Izgara yatay aral\u0131\u011f\u0131 (px)
config.description.gridHorizontalSpace = Izgara \u00e7izgileri aras\u0131ndaki yatay bo\u015fluk (piksel cinsinden).
config.name.snapToGrid = Izgaraya tuttur
config.description.snapToGrid = Nesneleri hareket ettirirken imleci \u0131zgaraya tutturmay\u0131 etkinle\u015ftirir.
config.name.gridOverObjects = Izgaray\u0131 nesnelerin \u00fczerine g\u00f6ster
config.description.gridOverObjects = Izgara g\u00f6r\u00fcnt\u00fclendi\u011finde, sahnedeki nesnelerin \u00fczerinde g\u00f6sterilir.
config.name.gridColor = Izgara rengi
config.description.gridColor = \u00c7izilen \u0131zgaran\u0131n rengi.
config.name.guidesColor = K\u0131lavuz rengi
config.description.guidesColor = \u00c7izilen k\u0131lavuzlar\u0131n rengi.
config.name.gridSnapAccuracy = Izgara tutturma do\u011frulu\u011fu
config.description.gridSnapAccuracy = Tutturulmak i\u00e7in imlecin \u0131zgaraya olan uzakl\u0131\u011f\u0131 ne kadar olmal\u0131d\u0131r?
config.name.guidesSnapAccuracy = Izgara hizalama do\u011frulu\u011fu
config.description.guidesSnapAccuracy = Tutturulmak i\u00e7in imlecin k\u0131lavuza olan uzakl\u0131\u011f\u0131 ne kadar olmal\u0131d\u0131r?
config.name.snapAlignObjectHorizontalSpace = Izgara hizalama nesnesi yatay aral\u0131\u011f\u0131
config.description.snapAlignObjectHorizontalSpace = Hizalama tutturma s\u0131ras\u0131nda nesneler aras\u0131ndaki yatay bo\u015fluklar.
config.name.snapAlignObjectVerticalSpace = Izgara hizalama nesnesi dikey aral\u0131\u011f\u0131
config.description.snapAlignObjectVerticalSpace = Hizalama tutturma s\u0131ras\u0131nda nesneler aras\u0131ndaki dikey bo\u015fluklar.
config.name.snapAlignStageBorder = Sahne alan\u0131 kenarl\u0131\u011f\u0131n\u0131 hizala
config.description.snapAlignStageBorder = Hizalama s\u0131ras\u0131nda kenarl\u0131k aras\u0131ndaki bo\u015fluk.
config.name.snapAlignCenterAlignmentHorizontal = Yatay merkez hizalamas\u0131n\u0131 hizala
config.description.snapAlignCenterAlignmentHorizontal = Nesnenin yatay olarak merkez hizalamas\u0131n\u0131 etkinle\u015ftirir.
config.name.snapAlignCenterAlignmentVertical = Dikey merkez hizalamas\u0131n\u0131 hizala
config.description.snapAlignCenterAlignmentVertical = Nesnenin dikey olarak merkez hizalamas\u0131n\u0131 etkinle\u015ftirir.
