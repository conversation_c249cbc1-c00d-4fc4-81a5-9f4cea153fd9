<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.3" maxVersion="1.8" type="org.netbeans.modules.form.forminfo.JDialogFormInfo">
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="2"/>
    <Property name="title" type="java.lang.String" editor="org.netbeans.modules.i18n.form.FormI18nStringEditor">
      <ResourceString bundle="com/jpexs/decompiler/flash/gui/locales/FontPreviewDialog.properties" key="fontPreview.dialog.title" replaceFormat="java.util.ResourceBundle.getBundle(&quot;{bundleNameSlashes}&quot;).getString(&quot;{key}&quot;)"/>
    </Property>
    <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
      <Dimension value="[1024, 512]"/>
    </Property>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
    <SyntheticProperty name="generateCenter" type="boolean" value="false"/>
  </SyntheticProperties>
  <Events>
    <EventHandler event="componentMoved" listener="java.awt.event.ComponentListener" parameters="java.awt.event.ComponentEvent" handler="formComponentMoved"/>
    <EventHandler event="componentResized" listener="java.awt.event.ComponentListener" parameters="java.awt.event.ComponentEvent" handler="formComponentResized"/>
  </Events>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
  </AuxValues>

  <Layout>
    <DimensionLayout dim="0">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="0" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="0" attributes="0">
                  <Component id="comboBoxSampleTexts" alignment="0" max="32767" attributes="0"/>
                  <Group type="102" alignment="0" attributes="0">
                      <Component id="jLabel1" min="-2" max="-2" attributes="0"/>
                      <EmptySpace max="-2" attributes="0"/>
                      <Component id="labelFontName" min="-2" max="-2" attributes="0"/>
                      <EmptySpace min="-2" pref="519" max="-2" attributes="0"/>
                  </Group>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
          </Group>
          <Component id="jScrollPane1" alignment="0" max="32767" attributes="0"/>
      </Group>
    </DimensionLayout>
    <DimensionLayout dim="1">
      <Group type="103" groupAlignment="0" attributes="0">
          <Group type="102" alignment="1" attributes="0">
              <EmptySpace max="-2" attributes="0"/>
              <Component id="comboBoxSampleTexts" min="-2" max="-2" attributes="0"/>
              <EmptySpace max="-2" attributes="0"/>
              <Group type="103" groupAlignment="3" attributes="0">
                  <Component id="jLabel1" alignment="3" min="-2" max="-2" attributes="0"/>
                  <Component id="labelFontName" alignment="3" min="-2" max="-2" attributes="0"/>
              </Group>
              <EmptySpace max="-2" attributes="0"/>
              <Component id="jScrollPane1" pref="431" max="32767" attributes="0"/>
          </Group>
      </Group>
    </DimensionLayout>
  </Layout>
  <SubComponents>
    <Container class="javax.swing.JScrollPane" name="jScrollPane1">

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JScrollPaneSupportLayout"/>
      <SubComponents>
        <Container class="javax.swing.JPanel" name="jPanel1">

          <Layout>
            <DimensionLayout dim="0">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" alignment="0" attributes="0">
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="jLabel2" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel3" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel4" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel5" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel6" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel7" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel8" alignment="0" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="0" attributes="0">
                          <Component id="labelSample72" min="-2" max="-2" attributes="0"/>
                          <Component id="labelSample18" min="-2" max="-2" attributes="0"/>
                          <Component id="labelSample12" min="-2" max="-2" attributes="0"/>
                          <Component id="labelSample24" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="labelSample36" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="labelSample48" alignment="0" min="-2" max="-2" attributes="0"/>
                          <Component id="labelSample60" alignment="0" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace pref="692" max="32767" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
            <DimensionLayout dim="1">
              <Group type="103" groupAlignment="0" attributes="0">
                  <Group type="102" attributes="0">
                      <EmptySpace min="-2" max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="labelSample12" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel2" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="labelSample18" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel3" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="labelSample24" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel4" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="labelSample36" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel5" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="labelSample48" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel6" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="labelSample60" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel7" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                      <Group type="103" groupAlignment="3" attributes="0">
                          <Component id="labelSample72" alignment="3" min="-2" max="-2" attributes="0"/>
                          <Component id="jLabel8" alignment="3" min="-2" max="-2" attributes="0"/>
                      </Group>
                      <EmptySpace max="-2" attributes="0"/>
                  </Group>
              </Group>
            </DimensionLayout>
          </Layout>
          <SubComponents>
            <Component class="javax.swing.JLabel" name="labelSample72">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Tahoma" size="72" style="0"/>
                </Property>
                <Property name="text" type="java.lang.String" value="---"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="labelSample60">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Tahoma" size="60" style="0"/>
                </Property>
                <Property name="text" type="java.lang.String" value="---"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="labelSample12">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Tahoma" size="12" style="0"/>
                </Property>
                <Property name="text" type="java.lang.String" value="---"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="labelSample24">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Tahoma" size="24" style="0"/>
                </Property>
                <Property name="text" type="java.lang.String" value="---"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="labelSample48">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Tahoma" size="48" style="0"/>
                </Property>
                <Property name="text" type="java.lang.String" value="---"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="labelSample36">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Tahoma" size="36" style="0"/>
                </Property>
                <Property name="text" type="java.lang.String" value="---"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="labelSample18">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Tahoma" size="18" style="0"/>
                </Property>
                <Property name="text" type="java.lang.String" value="---"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="jLabel2">
              <Properties>
                <Property name="text" type="java.lang.String" value="12"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="jLabel3">
              <Properties>
                <Property name="text" type="java.lang.String" value="18"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="jLabel4">
              <Properties>
                <Property name="text" type="java.lang.String" value="24"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="jLabel5">
              <Properties>
                <Property name="text" type="java.lang.String" value="36"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="jLabel6">
              <Properties>
                <Property name="text" type="java.lang.String" value="48"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="jLabel7">
              <Properties>
                <Property name="text" type="java.lang.String" value="60"/>
              </Properties>
            </Component>
            <Component class="javax.swing.JLabel" name="jLabel8">
              <Properties>
                <Property name="text" type="java.lang.String" value="72"/>
              </Properties>
            </Component>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
    <Component class="javax.swing.JComboBox" name="comboBoxSampleTexts">
      <Properties>
        <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.RADConnectionPropertyEditor">
          <Connection code="getModel()" type="code"/>
        </Property>
      </Properties>
      <Events>
        <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="comboBoxSampleTextsItemStateChanged"/>
      </Events>
    </Component>
    <Component class="javax.swing.JLabel" name="jLabel1">
      <Properties>
        <Property name="text" type="java.lang.String" editor="org.netbeans.modules.i18n.form.FormI18nStringEditor">
          <ResourceString bundle="com/jpexs/decompiler/flash/gui/locales/MainFrame.properties" key="font.name" replaceFormat="java.util.ResourceBundle.getBundle(&quot;{bundleNameSlashes}&quot;).getString(&quot;{key}&quot;)"/>
        </Property>
      </Properties>
    </Component>
    <Component class="javax.swing.JLabel" name="labelFontName">
      <Properties>
        <Property name="text" type="java.lang.String" value="-"/>
      </Properties>
    </Component>
  </SubComponents>
</Form>
