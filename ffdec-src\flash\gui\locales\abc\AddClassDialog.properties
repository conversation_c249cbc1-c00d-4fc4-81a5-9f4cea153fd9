# Copyright (C) 2021 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = Add script
button.ok = OK
button.cancel = Cancel
classname = Fully qualified class name:
#after 16.3.1
button.proceed = Proceed
abc.where = Where to create byte code:
abc.where.existing = Existing DoABC tag
abc.where.new = New DoABC tag
