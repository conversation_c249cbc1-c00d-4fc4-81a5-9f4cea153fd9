# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
rename.type = \u0421\u043f\u043e\u0441\u0456\u0431 \u043f\u0435\u0440\u0435\u0439\u043c\u0435\u043d\u0443\u0432\u0430\u043d\u043d\u044f:
rename.type.typenumber = \u0422\u0438\u043f + \u043f\u043e\u0440\u044f\u0434\u043a\u043e\u0432\u044b\u0439 \u043d\u043e\u043c\u0435\u0440 (class_27, method_456,...)
rename.type.randomword = \u0412\u0438\u043f\u0430\u0434\u043a\u043e\u0432\u0435 \u0441\u043b\u043e\u0432\u043e (abada, kof, supo, kosuri,...)
dialog.title = \u041f\u0435\u0440\u0435\u0439\u043c\u0435\u043d\u0443\u0432\u0430\u0442\u0438 \u0456\u0434\u0435\u043d\u0442\u0438\u0444\u0456\u043a\u0430\u0442\u043e\u0440\u0438
button.ok = \u0413\u0430\u0440\u0430\u0437\u0434
button.cancel = \u0421\u043a\u0430\u0441\u0443\u0432\u0430\u0442\u0438
