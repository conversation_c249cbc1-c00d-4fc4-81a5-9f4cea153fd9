# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Proj\u00edt v\u0161echny t\u0159\u00eddy
dialog.title = Deobfuskace P-k\u00f3du
deobfuscation.level = \u00darove\u0148 deobfuskace k\u00f3du:
deobfuscation.removedeadcode = Odstranit nepou\u017eit\u00fd k\u00f3d
deobfuscation.removetraps = Odstranit pasti
deobfuscation.restorecontrolflow = Obnovit control flow
button.ok = OK
button.cancel = Storno
deobfuscation.scope = Rozsah:
deobfuscation.scope.method = Aktu\u00e1ln\u00ed metoda
deobfuscation.scope.script = Aktu\u00e1ln\u00ed skript
deobfuscation.scope.swf = Cel\u00e9 SWF
warning.modify = VAROV\u00c1N\u00cd: Tato akce modifikuje dan\u00fd SWF soubor.\r\nPokud chcete jen deobfuskaci pro zobrazen\u00ed,\r\npou\u017eijte volbu "Automatick\u00e1 deobfuskace" v Nastaven\u00ed\r\nnebo drobnou ikonku s pilulkou nad editorem skript\u016f.
