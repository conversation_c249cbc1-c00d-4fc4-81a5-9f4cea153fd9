# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%total% \u6587\u5b57\u306e\u3046\u3061\u306e %available% \u6587\u5b57)
dialog.title = \u30d5\u30a9\u30f3\u30c8\u306e\u57cb\u3081\u8fbc\u307f
label.individual = \u500b\u3005\u306e\u6587\u5b57:
button.loadfont = \u30b9\u30c8\u30ec\u30fc\u30b8\u304b\u3089\u30d5\u30a9\u30f3\u30c8\u3092\u8aad\u307f\u8fbc\u3080...
filter.ttf = True Type \u30d5\u30a9\u30f3\u30c8\u30d5\u30a1\u30a4\u30eb (*.ttf)
error.invalidfontfile = \u7121\u52b9\u306a\u30d5\u30a9\u30f3\u30c8\u30d5\u30a1\u30a4\u30eb
error.cannotreadfontfile = \u30d5\u30a9\u30f3\u30c8\u30d5\u30a1\u30a4\u30eb\u304c\u8aad\u307f\u8fbc\u307e\u305b\u3093
installed = \u30a4\u30f3\u30b9\u30c8\u30fc\u30eb\u6e08\u307f: 
ttffile.noselection = TTF \u30d5\u30a1\u30a4\u30eb: <\u672a\u9078\u629e>
ttffile.selection = TTF \u30d5\u30a1\u30a4\u30eb: %fontname% (%filename%)
allcharacters = \u5168\u3066\u306e\u6587\u5b57 (%available% \u6587\u5b57)
ascentdescentleading = ascent, descent, leading \u306e\u8a2d\u5b9a
