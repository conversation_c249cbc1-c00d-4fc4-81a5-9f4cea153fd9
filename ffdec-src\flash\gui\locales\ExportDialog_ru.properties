# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = \u0424\u043e\u0440\u043c\u044b
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = \u041a\u0430\u043d\u0432\u0430\u0441 HTML5
texts = \u0422\u0435\u043a\u0441\u0442\u044b
texts.plain = \u041e\u0431\u044b\u0447\u043d\u044b\u0439 \u0442\u0435\u043a\u0441\u0442
texts.formatted = \u0424\u043e\u0440\u043c\u0430\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u043d\u044b\u0439 \u0442\u0435\u043a\u0441\u0442
texts.svg = SVG
images = \u0418\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u044f
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = \u0412\u0438\u0434\u0435\u043e
movies.flv = FLV (\u0411\u0435\u0437 \u0437\u0432\u0443\u043a\u0430)
sounds = \u0417\u0432\u0443\u043a\u0438
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (\u0422\u043e\u043b\u044c\u043a\u043e \u0437\u0432\u0443\u043a)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Scripts
scripts.as = ActionScript
scripts.pcode = P-code
scripts.pcode_hex=P-code \u0441 Hex
scripts.hex = Hex
binaryData = \u0414\u0432\u043e\u0438\u0447\u043d\u044b\u0435 \u0434\u0430\u043d\u043d\u044b\u0435
binaryData.raw = Raw
dialog.title = \u042d\u043a\u0441\u043f\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c...
button.ok = OK
button.cancel = \u041e\u0442\u043c\u0435\u043d\u0430
morphshapes = Morphshape'\u044b
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = \u041a\u0430\u043d\u0432\u0430\u0441 HTML5
frames = \u041a\u0430\u0434\u0440\u044b
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = \u041a\u0430\u043d\u0432\u0430\u0441 HTML5
frames.pdf = PDF
frames.bmp = BMP
fonts = \u0428\u0440\u0438\u0444\u0442\u044b
fonts.ttf = TTF
fonts.woff = WOFF
zoom = \u041c\u0430\u0441\u0448\u0442\u0430\u0431
zoom.percent = %
zoom.invalid = \u041d\u0435\u0432\u0435\u0440\u043d\u043e\u0435 \u0437\u043d\u0430\u0447\u0435\u043d\u0438\u0435 \u043c\u0430\u0441\u0448\u0442\u0430\u0431\u0430.
symbolclass = \u041a\u043b\u0430\u0441\u0441 \u0441\u0438\u043c\u0432\u043e\u043b\u0430
symbolclass.csv = CSV
