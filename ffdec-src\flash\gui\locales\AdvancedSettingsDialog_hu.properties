# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Halad\u00f3 be\u00e1ll\u00edt\u00e1sok
advancedSettings.restartConfirmation = N\u00e9h\u00e1ny m\u00f3dos\u00edt\u00e1s \u00e9rv\u00e9nybel\u00e9p\u00e9s\u00e9hez a programot \u00fajra kell ind\u00edtani. Szeretn\u00e9 \u00fajraind\u00edtani most?
advancedSettings.columns.name = N\u00e9v
advancedSettings.columns.value = \u00c9rt\u00e9k
advancedSettings.columns.description = Le\u00edr\u00e1s
default = alap\u00e9rt\u00e9k
config.group.name.export = Export\u00e1l\u00e1s
config.group.description.export = Export\u00e1l\u00e1s be\u00e1ll\u00edt\u00e1sai
config.group.name.script = Szkriptek
config.group.description.script = ActionScript visszaford\u00edt\u00e1ssal kapcsolatos
config.group.name.update = Friss\u00edt\u00e9sek
config.group.description.update = Friss\u00edt\u00e9sek keres\u00e9se
config.group.name.format = Form\u00e1z\u00e1s
config.group.description.format = ActionScript k\u00f3d form\u00e1z\u00e1s
config.group.name.limit = Korl\u00e1tok
config.group.description.limit = Visszaford\u00edt\u00e1si korl\u00e1tok obfuszk\u00e1lt k\u00f3dokra, stb.
config.group.name.ui = Fel\u00fclet
config.group.description.ui = Felhaszn\u00e1l\u00f3i fel\u00fclet be\u00e1ll\u00edt\u00e1sai
config.group.name.debug = Hibakeres\u00e9s
config.group.description.debug = Hibakeres\u00e9si be\u00e1ll\u00edt\u00e1sok
config.group.name.display = Megjelen\u00edt\u00e9s
config.group.description.display = Flash objektum megjelen\u00edt\u00e9s, stb.
config.group.name.decompilation = Visszaford\u00edt\u00e1s
config.group.description.decompilation = Glob\u00e1lis visszaford\u00edt\u00e1ssal kapcsolatos funkci\u00f3k
config.group.name.other = Egy\u00e9b
config.group.description.other = Egy\u00e9b kategoriz\u00e1latlan be\u00e1ll\u00edt\u00e1sok
config.name.openMultipleFiles = T\u00f6bb f\u00e1jl megnyit\u00e1sa
config.description.openMultipleFiles = Enged\u00e9lyezi t\u00f6bb f\u00e1jl megnyit\u00e1s\u00e1t egy ablakban
config.name.decompile = ActionScript forr\u00e1s mutat\u00e1sa
config.description.decompile = Kikapcsolhatja az AS visszaford\u00edt\u00e1s\u00e1t, ekkor a P-k\u00f3d jelenik meg
config.name.dumpView = Dump N\u00e9zet
config.description.dumpView = Nyers adatok mutat\u00e1sa
config.name.useHexColorFormat = Hexa sz\u00edn form\u00e1tum
config.description.useHexColorFormat = A sz\u00ednek hexa form\u00e1tum\u00fa mutat\u00e1sa
config.name.parallelSpeedUp = P\u00e1rhuzamos gyors\u00edt\u00e1s
config.description.parallelSpeedUp = P\u00e1rhuzamos\u00edt\u00e1s fel tudja gyors\u00edtani a visszaford\u00edt\u00e1st
config.name.parallelSpeedUpThreadCount = Sz\u00e1lak sz\u00e1ma (0 = auto)
config.description.parallelSpeedUpThreadCount = Sz\u00e1lak sz\u00e1ma a p\u00e1rhuzamos gyors\u00edt\u00e1sn\u00e1l. 0 = processor count - 1.
config.name.autoDeobfuscate = Automatikus deobfuszk\u00e1l\u00e1s
config.description.autoDeobfuscate = Futtassa a deobfuszk\u00e1l\u00e1st minden f\u00e1jlon az ActionScript visszaford\u00edt\u00e1sa el\u0151tt
config.name.cacheOnDisk = Lemez gyors\u00edt\u00f3t\u00e1r haszn\u00e1lata
config.description.cacheOnDisk = Gyors\u00edt\u00f3t\u00e1razza a m\u00e1r visszaford\u00edtott r\u00e9szeket a merevlemezre a mem\u00f3ria helyett
config.name.internalFlashViewer = Saj\u00e1t Flash n\u00e9z\u0151ke haszn\u00e1lata
config.description.internalFlashViewer = JPEXS Flash N\u00e9z\u0151ke haszn\u00e1lata a standard Flash Player helyett a flash r\u00e9szek megjelen\u00edt\u00e9s\u00e9re
config.name.gotoMainClassOnStartup = F\u0151 oszt\u00e1lyhoz ugr\u00e1s ind\u00edt\u00e1skor (AS3)
config.description.gotoMainClassOnStartup = A dokumentum oszt\u00e1lyhoz navig\u00e1l\u00e1s AS3 f\u00e1jl eset\u00e9n az SWF megnyit\u00e1sakor
config.name.autoRenameIdentifiers = Azonos\u00edt\u00f3k automatikus \u00e1tnevez\u00e9se
config.description.autoRenameIdentifiers = Automatikusan \u00e1tnevezi az \u00e9rv\u00e9nytelen azonos\u00edt\u00f3kat az SWF bet\u00f6lt\u00e9sekor
config.name.offeredAssociation = (Bels\u0151) SWF f\u00e1jlok t\u00e1rs\u00edt\u00e1sa megjelen\u00edtve
config.description.offeredAssociation = Dial\u00f3gus ablak a f\u00e1jl t\u00e1rs\u00edt\u00e1s\u00e1r\u00f3l m\u00e1r meg lett jelen\u00edtve
config.name.decimalAddress = Decim\u00e1lis c\u00edmek haszn\u00e1lata
config.description.decimalAddress = Decim\u00e1lis c\u00edmek haszn\u00e1lata a hexadecim\u00e1lisok helyett
config.name.showAllAddresses = Minden c\u00edm mutat\u00e1sa
config.description.showAllAddresses = Minden ActionScript utas\u00edt\u00e1s c\u00edm mutat\u00e1sa
config.name.useFrameCache = Keret gyors\u00edt\u00f3t\u00e1r haszn\u00e1lata
config.description.useFrameCache = Keretek gyors\u00edt\u00f3t\u00e1rba helyez\u00e9se az \u00fajb\u00f3li renderel\u00e9s megel\u0151z\u00e9s\u00e9hez
config.name.useRibbonInterface = Szalag fel\u00fclet
config.description.useRibbonInterface = Kapcsolja ki a klasszikus fel\u00fclet haszn\u00e1lat\u00e1hoz szalagos men\u00fc n\u00e9lk\u00fcl
config.name.openFolderAfterFlaExport = Mappa megnyit\u00e1sa FLA export\u00e1l\u00e1s ut\u00e1n
config.description.openFolderAfterFlaExport = A kimeneti k\u00f6nyvt\u00e1r megnyit\u00e1sa az FLA f\u00e1jl export\u00e1l\u00e1sa ut\u00e1n
config.name.useDetailedLogging = R\u00e9szletes napl\u00f3z\u00e1s
config.description.useDetailedLogging = R\u00e9szletes hiba\u00fczenetek \u00e9s inform\u00e1ci\u00f3k napl\u00f3z\u00e1sa hibakeres\u00e9ssi c\u00e9lb\u00f3l
config.name._debugMode=FFDec hibakeres\u00e9si m\u00f3dban
config.description._debugMode=M\u00f3d az FFDec hibakeres\u00e9s\u00e9hez. Bekapcsolja a hibakeres\u00e9si men\u00fct. Ennek nincs k\u00f6ze a hibakeres\u00e9s funkci\u00f3hoz.
config.name.resolveConstants = \u00c1lland\u00f3k felold\u00e1sa AS1/2 p-k\u00f3d eset\u00e9n
config.description.resolveConstants = Kapcsolja ezt ki a 'constantxx' \u00e9rt\u00e9kek mutat\u00e1s\u00e1hoz a val\u00f3s \u00e9rt\u00e9kek helyett a P-k\u00f3d ablakban
config.name.sublimiter = K\u00f3d sub korl\u00e1t
config.description.sublimiter = K\u00f3d sub korl\u00e1t obfuszk\u00e1lt k\u00f3dn\u00e1l.
config.name.exportTimeout = Teljes export\u00e1l\u00e1si id\u0151korl\u00e1t (m\u00e1sodperc)
config.description.exportTimeout = A visszaford\u00edt\u00f3 le\u00e1ll\u00edtja az export\u00e1l\u00e1st miut\u00e1n el\u00e9ri ezt az id\u0151t
config.name.decompilationTimeoutFile = F\u00e1jl visszaford\u00edt\u00e1si id\u0151korl\u00e1t (m\u00e1sodperc)
config.description.decompilationTimeoutFile = A visszaford\u00edt\u00f3 le\u00e1ll\u00edtja az ActionScript visszaford\u00edt\u00e1s\u00e1t miut\u00e1n el\u00e9ri ezt az id\u0151t
config.name.paramNamesEnable = Param\u00e9ter nevek enged\u00e9lyez\u00e9se AS3 eset\u00e9n
config.description.paramNamesEnable = Param\u00e9ter nevek haszn\u00e1lata a visszaford\u00edt\u00e1sn\u00e1l probl\u00e9m\u00e1khoz vezethet mivel a hivatalos programok mint pl. Flash CS 5.5 rossz param\u00e9ter n\u00e9v indexeket sz\u00far be
config.name.displayFileName = SWF n\u00e9v mutat\u00e1sa a c\u00edmsorban
config.description.displayFileName = SWF f\u00e1jl/url n\u00e9v mutat\u00e1sa az ablak c\u00edmsor\u00e1ban (Ut\u00e1na k\u00e9sz\u00edthet k\u00e9perny\u0151k\u00e9peket)
config.name._debugCopy=\u00dajraford\u00edt\u00e1sos hibakeres\u00e9s FFDec-ben
config.description._debugCopy=Megpr\u00f3b\u00e1lja \u00fajraford\u00edtani az SWF f\u00e1jlt megnyit\u00e1s ut\u00e1n hogy megbizonyosodjon arr\u00f3l, hogy ugyanazt a bin\u00e1ris k\u00f3dot \u00e1ll\u00edtja el\u0151. Csak FFDec HIBAKERES\u00c9SHEZ haszn\u00e1lja!
config.name.dumpTags = Tagek ki\u00edr\u00e1sa a parancssorra
config.description.dumpTags = Tagek ki\u00edr\u00e1sa a parancssorra az SWF f\u00e1jl olvas\u00e1sakor
config.name.decompilationTimeoutSingleMethod = AS3: Egyedi met\u00f3dus visszaford\u00edt\u00e1si id\u0151korl\u00e1t (m\u00e1sodperc)
config.description.decompilationTimeoutSingleMethod = A visszaford\u00edt\u00f3 le\u00e1ll\u00edtja az ActionScript visszaford\u00edt\u00e1s\u00e1t miut\u00e1n el\u00e9ri ezt az id\u0151t egy met\u00f3dusn\u00e1l
config.name.lastRenameType = (Bels\u0151) Utols\u00f3 \u00e1tevez\u00e9si t\u00edpus
config.description.lastRenameType = Utolj\u00e1ra haszn\u00e1lt t\u00edpus az azonos\u00edt\u00f3k \u00e1tnevez\u00e9s\u00e9re
config.name.lastSaveDir = (Bels\u0151) Utols\u00f3 ment\u00e9si k\u00f6nyvt\u00e1r
config.description.lastSaveDir = Utolj\u00e1ra haszn\u00e1lt ment\u00e9si k\u00f6nyvt\u00e1r
config.name.lastOpenDir = (Bels\u0151) Utols\u00f3 megnyit\u00e1si k\u00f6nyvt\u00e1r
config.description.lastOpenDir = Utolj\u00e1ra haszn\u00e1lt megnyit\u00e1si k\u00f6nyvt\u00e1r
config.name.lastExportDir = (Bels\u0151) Utols\u00f3 export\u00e1l\u00e1si k\u00f6nyvt\u00e1r
config.description.lastExportDir = Utolj\u00e1ra haszn\u00e1lt export\u00e1l\u00e1si k\u00f6nyvt\u00e1r
config.name.locale = Nyelv
config.description.locale = Helysz\u00edn azonos\u00edt\u00f3
config.name.registerNameFormat = Regiszter v\u00e1ltoz\u00f3 form\u00e1tum
config.description.registerNameFormat = Helyi regiszter v\u00e1ltoz\u00f3n\u00e9v form\u00e1tum. Haszn\u00e1lja a %d-t a regiszter sz\u00e1m\u00e1hoz.
config.name.maxRecentFileCount = Utolj\u00e1ra haszn\u00e1lt f\u00e1jlok maxim\u00e1lis sz\u00e1ma
config.description.maxRecentFileCount = Utolj\u00e1ra haszn\u00e1lt f\u00e1jlok maxim\u00e1lis sz\u00e1ma
config.name.recentFiles = (Bels\u0151) Utolj\u00e1ra haszn\u00e1lt f\u00e1jlok
config.description.recentFiles = Utolj\u00e1ra megnyitott f\u00e1jlok
config.name.fontPairingMap = (Bels\u0151) Bet\u0171t\u00edpus p\u00e1rok import\u00e1l\u00e1shoz
config.description.fontPairingMap = Bet\u0171t\u00edpus p\u00e1rok \u00faj karakterek import\u00e1l\u00e1s\u00e1hoz
config.name.lastUpdatesCheckDate = (Bels\u0151) Utols\u00f3 friss\u00edt\u00e9s keres\u00e9s\u00e9nek d\u00e1tuma
config.description.lastUpdatesCheckDate = Utols\u00f3 friss\u00edt\u00e9s keres\u00e9s\u00e9nek d\u00e1tuma a kiszolg\u00e1l\u00f3r\u00f3l
config.name.gui.window.width = (Bels\u0151) Legut\u00f3bbi ablak sz\u00e9less\u00e9g
config.description.gui.window.width = Legut\u00f3bb mentett ablak sz\u00e9less\u00e9ge
config.name.gui.window.height = (Bels\u0151) Legut\u00f3bbi ablak magass\u00e1ga
config.description.gui.window.height = Legut\u00f3bb mentett ablak magass\u00e1ga
config.name.gui.window.maximized.horizontal = (Bels\u0151) Az ablak v\u00edzszintesen maxim\u00e1lis m\u00e9ret\u0171
config.description.gui.window.maximized.horizontal = Ablak utols\u00f3 \u00e1llapota - v\u00edzszintesen maxim\u00e1lis
config.name.gui.window.maximized.vertical = (Bels\u0151) Az ablak f\u00fcgg\u0151legesen maxim\u00e1lis m\u00e9ret\u0171
config.description.gui.window.maximized.vertical = Ablak utols\u00f3 \u00e1llapota - f\u00fcgg\u0151legesen maxim\u00e1lis
config.name.gui.avm2.splitPane.dividerLocationPercent=(Bels\u0151) AS3 oszt\u00f3 helyzete
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Bels\u0151) AS1/2 oszt\u00f3 helyzete
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Bels\u0151) El\u0151n\u00e9zet oszt\u00f3 helyzete
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Bels\u0151) Oszt\u00f3 helyzete 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Bels\u0151) Oszt\u00f3 helyzete 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Ment\u00e9s EXE-k\u00e9nt nagy\u00edt\u00e1s m\u00f3dja
config.description.saveAsExeScaleMode = Nagy\u00edt\u00e1s m\u00f3dja EXE export eset\u00e9n
config.name.syntaxHighlightLimit = Szintaxis kiemel\u00e9s maxim\u00e1lis karakter sz\u00e1m
config.description.syntaxHighlightLimit = Maxim\u00e1lis karakter sz\u00e1m a szintaxis kiemel\u00e9s futtat\u00e1s\u00e1hoz
config.name.guiFontPreviewSampleText = (Bels\u0151) Utols\u00f3 bet\u0171t\u00edpus el\u0151n\u00e9zet p\u00e9lda sz\u00f6veg
config.description.guiFontPreviewSampleText = Utols\u00f3 bet\u0171t\u00edpus p\u00e9lda sz\u00f6veg indexe a list\u00e1ban
config.name.gui.fontPreviewWindow.width = (Bels\u0151) Bet\u0171t\u00edpus el\u0151n\u00e9zet ablak utols\u00f3 sz\u00e9less\u00e9ge
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Bels\u0151) Bet\u0171t\u00edpus el\u0151n\u00e9zet ablak utols\u00f3 magass\u00e1ga
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Bels\u0151) Bet\u0171t\u00edpus el\u0151n\u00e9zet ablak utols\u00f3 X poz\u00edci\u00f3ja
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Bels\u0151) Bet\u0171t\u00edpus el\u0151n\u00e9zet ablak utols\u00f3 Y poz\u00edci\u00f3ja
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Karakterek sz\u00e1ma beh\u00faz\u00e1sonk\u00e9nt
config.description.formatting.indent.size = Sz\u00f3k\u00f6z\u00f6k (vagy tab-ok) sz\u00e1ma egy beh\u00faz\u00e1sn\u00e1l
config.name.formatting.indent.useTabs = Tab-ok beh\u00faz\u00e1shoz
config.description.formatting.indent.useTabs = Tab-ok haszn\u00e1lata sz\u00f3k\u00f6z\u00f6k helyett beh\u00faz\u00e1sokn\u00e1l
config.name.beginBlockOnNewLine = Kapcsos z\u00e1r\u00f3jel \u00faj sorban
config.description.beginBlockOnNewLine = Blokk kezdeti kapcsos z\u00e1r\u00f3jel \u00faj sorban
config.name.check.updates.delay = Friss\u00edt\u00e9s keres\u00e9si v\u00e1rakoz\u00e1s
config.description.check.updates.delay = Minim\u00e1lis id\u0151 az automatikus friss\u00edt\u00e9sek keres\u00e9s\u00e9hez az alkalmaz\u00e1s ind\u00edt\u00e1sakor
config.name.check.updates.stable = Stabil verzi\u00f3k ellen\u0151rz\u00e9se
config.description.check.updates.stable = Stabil verzi\u00f3 friss\u00edt\u00e9sek keres\u00e9se
config.name.check.updates.nightly = \u00c9jszakai verzi\u00f3k ellen\u0151rz\u00e9se
config.description.check.updates.nightly = \u00c9jszakai verzi\u00f3 friss\u00edt\u00e9sek keres\u00e9se
config.name.check.updates.enabled = Friss\u00edt\u00e9sek keres\u00e9se enged\u00e9lyezve
config.description.check.updates.enabled = Friss\u00edt\u00e9sek automatikus keres\u00e9se az alkalmaz\u00e1s ind\u00edt\u00e1sakor
config.name.export.formats = (Bels\u0151) Export\u00e1l\u00e1si form\u00e1tumok
config.description.export.formats = Utolj\u00e1ra haszn\u00e1lt export\u00e1l\u00e1si form\u00e1tumok
config.name.textExportSingleFile = Sz\u00f6vegek export\u00e1l\u00e1sa egy f\u00e1jlba
config.description.textExportSingleFile = Sz\u00f6vegek export\u00e1l\u00e1sa egyetlen f\u00e1jlba t\u00f6bb f\u00e1jl helyett
config.name.textExportSingleFileSeparator = Sz\u00f6vegek elv\u00e1laszt\u00e1sa egy f\u00e1jlba t\u00f6rt\u00e9n\u0151 export\u00e1l\u00e1s eset\u00e9n
config.description.textExportSingleFileSeparator = Sz\u00f6veg, melyet besz\u00far a sz\u00f6vegek k\u00f6z\u00e9 egyetlen f\u00e1jlba t\u00f6rt\u00e9n\u0151 sz\u00f6veg export\u00e1l\u00e1skor
config.name.textExportSingleFileRecordSeparator = Rekordok elv\u00e1laszt\u00e1sa egy f\u00e1jlba t\u00f6rt\u00e9n\u0151 export\u00e1l\u00e1s eset\u00e9n
config.description.textExportSingleFileRecordSeparator = Sz\u00f6veg, melyet besz\u00far a rekordok k\u00f6z\u00e9 egyetlen f\u00e1jlba t\u00f6rt\u00e9n\u0151 sz\u00f6veg export\u00e1l\u00e1skor
config.name.warning.experimental.as12edit=Figyelmeztet\u00e9s AS1/2 direkt szerkeszt\u00e9se eset\u00e9n
config.description.warning.experimental.as12edit=Figyelmeztet\u00e9s megjelen\u00edt\u00e9se AS1/2 k\u00eds\u00e9rleti szerkeszt\u00e9se eset\u00e9n
config.name.warning.experimental.as3edit=Figyelmeztet\u00e9s AS3 direkt szerkeszt\u00e9se eset\u00e9n
config.description.warning.experimental.as3edit=Figyelmeztet\u00e9s megjelen\u00edt\u00e9se AS3 k\u00eds\u00e9rleti szerkeszt\u00e9se eset\u00e9n
config.name.packJavaScripts = JavaScript csomagol\u00e1s
config.description.packJavaScripts = JavaScript csomagol\u00f3 futtat\u00e1sa V\u00e1szon Export\u00e1l\u00e1skor l\u00e9trehozott szkriptekre.
config.name.textExportExportFontFace = Font-face haszn\u00e1lata SVG export\u00e1l\u00e1skor
config.description.textExportExportFontFace = Bet\u0171t\u00edpus f\u00e1jlok be\u00e1gyaz\u00e1sa az SVG f\u00e1jlokba \u00e9s font-face haszn\u00e1lata alakzatok helyett
config.name.lzmaFastBytes = LZMA fast bytes (\u00e9rv\u00e9nyes \u00e9rt\u00e9kek: 5-255)
config.description.lzmaFastBytes = Az LZMA t\u00f6m\u00f6r\u00edt\u0151 "Fast bytes" param\u00e9tere
config.name.pluginPath = Plugin Path
config.description.pluginPath = -   
config.name.showMethodBodyId = Method body azonos\u00edt\u00f3 mutat\u00e1sa
config.description.showMethodBodyId = Megmutatja a methodbody azonos\u00edt\u00f3t a parancssori import\u00e1l\u00e1shoz
config.name.export.zoom = (Bels\u0151) Export zoom
config.description.export.zoom = Utolj\u00e1ra haszn\u00e1lt export zoom
config.name.debuggerPort = Debugger port
config.description.debuggerPort = Socket hibakeres\u00e9shez haszn\u00e1lt port
config.name.displayDebuggerInfo = (Bels\u0151) Hibakeres\u00e9si inform\u00e1ci\u00f3 mutat\u00e1sa
config.description.displayDebuggerInfo = Hibakeres\u00e9si inform\u00e1ci\u00f3 mutat\u00e1sa bekapcsol\u00e1s el\u0151tt
config.name.randomDebuggerPackage = V\u00e9letlen csomag n\u00e9v haszn\u00e1lata hibakeres\u00e9sn\u00e9l
config.description.randomDebuggerPackage = \u00c1tnevezi a hibakeres\u00e9si csomag nev\u00e9t v\u00e9letlen n\u00e9vre hogy nehezebben lehessen detekt\u00e1lni ActionScript-b\u0151l
config.name.lastDebuggerReplaceFunction = (Bels\u0151) Utolj\u00e1ra kiv\u00e1lasztott "trace" helyettes\u00edt\u00e9s
config.description.lastDebuggerReplaceFunction = Utolj\u00e1ra kiv\u00e1lasztott f\u00fcggv\u00e9ny n\u00e9v a "trace" helyettes\u00edts\u00e9re hibakeres\u00e9skor
config.name.getLocalNamesFromDebugInfo = AS3: Helyi regiszter nevek a hibakeres\u00e9si inform\u00e1ci\u00f3b\u00f3l
config.description.getLocalNamesFromDebugInfo = Ha a hibakeres\u00e9si inform\u00e1ci\u00f3 el\u00e9rhet\u0151, akkor \u00e1tnevezi a helyi regisztereket _loc_x_r\u0151l a val\u00f3s n\u00e9vre. Ez kikapcsolhat\u00f3, mert n\u00e9h\u00e1ny obfuszk\u00e1tor \u00e9rv\u00e9nytelen regiszter neveket haszn\u00e1l.
config.name.tagTreeShowEmptyFolders = \u00dcres mapp\u00e1k mutat\u00e1sa
config.description.tagTreeShowEmptyFolders = \u00dcres mapp\u00e1k mutat\u00e1sa a tag f\u00e1ban.
config.name.autoLoadEmbeddedSwfs = Be\u00e1gyazott SWFek automatikus bet\u00f6lt\u00e9se
config.description.autoLoadEmbeddedSwfs = Aut\u00f3matikusan bet\u00f6lti a be\u00e1gyazott SWF-eket a DefineBinaryData tagekb\u0151l.
config.name.overrideTextExportFileName = Sz\u00f6veg export\u00e1l\u00e1s f\u00e1jlnev\u00e9nek fel\u00fcl\u00edr\u00e1sa
config.description.overrideTextExportFileName = Szem\u00e9lyre szabhatod a nev\u00e9t az export\u00e1lt sz\u00f6vegf\u00e1jlnak. Haszn\u00e1ld a {filename} helykit\u00f6lt\u0151t az aktu\u00e1lis SWF nev\u00e9hez.
config.name.showOldTextDuringTextEditing = R\u00e9gi sz\u00f6veg mutat\u00e1sa sz\u00f6veg szerkeszt\u00e9sekor
config.description.showOldTextDuringTextEditing = Mutatja az eredeti sz\u00f6veget sz\u00fcrke sz\u00ednnel az el\u0151n\u00e9zeti ablakban szerkeszt\u00e9s k\u00f6zben.
config.group.name.import = Import\u00e1l\u00e1s
config.group.description.import = Import\u00e1l\u00e1s be\u00e1ll\u00edt\u00e1sai
config.name.textImportResizeTextBoundsMode = Sz\u00f6veg hat\u00e1r \u00e1tm\u00e9retez\u00e9se
config.description.textImportResizeTextBoundsMode = Sz\u00f6veg hat\u00e1r \u00e1tm\u00e9retez\u00e9se sz\u00f6veg m\u00f3dos\u00edt\u00e1sa ut\u00e1n.
config.name.showCloseConfirmation = SWF bez\u00e1r\u00e1si meger\u0151s\u00edt\u00e9s \u00fajb\u00f3li mutat\u00e1sa
config.description.showCloseConfirmation = SWF bez\u00e1r\u00e1si meger\u0151s\u00edt\u00e9s \u00fajb\u00f3li mutat\u00e1sa m\u00f3dosult f\u00e1jlok eset\u00e9n.
config.name.showCodeSavedMessage = K\u00f3d mentve \u00fczenet \u00fajra mutat\u00e1sa
config.description.showCodeSavedMessage = K\u00f3d mentve \u00fczenet \u00fajra mutat\u00e1sa
config.name.showTraitSavedMessage = Trait mentve \u00fczenet \u00fajra mutat\u00e1sa
config.description.showTraitSavedMessage = Trait mentve \u00fczenet \u00fajra mutat\u00e1sa
config.name.updateProxyAddress = Http Proxy c\u00edm a friss\u00edt\u00e9sek keres\u00e9s\u00e9hez
config.description.updateProxyAddress = Http Proxy c\u00edm a friss\u00edt\u00e9sek keres\u00e9s\u00e9hez. Form\u00e1tum: example.com:8080
config.name.editorMode = Szerkeszt\u0151 m\u00f3d
config.description.editorMode = A sz\u00f6veget tartalmaz\u00f3 mez\u0151 szerkeszthet\u0151v\u00e9 t\u00e9tele automatikusan a sz\u00f6veg vagy szkript kiv\u00e1laszt\u00e1sa eset\u00e9n
config.name.autoSaveTagModifications = Tag m\u00f3dos\u00edt\u00e1sainak automatikus ment\u00e9se
config.description.autoSaveTagModifications = V\u00e1ltoz\u00e1sok ment\u00e9se amikor \u00faj tag-et v\u00e1laszt ki a f\u00e1ban
config.name.saveSessionOnExit = Munkamenet ment\u00e9se kil\u00e9p\u00e9skor
config.description.saveSessionOnExit = Elmenti az aktu\u00e1lis munkamenetet \u00e9s vissza\u00e1ll\u00edtja azt az FFDec \u00fajraind\u00edt\u00e1sa ut\u00e1n (csak igazi f\u00e1jlokkal m\u0171k\u00f6dik)
config.name._showDebugMenu=FFDec hibakeres\u00e9si men\u00fc mutat\u00e1sa
config.description._showDebugMenu=Hibakeres\u00e9si men\u00fc mutat\u00e1sa a szalagon a visszaford\u00edt\u00f3 hibakeres\u00e9s\u00e9hez.
config.name.allowOnlyOneInstance = Csak egy FFDec p\u00e9ld\u00e1ny enged\u00e9lyez\u00e9se (Csak Windows OS-en)
config.description.allowOnlyOneInstance = FFDec csak egy p\u00e9ld\u00e1nyban futhat, minden f\u00e1jl egy ablakban lesz megnyitva. Csak Windows oper\u00e1ci\u00f3s rendszeren m\u0171k\u00f6dik.
config.name.scriptExportSingleFile = Szkriptek export\u00e1l\u00e1sa egy f\u00e1jlba
config.description.scriptExportSingleFile = Szkriptek export\u00e1l\u00e1sa egyetlen f\u00e1jlba t\u00f6bb f\u00e1jl helyett
config.name.setFFDecVersionInExportedFont = FFDec verzi\u00f3sz\u00e1m be\u00e1ll\u00edt\u00e1sa az export\u00e1lt bet\u0171t\u00edpusban
config.description.setFFDecVersionInExportedFont = Ha ez a be\u00e1ll\u00edt\u00e1s ki van kapcsolva, az FFDec nem fogja be\u00edrni az aktu\u00e1lis FFDec verzi\u00f3sz\u00e1m\u00e1t az export\u00e1lt bet\u0171t\u00edpus f\u00e1jlba.
config.name.gui.skin = Felhaszn\u00e1l\u00f3i fel\u00fclet b\u0151r
config.description.gui.skin = Kin\u00e9zet
config.name.lastSessionFiles = Utols\u00f3 munkamenet f\u00e1jljai
config.description.lastSessionFiles = Az legut\u00f3bbi munkamenetben megnyitott f\u00e1jlokat tartalmazza
config.name.lastSessionSelection = Utols\u00f3 munkamenet kiv\u00e1lasztott eleme
config.description.lastSessionSelection = Az legut\u00f3bbi munkamenetben kiv\u00e1lasztott elemet tartalmazza
config.name.loopMedia = Zen\u00e9k \u00e9s szpr\u00e1jtok
config.description.loopMedia = Aut\u00f3matikusan \u00fajraind\u00edtja a zen\u00e9k \u00e9s szpr\u00e1jtok lej\u00e1tsz\u00e1s\u00e1t
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Internal) Timeline oszt\u00f3 helyzete
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = K\u00e9pek gyors\u00edt\u00f3t\u00e1rba helyez\u00e9se
config.description.cacheImages = A dek\u00f3dolt k\u00e9peket gyors\u00edt\u00f3t\u00e1rba helyezi
config.name.swfSpecificConfigs = SWF specifikus be\u00e1llt\u00e1sok
config.description.swfSpecificConfigs = Az SWF specifikus be\u00e1llt\u00e1sokat tartalmazza
config.name.exeExportMode = EXE export m\u00f3d
config.description.exeExportMode = EXE export m\u00f3d
config.name.ignoreCLikePackages = FlashCC / Alchemy vagy hasonl\u00f3 csomagok figyelmen k\u00edv\u00fcl hagy\u00e1sa
config.description.ignoreCLikePackages = FlashCC/Alchemy csomagok \u00e1ltal\u00e1ban nem ford\u00edthat\u00f3ak vissza hib\u00e1tlanul. Kikapcsolhatod hogy gyorsabban visszaford\u00edtsa a t\u00f6bbi csomagot.
config.name.overwriteExistingFiles = L\u00e9tez\u0151 f\u00e1jlok fel\u00fcl\u00edr\u00e1sa
config.description.overwriteExistingFiles = Fel\u00fcl\u00edrja a l\u00e9tez\u0151 f\u00e1jlokat export\u00e1l\u00e1s sor\u00e1n. Jelenleg csak az AS2/3 szkriptekre \u00e9rv\u00e9nyes
config.name.smartNumberFormatting = Okos sz\u00e1m form\u00e1z\u00e1s haszn\u00e1lata
config.description.smartNumberFormatting = A speci\u00e1lis sz\u00e1mokat (mint p\u00e9ld\u00e1ul sz\u00ednek \u00e9s id\u0151k) felismeri
config.name.enableScriptInitializerDisplay = (REMOVED) Szkript inicializ\u00e1l\u00f3k megjelen\u00edt\u00e9se
config.description.enableScriptInitializerDisplay = Enged\u00e9lyezi a szkript inicializ\u00e1l\u00f3k megjelen\u00edt\u00e9s\u00e9t \u00e9s szerkeszt\u00e9s\u00e9t. Ez a be\u00e1ll\u00edt\u00e1s hozz\u00e1adhat egy \u00faj sort minden oszt\u00e1lyhoz a kiemel\u00e9shez.
config.name.autoOpenLoadedSWFs = Fut\u00e1s k\u00f6zben bet\u00f6lt\u00f6tt SWF-ek megnyit\u00e1sa (K\u00fcls\u0151 n\u00e9z\u0151ke = Csak WIN)
config.description.autoOpenLoadedSWFs = Automatikusan megnyitja az \u00f6sszes AS3 oszt\u00e1ly bet\u00f6lt\u0151 \u00e1ltal bet\u00f6lt\u00f6tt SWF-t amikor az SWF-t az FFDec k\u00fcls\u0151 n\u00e9z\u0151k\u00e9j\u00e9ben j\u00e1tszuk le. Ez a funkci\u00f3 csak Windowon \u00e9rhet\u0151 el.
config.name.lastSessionFileTitles = Utols\u00f3 munkamenet f\u00e1jl c\u00edmei
config.description.lastSessionFileTitles = Tartalmazza az utols\u00f3 munkamenet megnyitott f\u00e1jljainak a c\u00edmeit (p\u00e9ld\u00e1ul amikor URL-r\u0151l lett megnyitva, stb.)
config.group.name.paths = \u00datvonalak
config.group.description.paths = A sz\u00fcks\u00e9ges f\u00e1jlok el\u00e9rhet\u0151s\u00e9gei
#config.group.tip.paths = Ezeket a f\u00e1jlokat az Adobe honlapj\u00e1r\u00f3l t\u00f6ltheti le
#TODO: translate again:
config.group.tip.paths = Download projector and Playerglobal on <a href="%link1%">adobe webpage</a>. Flex SDK can be downloaded on <a href="%link2%">apache web</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Flash Player projector \u00fatvonal
config.description.playerLocation = Standalone flash player helye. A Futtat\u00e1shoz sz\u00fcks\u00e9ges.
config.name.playerDebugLocation = 2) Flash Player projector content debugger \u00fatvonal
config.description.playerDebugLocation = standalone debug flash player helye. A Hibakere\u00e9s\u00e9hez sz\u00fcks\u00e9ges.
config.name.playerLibLocation = 3) PlayerGlobal (.swc) \u00fatvonal
config.description.playerLibLocation = playerglobal.swc flash player library helye. Az AS3 ford\u00edt\u00e1skor van haszn\u00e1lva.
config.name.debugHalt = Futtat\u00e1s felf\u00fcggeszt\u00e9se a hibakeres\u00e9s ind\u00edt\u00e1sakor
config.description.debugHalt = Sz\u00fcnetelteti az SWF futtat\u00e1s\u00e1t a hibakeres\u00e9s ind\u00edt\u00e1sakor.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Internal) Hibakeres\u00e9si men\u00fc splitter poz\u00edci\u00f3
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Tipp: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Internal) AS1/2 Hibakeres\u00e9si men\u00fc splitter poz\u00edci\u00f3
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = V\u00e1rakoz\u00e1s miel\u0151tt v\u00e1ltja az SWF-et external player eset\u00e9n (ms)
config.description.setMovieDelay = Nem aj\u00e1nlott ezt az \u00e9rt\u00e9ket 1000ms-t\u0151l kissebre \u00e1ll\u00edtani
config.name.warning.svgImport = Figyelmeztet\u00e9s SVG import\u00e1l\u00e1skor
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Non-smoothed kit\u00f6lt\u00e9s haszn\u00e1lata amikor a shape-et egy k\u00e9ppel cser\u00e9li
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=AS1/2 a saj\u00e1t flash n\u00e9z\u0151k\u00e9ben (K\u00eds\u00e9rleti)
config.description.internalFlashViewer.execute.as12=Megpr\u00f3b\u00e1lja futtatni az ActionScript 1/2 szkripteket az SWF lej\u00e1tsz\u00e1sakor az FFDec flash n\u00e9z\u0151k\u00e9t haszn\u00e1lva
config.name.warning.hexViewNotUpToDate = Hexa N\u00e9zet nem friss figyelmeztet\u00e9s mutat\u00e1sa
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = \u00a7\u00a7dup utas\u00edt\u00e1sok mutat\u00e1sa
config.description.displayDupInstructions = Megjelen\u00edti a \u00a7\u00a7dup utas\u00edt\u00e1sokat a k\u00f3dban. N\u00e9lk\u00fcl\u00fck a k\u00f3d k\u00f6nnyen leford\u00edthat\u00f3, de n\u00e9h\u00e1ny mell\u00e9khat\u00e1sokkal rendelkez\u0151 duplik\u00e1lt k\u00f3d t\u00f6bbsz\u00f6r is lefuthat.
config.name.useRegExprLiteral = RegExp visszaford\u00edt\u00e1sa /pattern/mod form\u00e1ban.
config.description.useRegExprLiteral = /pattern/mod szintaxis haszn\u00e1lata a regul\u00e1ris kifejez\u00e9sek visszaford\u00edt\u00e1sakor. Egy\u00e9bk\u00e9nt a new RegExp("pat","mod") forma lesz haszn\u00e1lva
config.name.handleSkinPartsAutomatically = [SkinPart] metadata automatikus kezel\u00e9se
config.description.handleSkinPartsAutomatically = Visszaford\u00edtja \u00e9s k\u00f6zvetlen\u00fcl szerkesztheti automatikusan a [SkinPart] metaadatokat. Amikor ki van kapcsolva a _skinParts attributum \u00e9s a gettere l\u00e1that\u00f3 \u00e9s manu\u00e1lisan szerkeszthet\u0151.
config.name.simplifyExpressions = Kifejez\u00e9sek egyszer\u0171s\u00edt\u00e9se
config.description.simplifyExpressions = Ki\u00e9rt\u00e9keli \u00e9s egyszer\u0171s\u00edti a kifejez\u00e9seket a k\u00f3d k\u00f6nnyebb olvashat\u00f3s\u00e1ga \u00e9rdek\u00e9ben
config.name.resetLetterSpacingOnTextImport = Bet\u0171k\u00f6z alaphelyzetbe \u00e1ll\u00edt\u00e1sa sz\u00f6veg import\u00e1l\u00e1skor
config.description.resetLetterSpacingOnTextImport = Cirill bez\u0171kh\u00f6z hasznos, mert azok sz\u00e9lesebbek
