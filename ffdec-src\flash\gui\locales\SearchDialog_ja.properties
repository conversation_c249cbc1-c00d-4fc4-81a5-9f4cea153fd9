# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
checkbox.ignorecase = \u5927\u6587\u5b57\u3068\u5c0f\u6587\u5b57\u3092\u533a\u5225\u3057\u306a\u3044
checkbox.regexp = \u6b63\u898f\u8868\u73fe
button.ok = OK
button.cancel = \u30ad\u30e3\u30f3\u30bb\u30eb
label.searchtext = \u691c\u7d22\u6587\u5b57\u5217:
label.replacementtext = \u7f6e\u63db\u6587\u5b57\u5217:
#dialog.title = ActionScript search
dialog.title = \u6587\u5b57\u5217\u306e\u691c\u7d22
dialog.title.replace = \u6587\u5b57\u5217\u306e\u7f6e\u63db
error = \u30a8\u30e9\u30fc
error.invalidregexp = \u7121\u52b9\u306a\u30d1\u30bf\u30fc\u30f3
checkbox.searchText = \u30c6\u30ad\u30b9\u30c8\u5185\u3092\u691c\u7d22
checkbox.searchAS = AS \u5185\u3092\u691c\u7d22
checkbox.replaceInParameters = \u30d1\u30e9\u30e1\u30fc\u30bf\u3092\u7f6e\u63db
checkbox.searchPCode = P-code \u5185\u3092\u691c\u7d22
label.scope = \u7bc4\u56f2:
scope.currentFile = \u73fe\u5728\u306e SWF
scope.selection = \u9078\u629e\u4e2d\u306e (%selection%)
scope.allFiles = \u958b\u3044\u3066\u3044\u308b\u3059\u3079\u3066\u306e SWF
scope.selection.items = %count% \u9805\u76ee
