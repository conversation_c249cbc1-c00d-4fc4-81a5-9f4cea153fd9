# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = AS3 s\u0131n\u0131f ba\u011flant\u0131s\u0131
button.ok = Tamam
button.proceed = Devam et
button.cancel = \u0130ptal
classname = Tam nitelikli s\u0131n\u0131f ad\u0131:
error.multipleClasses = Hata: Bu karaktere zaten birden fazla s\u0131n\u0131f atanm\u0131\u015ft\u0131r, bu ara\u00e7la yeniden adland\u0131r\u0131lamaz. <PERSON><PERSON><PERSON>, yine de SymbolClass etiketini manuel olarak de\u011fi\u015ftirebilirsiniz.
error.alreadyAssignedClass = Hata: Bu s\u0131n\u0131f zaten farkl\u0131 bir karaktere atanm\u0131\u015f
error.needToModify = S\u0131n\u0131f ad\u0131n\u0131 yeni bir adla de\u011fi\u015ftirin.
class.found = Bu ad ile mevcut bir s\u0131n\u0131f bulundu.
class.notfound = Ad\u0131 olan s\u0131n\u0131f hen\u00fcz mevcut de\u011fil.
symbolClassAppropriate = En yak\u0131n uygun \u00e7er\u00e7evedeki SymbolClass etiketi de\u011fi\u015ftirilecek veya olu\u015fturulacak.
class.notfound.createAsk = S\u0131n\u0131f\u0131n olu\u015fturulmas\u0131n\u0131 istiyor musunuz?
class.notfound.create = Evet, s\u0131n\u0131f olu\u015ftur
class.notfound.create.parentType = \u00dcst s\u0131n\u0131f ad\u0131 (tam nitelikli):
class.notfound.create.abc.where = Bayt kodunun nerede olu\u015fturulaca\u011f\u0131:
class.notfound.create.abc.where.existing = Mevcut DoABC etiketi
class.notfound.create.abc.where.new = Yeni DoABC etiketi
class.notfound.onlySetClassName = Hay\u0131r, sadece s\u0131n\u0131f ad\u0131n\u0131 atay\u0131n
class.notfound.onlySetClassName.symbolClass.where = Ba\u011flant\u0131 verilerinin depolanaca\u011f\u0131 yer:
class.notfound.onlySetClassName.symbolClass.where.existing = Mevcut SymbolClass etiketi
class.notfound.onlySetClassName.symbolClass.where.new = Yeni SymbolClass etiketi
