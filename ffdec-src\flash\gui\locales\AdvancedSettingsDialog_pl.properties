# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Ustawienia Zaawansowane
advancedSettings.restartConfirmation = Musisz ponownie uruchomi\u0107 program, aby niekt\u00f3re zmiany odnios\u0142y efekt. Czy chcesz uruchomi\u0107 ponownie teraz?
advancedSettings.columns.name = Nazwa
advancedSettings.columns.value = Warto\u015b\u0107
advancedSettings.columns.description = Opis
default = domy\u015blne
config.group.name.export = Eksport
config.group.description.export = Konfiguracja eksportu
config.group.name.script = Skrypty
config.group.description.script = Powi\u0105zane z dekompilacj\u0105 kodu ActionScript
config.group.name.update = Aktualizacje
config.group.description.update = Sprawdzanie aktualizacji
config.group.name.format = Formatowanie
config.group.description.format = Formatowanie kodu ActionScript
config.group.name.limit = Limity
config.group.description.limit = Limity dekompilacji dla zaciemnionego kodu, itp.
config.group.name.ui = Interfejs
config.group.description.ui = Konfiguracja interfejsu u\u017cytkownika
config.group.name.debug = Debugowanie
config.group.description.debug = Ustawienia debugowania
config.group.name.display = Widok
config.group.description.display = Wy\u015bwietlanie obiekt\u00f3w Flash, itp.
config.group.name.decompilation = Dekompilacja
config.group.description.decompilation = Globalne funkcje zwi\u0105zane z dekompilacj\u0105
config.group.name.other = Inne
config.group.description.other = Inne ustawienia bez kategorii
config.name.openMultipleFiles = Otw\u00f3rz wiele plik\u00f3w
config.description.openMultipleFiles = Pozwala jednorazowo otworzy\u0107 wiele plik\u00f3w w jednym oknie
config.name.decompile = Poka\u017c \u017ar\u00f3d\u0142o ActionScript
config.name.dumpView = Widok zrzutu
config.description.dumpView = Wy\u015bwietl zrzut surowych danych
config.name.useHexColorFormat = Szesnastkowy format kolor\u00f3w
config.description.useHexColorFormat = Poka\u017c kolory w szesnastkowym formacie
config.name.parallelSpeedUp = R\u00f3wnoleg\u0142e przyspieszenie
config.description.parallelSpeedUp = R\u00f3wnoleg\u0142o\u015b\u0107 mo\u017ce przyspieszy\u0107 dekompilacj\u0119
config.name.parallelSpeedUpThreadCount = Liczba w\u0105tk\u00f3w (0 = auto)
config.description.parallelSpeedUpThreadCount = Liczba w\u0105tk\u00f3w dla r\u00f3wnoleg\u0142ego przyspieszenia. 0 = processor count - 1.
config.name.autoDeobfuscate = Automatyczne odkodowanie
config.description.autoDeobfuscate = Uruchom odkodowanie na ka\u017cdym pliku przed dekompilacj\u0105 kodu ActionScript
config.name.cacheOnDisk = U\u017cyj pami\u0119ci podr\u0119cznej na dysku
config.description.cacheOnDisk = Zapisuj zdekompilowane cz\u0119\u015bci do pami\u0119ci podr\u0119cznej na dysku twardym zamiast w pami\u0119ci
config.name.internalFlashViewer = U\u017cyj wewn\u0119trznego odtwarzacza Flash
config.description.internalFlashViewer = U\u017cyj JPEXS Flash Viewer zamiast standardowego odtwarzacza Flash dla wy\u015bwietlania element\u00f3w Flash
config.name.gotoMainClassOnStartup = Id\u017a do g\u0142\u00f3wnej klasy podczas uruhomienia (AS3)
config.description.gotoMainClassOnStartup = Przechodzi do klasy dokumentu pliku ActionScript 3 podczas otwarcia pliku SWF
config.name.autoRenameIdentifiers = Automatycznie zmie\u0144 nazwy identyfikator\u00f3w
config.description.autoRenameIdentifiers = Automatycznie zmie\u0144 nazwy nieprawid\u0142owych identyfikator\u00f3w podczas \u0142adowania pliku SWF
config.name.offeredAssociation = (Wewn\u0119trzne) Powi\u0105zanie z wy\u015bwietlanymi plikami SWF
config.description.offeredAssociation = Okno o powi\u0105zaniu pliku by\u0142o ju\u017c wy\u015bwietlone
config.name.decimalAddress = U\u017cyj adres\u00f3w dziesi\u0119tnych
config.description.decimalAddress = U\u017cyj adres\u00f3w dziesi\u0119tnych zamiast szesnastkowych
config.name.showAllAddresses = Poka\u017c wszystkie adresy
config.description.showAllAddresses = Wy\u015bwietl wszystkie instrukcje adres\u00f3w kodu ActionScript
config.name.useFrameCache = Zapisywanie do pami\u0119ci podr\u0119cznej dla klatek
config.description.useFrameCache = Zapisuj klatki do pami\u0119ci podr\u0119cznej przez ponownym renderowaniem
config.name.useRibbonInterface = Interfejs wst\u0105\u017cki
config.description.useRibbonInterface = Odznacz, aby u\u017cy\u0107 klasyczny interfejs bez menu wst\u0105\u017cki
config.name.openFolderAfterFlaExport = Otw\u00f3rz folder po eksporcie pliku FLA
config.description.openFolderAfterFlaExport = Wy\u015bwietl katalog wyj\u015bciowy po eksportowaniu pliku FLA
config.name.useDetailedLogging = Szczeg\u00f3\u0142owy dziennik
config.description.useDetailedLogging = Zapisuj do dziennika wiadomo\u015bci o b\u0142\u0119dach i informacje dla cel\u00f3w debugowania
config.name.resolveConstants = Rozwi\u0105\u017c sta\u0142e w p-kodzie AS1/2
config.description.resolveConstants = Wy\u0142\u0105cz, aby wy\u015bwietli\u0107 "constantxx" zamiast prawdziwych warto\u015bci w oknie P-kodu
config.name.sublimiter = Limit zast\u0105pie\u0144 kodu
config.description.sublimiter = Limit zast\u0105pie\u0144 kodu dla zaciemnionego kodu.
config.name.exportTimeout = Ca\u0142kowity limit czasu eksportu (w sekundach)
config.description.exportTimeout = Dekompiler sko\u0144czy eksportowa\u0107 po osi\u0105gni\u0119ciu tego czasu
config.name.decompilationTimeoutFile = Limit czasu dekompilacji pojedynczego pliku (w sekundach)
config.description.decompilationTimeoutFile = Dekompiler sko\u0144czy dekompilacj\u0119 kodu ActionScript w jednym pliku po osi\u0105gni\u0119ciu tego czasu
config.name.paramNamesEnable = W\u0142\u0105cz nazwy parametr\u00f3w w kodzie AS3
config.description.paramNamesEnable = U\u017cywanie nazwy parametr\u00f3w przy dekompilowaniu mo\u017ce wywo\u0142ywa\u0107 b\u0142\u0119dy, poniewa\u017c oficjalne programy takie jak Adobe Flash CS 5.5 wstawiaj\u0105 z\u0142e indeksy nazw parametr\u00f3w
config.name.displayFileName = Poka\u017c nazw\u0119 pliku SWF w tytule
config.description.displayFileName = Wy\u015bwietla nazw\u0119 pliku/url w tytule okna (Mo\u017cesz wtedy robi\u0107 zrzuty ekranu)
config.name.dumpTags = Zrzu\u0107 etykiety do konsoli
config.description.dumpTags = Zrzu\u0107 etykiety do konsoli podczas czytania pliku SWF
config.name.decompilationTimeoutSingleMethod = AS3: Limit czasu dekompilowania pojedynczej metody (w sekundach)
config.description.decompilationTimeoutSingleMethod = Dekompiler zako\u0144czy dekompilacj\u0119 kodu ActionScript po osi\u0105gni\u0119ciu tego czasu w jednej metodzie
config.name.lastRenameType = (Wewn\u0119trzne) Ostatni typ zmiany nazwy
config.description.lastRenameType = Ostatnio u\u017cyty typ zmiany nazwy identyfikator\u00f3w
config.name.lastSaveDir = (Wewn\u0119trzne) Katalog ostatniego zapisu
config.description.lastSaveDir = Katalog u\u017cyty do ostatniego zapisu
config.name.lastOpenDir = (Wewn\u0119trzne) Ostatnio otwarty katalog
config.description.lastOpenDir = Ostatnio u\u017cyty otwarty katalog
config.name.lastExportDir = (Wewn\u0119trzne) Ostatni katalog eksportu
config.description.lastExportDir = Ostatnio u\u017cyty katalog eksportu
config.name.locale = J\u0119zyk
config.description.locale = Identyfikator ustawie\u0144 regionalnych
config.name.registerNameFormat = Format zmiennej rejestru
config.description.registerNameFormat = Format nazwy zmiennej lokalnego rejestru. U\u017cyj %d dla liczby rejestru.
config.name.maxRecentFileCount = Maksymalna liczba ostatnio otwartych plik\u00f3w
config.description.maxRecentFileCount = Maksymalna liczba ostatnio otwartych plik\u00f3w
config.name.recentFiles = (Wewn\u0119trzne) Ostatnie pliki
config.description.recentFiles = Ostatnio otwarte pliki
config.name.fontPairingMap = (Wewn\u0119trzne) Pary czcionek do importu
config.description.fontPairingMap = Pary czcionek do importowania nowych znak\u00f3w
config.name.lastUpdatesCheckDate = (Wewn\u0119trzne) Data ostatniego sprawdzenia aktualizacji
config.description.lastUpdatesCheckDate = Data ostatniego sprawdzenia aktualizacji na serwerze
config.name.gui.window.width = (Wewn\u0119trzne) Ostatnia szeroko\u015b\u0107 okna
config.description.gui.window.width = Ostatnio zapisana szeroko\u015b\u0107 okna
config.name.gui.window.height = (Wewn\u0119trzne) Ostatnia wysoko\u015b\u0107 okna
config.description.gui.window.height = Ostatnio zapisana wysoko\u015b\u0107 okna
config.name.gui.window.maximized.horizontal = (Wewn\u0119trzne) Okno maksymalizowane poziomo
config.description.gui.window.maximized.horizontal = Ostatni stan okna - maksymalizowane poziomo
config.name.gui.window.maximized.vertical = (Wewn\u0119trzne) Okno maksymalizowane pionowo
config.description.gui.window.maximized.vertical = Ostatni stan okna - maksymalizowane pionowo
config.name.gui.avm2.splitPane.dividerLocationPercent=(Wewn\u0119trzne) Lokalizacja rozga\u0142\u0119\u017anika AS3
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Wewn\u0119trzne) Lokalizacja rozga\u0142\u0119\u017anika AS1/2
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Wewn\u0119trzne) Lokalizacja rozga\u0142\u0119\u017anika podgl\u0105du
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Wewn\u0119trzne) Lokalizacja rozga\u0142\u0119\u017anika 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Wewn\u0119trzne) Lokalizacja rozga\u0142\u0119\u017anika 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Zapisz jako plik EXE w trybie skalowania
config.description.saveAsExeScaleMode = Tryb skalowania dla eksportu plik\u00f3w EXE
config.name.syntaxHighlightLimit = Maksymalna liczba znak\u00f3w pod\u015bwietlania sk\u0142adni
config.description.syntaxHighlightLimit = Maksymalna liczba znak\u00f3w, na kt\u00f3rej uruchomi\u0107 pod\u015bwietlanie sk\u0142adni
config.name.guiFontPreviewSampleText = (Wewn\u0119trzne) Ostatni przyk\u0142adowy tekst podgl\u0105du czcionki
config.description.guiFontPreviewSampleText = Indeks listy ostatniego przyk\u0142adowego tekstu podgl\u0105du czcionki
config.name.gui.fontPreviewWindow.width = (Wewn\u0119trzne) Szeroko\u015b\u0107 okna podgl\u0105du ostatniej czcionki
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Wewn\u0119trzne) Wysoko\u015b\u0107 okna podgl\u0105du ostatniej czcionki
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Wewn\u0119trzne) Pozycja X okna podgl\u0105du ostatniej czcionki
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Wewn\u0119trzne) Pozycja Y okna podgl\u0105du ostatniej czcionki
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Znaki na akapit
config.description.formatting.indent.size = Liczba lub odst\u0119py (lub taby) na jeden akapit
config.name.formatting.indent.useTabs = Zak\u0142adki na akapit
config.description.formatting.indent.useTabs = U\u017cyj zak\u0142adek zamiast odst\u0119p\u00f3w dla akapitu
config.name.beginBlockOnNewLine = Nawias klamrowy w nowej linii
config.description.beginBlockOnNewLine = Rozpocznij blok z nawiasami klamrowymi w nowej linii
config.name.check.updates.delay = Op\u00f3\u017anienie sprawdzania aktualizacji
config.description.check.updates.delay = Minimalny czas pomi\u0119dzy automatycznymi kontrolami aktualizacji podczas uruchomienia aplikacji
config.name.check.updates.stable = Sprawdzaj stabilne wersje
config.description.check.updates.stable = Sprawdzanie stabilnych wersji aktualizacji
config.name.check.updates.nightly = Sprawdzaj nocne wersje
config.description.check.updates.nightly = Sprawdzanie nocnych wersji aktualizacji
config.name.check.updates.enabled = Sprawdzanie aktualizacji w\u0142\u0105czone
config.description.check.updates.enabled = Automatyczne sprawdzanie aktualizacji podczas uruchomienia aplikacji
config.name.export.formats = (Wewn\u0119trzne) Formaty do eksportu
config.description.export.formats = Ostatnio u\u017cyte formaty eksportu
config.name.textExportSingleFile = Eksportuj teksty w jednym pliku
config.description.textExportSingleFile = Eksportowanie tekst\u00f3w w jednym pliku zamiast w kilku
config.name.textExportSingleFileSeparator = Separator tekst\u00f3w w jednym pliku eksportu tekstu
config.description.textExportSingleFileSeparator = Tekst do wstawienia pomi\u0119dzy tekstami w pojedynczym pliku eksportu tekstu
config.name.textExportSingleFileRecordSeparator = Separator zapis\u00f3w w jednym pliku eksportu tekstu
config.description.textExportSingleFileRecordSeparator = Tekst do wstawienia pomi\u0119dzy zapisami w pojedynczym pliku eksportu tekstu
config.name.warning.experimental.as12edit=Ostrzegaj podczas bezpo\u015bredniego edytowania kodu AS1/2
config.description.warning.experimental.as12edit=Poka\u017c ostrze\u017cenie podczas eksperymentalnej bezpo\u015bredniej edycji kodu AS1/2
config.name.warning.experimental.as3edit=Ostrzegaj podczas bezpo\u015bredniego edytowania kodu AS3
config.description.warning.experimental.as3edit=Poka\u017c ostrze\u017cenie podczas eksperymentalnej bezpo\u015bredniej edycji kodu AS3
config.name.packJavaScripts = Pakuj skrypty JavaScript
config.description.packJavaScripts = Uruchom pakowanie JavaScript na skryptach utworzonych podczas eksportu Canvas.
config.name.textExportExportFontFace = U\u017cyj font-face w eksporcie SVG
config.description.textExportExportFontFace = Osad\u017a pliki czcionek w SVG u\u017cywaj\u0105c font-face zamiast kszta\u0142t\u00f3w
config.name.lzmaFastBytes = LZMA fast bytes (prawid\u0142owe warto\u015bci: 5-255)
config.description.lzmaFastBytes = Parametr fast bytes kodera LZMA
config.name.pluginPath = Plugin Path
config.description.pluginPath = -   
config.name.showMethodBodyId = Poka\u017c id korpusu metody
config.description.showMethodBodyId = Pokazuje id korpusu metody dla importu linii polece\u0144
