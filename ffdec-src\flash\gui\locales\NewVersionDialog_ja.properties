# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
version = \u30d0\u30fc\u30b8\u30e7\u30f3
releasedate = \u30ea\u30ea\u30fc\u30b9\u65e5:
newversionavailable = \u65b0\u3057\u3044\u30d0\u30fc\u30b8\u30e7\u30f3\u304c\u5229\u7528\u53ef\u80fd:
changeslog = \u5909\u66f4\u5c65\u6b74:
downloadnow = \u4eca\u3059\u3050\u30c0\u30a6\u30f3\u30ed\u30fc\u30c9\u3057\u307e\u3059\u304b\uff1f
button.ok = OK
button.cancel = \u30ad\u30e3\u30f3\u30bb\u30eb
dialog.title = \u65b0\u3057\u3044\u30d0\u30fc\u30b8\u30e7\u30f3\u304c\u5229\u7528\u53ef\u80fd
newversion = \u65b0\u3057\u3044\u30d0\u30fc\u30b8\u30e7\u30f3
newvermessage = %oldAppName% \u306e\u65b0\u3057\u3044\u30d0\u30fc\u30b8\u30e7\u30f3\u304c\u5229\u7528\u53ef\u80fd\u3067\u3059: %newAppName%\u3002\r\n\u30c0\u30a6\u30f3\u30ed\u30fc\u30c9\u306f %projectPage% \u304b\u3089\u884c\u3063\u3066\u304f\u3060\u3055\u3044\u3002
#change this only when the date format is wrong in the changelog
#you can use any java date format string, e.g: yyyy.MM.dd
customDateFormat = default
