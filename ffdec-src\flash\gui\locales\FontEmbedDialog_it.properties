# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%available% di %total% caratteri)
dialog.title = Incorpora tipo di carattere
label.individual = Caratteri individuali:
button.loadfont = Carica font...
filter.ttf = Font True Type (*.ttf)
error.invalidfontfile = File di font non valido
error.cannotreadfontfile = Impossibile leggere il file di font
installed = Installato:
ttffile.noselection = File TTF: <select>
ttffile.selection = File TTF: %fontname% (%filename%)
allcharacters = Tutti i caratteri (%available% caratteri)
