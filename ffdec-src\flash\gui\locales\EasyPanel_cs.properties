# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

library = Knihovna

library.folder.images = obr\u00e1zky
library.folder.graphics = grafiky
library.folder.shapeTweens = interpolace tvar\u016f
library.folder.texts = texty
library.folder.fonts = p\u00edsma
library.folder.movieClips = animovan\u00e9 klipy
library.folder.buttons = tla\u010d\u00edtka
library.folder.sounds = zvuky
library.folder.videos = videa

library.header.name = N\u00e1zev
library.header.asLinkage = AS propojen\u00ed

item.image = obr\u00e1zek
item.graphic = grafika
item.shapeTween = interpolace tvar\u016f
item.text = text
item.font = p\u00edsmo
item.movieClip = animovan\u00fd klip
item.button = tla\u010d\u00edtko
item.sound = zvuk
item.video = video
item.unknown = nezn\u00e1m\u00e9

undo = Vr\u00e1tit zp\u011bt %action%
undo.cannot = Nelze vr\u00e1tit zp\u011bt
redo = Opakovat %action%
redo.cannot = Nelze opakovat

transform = Transformace

action.addFrame = P\u0159idat sn\u00edmek
action.addKeyFrame = P\u0159idat kl\u00ed\u010dov\u00fd sn\u00edmek
action.addKeyFrameWithBlankFrameBefore = P\u0159idat kl\u00ed\u010dov\u00fd sn\u00edmek s pr\u00e1zdn\u00fdmi p\u0159ed n\u00edm
action.removeFrame = Odstranit sn\u00edmek
action.transform = Transformovat
action.move = P\u0159esunout
action.addToStage = P\u0159idat do sc\u00e9ny
action.change = Zm\u011bna %item%

action.change.compression = Komprese
action.change.swfVersion = SWF verze
action.change.encrypted = \u0160ifrovan\u00e9
action.change.swfVersion = SWF verze
action.change.gfx = GFX
action.change.frameRate = Frekvence sn\u00edmk\u016f
action.change.width = \u0160\u00ed\u0159ka
action.change.height = V\u00fd\u0161ka
action.change.colorEffect = Barevn\u00fd efekt



timeline.main = Hlavn\u00ed \u010dasov\u00e1 osa
timeline.item = \u010casov\u00e1 osa %item%
timeline.item.cancel = Storno - Klikn\u011bte pro p\u0159echod na hlavn\u00ed \u010dasovou osu

properties = Vlastnosti
properties.document = Dokument
properties.instance.single = Instance %item%
properties.instance.multiple = %count% instanc\u00ed
properties.instance.none = \u017d\u00e1dn\u00e1 instance

properties.instance.header.positionSize = Pozice a velikost
properties.instance.header.colorEffect = Barevn\u00fd efekt

property.label = %item%:

property.instance.item = Polo\u017eka
property.instance.colorEffect.alpha = Alpha
property.instance.colorEffect.red = Red
property.instance.colorEffect.green = Green
property.instance.colorEffect.blue = Blue
property.instance.positionSize.x = X
property.instance.positionSize.y = Y
property.instance.positionSize.width = \u0160
property.instance.positionSize.height = V

properties.instance.header.display = Zobrazen\u00ed

property.instance.display.visible = Viditeln\u00e9
property.instance.display.blending = Prol\u00edn\u00e1n\u00ed

property.instance.display.blending.normal = Norm\u00e1ln\u00ed
property.instance.display.blending.layer = Vrstva
property.instance.display.blending.multiply = N\u00e1sobit
property.instance.display.blending.screen = Screen
property.instance.display.blending.lighten = Zesv\u011btlit
property.instance.display.blending.darken = Ztmavit
property.instance.display.blending.difference = Rozd\u00edl
property.instance.display.blending.add = P\u0159idat
property.instance.display.blending.subtract = Ode\u010d\u00edst
property.instance.display.blending.invert = Inverze
property.instance.display.blending.alpha = Alfa
property.instance.display.blending.erase = Vymazat
property.instance.display.blending.overlay = P\u0159ekr\u00fdt
property.instance.display.blending.hardlight = Tvrd\u00e9 sv\u011btlo

property.instance.display.cacheAsBitmap = Ke\u0161ovat jako bitmapu
property.instance.display.cacheAsBitmap.transparent = Pr\u016fhledn\u00e1
property.instance.display.cacheAsBitmap.opaque = S barvou

#after 22.0.2
property.document.backgroundColor = Barva pozad\u00ed

properties.instance.header.filters = Filtry

property.instance.filters.header.property = Vlastnost
property.instance.filters.header.value = Hodnota

property.instance.filters.indeterminate = Neur\u010deno

property.instance.filters = Filtry
property.instance.filters.menu.add = P\u0159idat filtr
property.instance.filters.menu.add.removeAll = Odebrat v\u0161e
property.instance.filters.menu.remove = Odebrat filtr

filter.bevel = Reli\u00e9f
filter.blur = Rozmaz\u00e1n\u00ed
filter.colormatrix = \u00daprava barev
filter.convolution = Konvoluce
filter.dropshadow = Vr\u017een\u00fd st\u00edn
filter.glow = Z\u00e1\u0159e
filter.gradientbevel = P\u0159echodov\u00fd reli\u00e9f
filter.gradientglow = P\u0159echodov\u00e1 z\u00e1\u0159e

convolution.identity = Identita
convolution.boxBlur = Rovnom\u011brn\u00e9 rozmaz\u00e1n\u00ed
convolution.gaussianBlur = Gaussovsk\u00e9 rozmaz\u00e1n\u00ed
convolution.sharpen = Zaost\u0159en\u00ed
convolution.edgeDetectionXSobel = Detekce hran X (Sobel)
convolution.edgeDetectionYSobel = Detekce hran Y (Sobel)
convolution.edgeDetectionXPrewitt = Detekce hran X (Prewitt)
convolution.edgeDetectionYPrewitt = Detekce hran Y (Prewitt)
convolution.edgeDetectionXScharr = Detekce hran X (Scharr)
convolution.edgeDetectionYScharr = Detekce hran Y (Scharr)
convolution.laplacian = Laplace\u016fv filtr
convolution.emboss = Vytla\u010den\u00ed
convolution.outline = Obrys
convolution.motionBlurX = Pohybov\u00e9 rozmaz\u00e1n\u00ed X
convolution.motionBlurY = Pohybov\u00e9 rozmaz\u00e1n\u00ed Y
convolution.highPass = Vysokofrekven\u010dn\u00ed filtr

property.instance.filters.menu.clipboard = Schr\u00e1nka
property.instance.filters.menu.clipboard.copySelected = Kop\u00edrovat vybran\u00fd
property.instance.filters.menu.clipboard.copyAll = Kop\u00edrovat v\u0161e
property.instance.filters.menu.clipboard.paste = Vlo\u017eit

property.linkXY = Propojit X a Y vlastnosti

property.instance.filters.strength = s\u00edla
property.instance.filters.blurX = rozmaz\u00e1n\u00ed X
property.instance.filters.blurY = rozmaz\u00e1n\u00ed Y
property.instance.filters.passes = pr\u016fchody
property.instance.filters.gradient = p\u0159echod
property.instance.filters.innerShadow = vnit\u0159n\u00ed st\u00edn
property.instance.filters.compositeSource = slo\u017een\u00fd zdroj
property.instance.filters.onTop = navrchu
property.instance.filters.dropShadowColor = barva vr\u017een\u00e9ho st\u00ednu
property.instance.filters.knockout = vyseknut\u00ed
property.instance.filters.distance = vzd\u00e1lenost
property.instance.filters.angle = \u00fahel
property.instance.filters.shadowColor = barva st\u00ednu
property.instance.filters.highlightColor = barva sv\u011btla
property.instance.filters.brightness = jas
property.instance.filters.contrast = kontrast
property.instance.filters.saturation = sytost
property.instance.filters.hue = odst\u00edn
property.instance.filters.divisor = d\u011blitel
property.instance.filters.bias = posun
property.instance.filters.clamp = o\u0159\u00edznout
property.instance.filters.defaultColor = v\u00fdchoz\u00ed barva
property.instance.filters.preserveAlpha = zachovat alfu
property.instance.filters.matrix = matice
property.instance.filters.glowColor = barva z\u00e1\u0159e
property.instance.filters.innerGlow = vnit\u0159n\u00ed z\u00e1\u0159e

property.instance.display.ratio = Pom\u011br interpolace