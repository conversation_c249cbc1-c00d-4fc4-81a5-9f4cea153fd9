# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
proxy.start = \u0417\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u043f\u0440\u043e\u043a\u0441\u0438
proxy.stop = \u041e\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u044c \u043f\u0440\u043e\u043a\u0441\u0438
port = \u041f\u043e\u0440\u0442:
open = \u041e\u0442\u043a\u0440\u044b\u0442\u044c
clear = \u041e\u0447\u0438\u0441\u0442\u0438\u0442\u044c
rename = \u041f\u0435\u0440\u0435\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u0442\u044c
remove = \u0423\u0434\u0430\u043b\u0438\u0442\u044c
sniff = \u041f\u0435\u0440\u0435\u0445\u0432\u0430\u0442\u0438\u0442\u044c:
dialog.title = \u041f\u0440\u043e\u043a\u0441\u0438
error = \u041e\u0448\u0438\u0431\u043a\u0430
error.port = \u041d\u0435\u0432\u0435\u0440\u043d\u044b\u0439 \u0444\u043e\u0440\u043c\u0430\u0442 \u043d\u043e\u043c\u0435\u0440\u0430 \u043f\u043e\u0440\u0442\u0430.
copy.url = \u041a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u0442\u044c URL
save.as = \u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u043a\u0430\u043a...
replace = \u0417\u0430\u043c\u0435\u043d\u0438\u0442\u044c...
error.save.as = \u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0441\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u0444\u0430\u0439\u043b
error.replace = \u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0437\u0430\u043c\u0435\u043d\u0438\u0442\u044c \u0434\u0430\u043d\u043d\u044b\u0435
error.start.server = \u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u0437\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u0441\u0435\u0440\u0432\u0435\u0440 \u043d\u0430 \u043f\u043e\u0440\u0442\u0443 %port%. \u0423\u0431\u0435\u0434\u0438\u0442\u0435\u0441\u044c, \u0447\u0442\u043e \u043f\u043e\u0440\u0442 \u043d\u0435 \u0437\u0430\u043d\u044f\u0442 \u0434\u0440\u0443\u0433\u0438\u043c \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0435\u043c.
column.accessed = \u0414\u043e\u0441\u0442\u0443\u043f
column.size = \u0420\u0430\u0437\u043c\u0435\u0440
column.url = URL
