# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Geavanceerde Instellingen
advancedSettings.restartConfirmation = U moet het programma opnieuw te starten om de wijzigingen door te voeren. Wilt U het nu opnieuw opstarten?
advancedSettings.columns.name = Naam
advancedSettings.columns.value = Waarde
advancedSettings.columns.description = Beschrijving
default = standaardinstellingen
config.group.name.export = Export
config.group.description.export = Configuratie van exporten
config.group.name.script = Scripts
config.group.description.script = ActionScript-decompilatie gerelateerd
config.group.name.update = Updates
config.group.description.update = Controleren op updates
config.group.name.format = Opmaak
config.group.description.format = Opmaak van ActionScript-code
config.group.name.limit = Limieten\
 
config.group.description.limit = Decompilatielimieten voor versluierde code, enz.
config.group.name.ui = Interface
config.group.description.ui = Configuratie van de gebruikersinterface
config.group.name.debug = Foutopsporing
config.group.description.debug = Instellingen foutopsporing
config.group.name.display = Weergeven
config.group.description.display = Flash-objecten weergave, enz.
config.group.name.decompilation = Decompilatie
config.group.description.decompilation = Globale decompilatie gerelateerde functies
config.group.name.other = Ander
config.group.description.other = Andere niet-gecategoriseerde configuraties
config.name.openMultipleFiles = Meerdere bestanden openen
config.description.openMultipleFiles = Maakt het openen van meerdere bestanden tegelijk in \u00e9\u00e9n venster mogelijk
config.name.decompile = ActionScript-bron weergeven
config.description.decompile = U kunt AS-decompilatie uitschakelen, dan wordt alleen P-code weergegeven
config.name.dumpView = Dump-weergave
config.description.dumpView = Bekijk de dump van onbewerkte gegevens
config.name.useHexColorFormat = Hex-kleurformaat
config.description.useHexColorFormat = Toon de kleuren in hex-formaat
config.name.parallelSpeedUp = Parallelle versnelling
config.description.parallelSpeedUp = Parallellisme kan decompilatie versnellen
config.name.parallelSpeedUpThreadCount = Aantal threads (0 = auto)
config.description.parallelSpeedUpThreadCount = Aantal threads voor parallelle versnelling. 0 = processor count - 1.
config.name.autoDeobfuscate = Automatische deobfusie
config.description.autoDeobfuscate = Voer deobfuscatie uit op elk bestand v\u00f3\u00f3r de decompilatie van ActionScript
config.name.cacheOnDisk = Gebruik caching op schijf
config.description.cacheOnDisk = Cache reeds gedecompileerde onderdelen op de harde schijf in plaats van in het geheugen
config.name.internalFlashViewer = Gebruik eigen Flash-viewer
config.description.internalFlashViewer = Gebruik JPEXS Flash Viewer in plaats van de standaard Flash Player voor de weergave van Flash-onderdelen
config.name.gotoMainClassOnStartup = Ga naar de hoofdklasse bij het opstarten (AS3)
config.description.gotoMainClassOnStartup = Navigeer naar de documentklasse van het AS3-bestand bij het openen van een SWF
config.name.autoRenameIdentifiers = Automatische hernoemings identifiers
config.description.autoRenameIdentifiers = Hernoem automatisch ongeldige identifierss bij het laden van SWF
config.name.offeredAssociation = (Interne) Associatie weergeven met SWF-bestanden
config.description.offeredAssociation = Het dialoogvenster over bestandskoppeling werd al weergegeven
config.name.decimalAddress = Gebruik decimale adressen
config.description.decimalAddress = Gebruik decimale adressen in plaats van hexadecimale adressen
config.name.showAllAddresses = Toon alle adressen
config.description.showAllAddresses = Geef alle ActionScript-instructieadressen weer
config.name.useFrameCache = Gebruik framecache
config.description.useFrameCache = Frames in de cache opslaan voordat ze opnieuw worden gerenderd
config.name.useRibbonInterface = Lint-interface
config.description.useRibbonInterface = Haal het vinkje weg om de klassieke interface zonder lintmenu te gebruiken
config.name.openFolderAfterFlaExport = Map openen na FLA-export
config.description.openFolderAfterFlaExport = Geef de uitvoermap weer na het exporteren van het FLA-bestand
config.name.useDetailedLogging = FFDec gedetailleerde logboekregistratie
config.description.useDetailedLogging = Registreer gedetailleerde foutmeldingen en informatie voor het debuggen van FFDec
config.name._debugMode=FFDec in debug-modus
config.description._debugMode=Modus voor het debuggen van FFDec. Schakelt het foutopsporingsmenu in. Dit heeft niets te maken met de debugger-functionaliteit
config.name.resolveConstants = Los constanten op in AS1/2 p-code
config.description.resolveConstants = Schakel dit uit om 'constantxx' weer te geven in plaats van echte waarden in het P-codevenster
config.name.sublimiter = Limiet van codesubs
config.description.sublimiter = Limiet van codesubs voor versluierde code.
config.name.exportTimeout = Totale exporttime-out (seconden)
config.description.exportTimeout = Decompiler stopt met exporteren nadat dit tijdstip is bereikt
config.name.decompilationTimeoutFile = Time-out voor decompilatie van \u00e9\u00e9n bestand (seconden)
config.description.decompilationTimeoutFile = Decompiler stopt de decompilatie van ActionScript nadat deze tijd in \u00e9\u00e9n bestand is bereikt
config.name.paramNamesEnable = Schakel parameternamen in AS3 in
config.description.paramNamesEnable = Het gebruik van parameternamen bij het decompileren kan problemen veroorzaken omdat offici\u00eble programma's zoals Flash CS 5.5 verkeerde parameternamenindices invoegen
config.name.displayFileName = Toon SWF-naam in titel
config.description.displayFileName = Geef de SWF-bestands-/URL-naam weer in de venstertitel (u kunt dan schermafbeeldingen maken)
config.name._debugCopy=FFDec-foutopsporing opnieuw compileren
config.description._debugCopy=het produceert dezelfde binaire code. Alleen gebruiken voor DEBUGGING van FFDec!
config.name.dumpTags = Dump tags naar console
config.description.dumpTags = Dump tags naar console bij het lezen van het SWF-bestand
config.name.decompilationTimeoutSingleMethod = AS3: Time-out voor decompilatie met \u00e9\u00e9n methode (seconden)
config.description.decompilationTimeoutSingleMethod = Decompiler stopt de decompilatie van ActionScript nadat deze tijd in \u00e9\u00e9n methode is bereikt
config.name.lastRenameType = (Intern) Laatste hernoemingstype
config.description.lastRenameType = Laatst gebruikte hernoemings identifier-type
config.name.lastSaveDir = (Intern) Laatste opslagmap
config.description.lastSaveDir = Laatst gebruikte opslagmap
config.name.lastOpenDir = (Intern) Laatst geopende map
config.description.lastOpenDir = Laatst gebruikte open map
config.name.lastExportDir = (Intern) Laatste exportmap
config.description.lastExportDir = Laatst gebruikte exportmap
config.name.locale = Taal
config.description.locale = Lokale identifier
config.name.registerNameFormat = Variabel formaat registreren
config.description.registerNameFormat = Formaat van namen van lokale registervariabelen. Gebruik %d als registernummer.
config.name.maxRecentFileCount = Maximaal recent aantal
config.description.maxRecentFileCount = Maximaal aantal recente bestanden
config.name.recentFiles = (Intern) Recente bestanden
config.description.recentFiles = Recent geopende bestanden
config.name.fontPairingMap = (Intern) Lettertypeparen voor import
config.description.fontPairingMap = Lettertypeparen voor het importeren van nieuwe tekens
config.name.lastUpdatesCheckDate = (Intern) Laatste update-controledatum
config.description.lastUpdatesCheckDate = Datum van laatste controle op updates op de server
config.name.gui.window.width = (Intern) Laatste vensterbreedte
config.description.gui.window.width = Laatst opgeslagen vensterbreedte
config.name.gui.window.height = (Intern) Laatste vensterhoogte
config.description.gui.window.height = Laatst opgeslagen vensterhoogte
config.name.gui.window.maximized.horizontal = (Intern) Venster horizontaal gemaximaliseerd
config.description.gui.window.maximized.horizontal = Laatste vensterstatus - horizontaal gemaximaliseerd
config.name.gui.window.maximized.vertical = (Intern) Venster verticaal gemaximaliseerd
config.description.gui.window.maximized.vertical = Laatste vensterstatus - verticaal gemaximaliseerd
config.name.gui.avm2.splitPane.dividerLocationPercent=(Interne) AS3 Splitterlocatie
config.name.gui.actionSplitPane.dividerLocationPercent = (Interne) AS1/2-splitterlocatie
config.name.gui.previewSplitPane.dividerLocationPercent = (Intern) Bekijk een voorbeeld van de locatie van de splitter
config.name.gui.splitPane1.dividerLocationPercent=(Interne) Splitterlocatie 1
config.name.gui.splitPane2.dividerLocationPercent=(Interne) Splitterlocatie 2
config.name.saveAsExeScaleMode = Opslaan als EXE-schaalmodus
config.description.saveAsExeScaleMode = Schaalmodus voor EXE-export
config.name.syntaxHighlightLimit = Syntaxis markeert max. aantal tekens
config.description.syntaxHighlightLimit = Maximaal aantal tekens waarop syntaxismarkering moet worden uitgevoerd
config.name.guiFontPreviewSampleText = (Intern) Voorbeeldtekst van het laatste lettertype
config.description.guiFontPreviewSampleText = Laatste lettertype voorbeeld voorbeeldtekst-lijstindex
config.name.gui.fontPreviewWindow.width = (Intern) Vensterbreedte laatste lettertypevoorbeeld
config.name.gui.fontPreviewWindow.height = (Intern) Hoogte van het laatste lettertypevoorbeeldvenster
config.name.gui.fontPreviewWindow.posX = (Intern) Laatste lettertypevoorbeeldvenster X
config.name.gui.fontPreviewWindow.posY = (Intern) Laatste lettertypevoorbeeldvenster Y
config.name.formatting.indent.size = Tekens per inspringing
config.description.formatting.indent.size = Aantal of spaties (of tabs) voor \u00e9\u00e9n inspringing
config.name.formatting.indent.useTabs = Tab's voor inspringen
config.description.formatting.indent.useTabs = Gebruik tabs in plaats van spaties om in te springen
config.name.beginBlockOnNewLine = Accolade op nieuwe regel
config.description.beginBlockOnNewLine = Begin het blok met accolade op een nieuwe regel
config.name.check.updates.delay = Updates controleren vertraging
config.description.check.updates.delay = Minimale tijd tussen automatische controles op updates bij het starten van de applicatie
config.name.check.updates.stable = Controleer op stabiele versies
config.description.check.updates.stable = Controleren op stabiele versie-updates
config.name.check.updates.nightly = Controleer op nachtelijke versies
config.description.check.updates.nightly = Controleren op nachtelijke versie-updates
config.name.check.updates.enabled = Updatecontrole ingeschakeld
config.description.check.updates.enabled = Automatische controle op updates bij het starten van de applicatie
config.name.export.formats = (Interne) Exportformaten
config.description.export.formats = Laatst gebruikte exportformaten
config.name.textExportSingleFile = Exporteer teksten naar \u00e9\u00e9n bestand
config.description.textExportSingleFile = Teksten exporteren naar \u00e9\u00e9n bestand in plaats van meerdere
config.name.textExportSingleFileSeparator = Scheidingsteken van teksten in \u00e9\u00e9n tekstexportbestand
config.description.textExportSingleFileSeparator = Tekst die tussen teksten moet worden ingevoegd bij de tekstexport van \u00e9\u00e9n bestand
config.name.textExportSingleFileRecordSeparator = Scheidingsteken van records in \u00e9\u00e9n tekstexportbestand
config.description.textExportSingleFileRecordSeparator = Tekst die tussen tekstrecords moet worden ingevoegd bij tekstexport met \u00e9\u00e9n bestand
config.name.warning.experimental.as12edit=Waarschuw bij directe bewerking AS1/2
config.description.warning.experimental.as12edit=Waarschuwing weergeven voor experimentele directe bewerking van AS1/2
config.name.warning.experimental.as3edit=Waarschuw voor directe bewerking via AS3
config.description.warning.experimental.as3edit=Waarschuwing weergeven voor experimentele directe bewerking van AS3
config.name.packJavaScripts = JavaScript inpakken
config.description.packJavaScripts = Voer JavaScript-packer uit op scripts die zijn gemaakt op Canvas Export
config.name.textExportExportFontFace = Gebruik lettertype in SVG-export
config.description.textExportExportFontFace = Sluit lettertypebestanden in SVG in met behulp van lettertype in plaats van vormen
config.name.lzmaFastBytes = LZMA snelle bytes (geldige waarden: 5-255)
config.description.lzmaFastBytes = Parameter voor snelle bytes van de LZMA-encoder
config.name.pluginPath = Plug-in pad
config.description.pluginPath = -
config.name.showMethodBodyId = Toon methode body-ID
config.description.showMethodBodyId = Toont de id van de methodebody voor import vanaf de opdrachtregel
config.name.export.zoom = (Intern) Zoom exporteren
config.description.export.zoom = Laatst gebruikte exportzoom
config.name.debuggerPort = Debugger-poort
config.description.debuggerPort = Poort gebruikt voor socket-foutopsporing
config.name.displayDebuggerInfo = (Intern) Foutopsporingsinformatie weergeven
config.description.displayDebuggerInfo = Geef informatie over debugger weer voordat u deze wijzigt
config.name.randomDebuggerPackage = Gebruik een willekeurige pakketnaam voor Debugger
config.description.randomDebuggerPackage = Hierdoor wordt de naam van het Debugger-pakket gewijzigd in een willekeurige tekenreeks, waardoor de aanwezigheid van debugger moeilijker te detecteren is door ActionScript
config.name.lastDebuggerReplaceFunction = (Intern) Laatst geselecteerde tracevervanging
config.description.lastDebuggerReplaceFunction = Functienaam die het laatst is geselecteerd bij het vervangen van de traceerfunctie door de
config.name.getLocalNamesFromDebugInfo = AS3: Haal lokale registernamen op uit foutopsporingsinformatie
config.description.getLocalNamesFromDebugInfo = Als er foutopsporingsinformatie aanwezig is, worden de lokale registers hernoemd van _loc_x_ naar echte namen. Dit kan worden uitgeschakeld omdat sommige obfuscators ongeldig register na gebruiken
config.name.tagTreeShowEmptyFolders = Lege mappen weergeven
config.description.tagTreeShowEmptyFolders = Toon lege mappen in de tagboom.
config.name.autoLoadEmbeddedSwfs = Ingesloten SWF's automatisch laden
config.description.autoLoadEmbeddedSwfs = Laad automatisch de ingesloten SWF's vanuit DefineBinaryData-tags.
config.name.overrideTextExportFileName = Tekstexportbestandsnaam overschrijven
config.description.overrideTextExportFileName = U kunt de bestandsnaam van de ge\u00ebxporteerde tekst aanpassen. Gebruik de tijdelijke aanduiding {filename} om de bestandsnaam van het huidige SWF-bestand te gebruiken.
config.name.showOldTextDuringTextEditing = Toon oude tekst tijdens tekstbewerking
config.description.showOldTextDuringTextEditing = Toont de originele tekst van de teksttag met grijze kleur in het voorbeeldgebied.
config.group.name.import = Import
config.group.description.import = Configuratie van imports
config.name.textImportResizeTextBoundsMode = Modus voor het wijzigen van de grootte van tekstgrenzen
config.description.textImportResizeTextBoundsMode = Modus voor het wijzigen van de grootte van tekstgrenzen na het bewerken van tekst.
config.name.showCloseConfirmation = Bevestiging SWF-sluiting opnieuw weergeven
config.description.showCloseConfirmation = Opnieuw SWF-sluitbevestiging weergeven voor gewijzigde bestanden.
config.name.showCodeSavedMessage = Toon opnieuw het code opgeslagen bericht
config.description.showCodeSavedMessage = Toon opnieuw het code opgeslagen bericht
config.name.showTraitSavedMessage = Toon opnieuw eigenschap opgeslagen bericht
config.description.showTraitSavedMessage = Toon opnieuw eigenschap opgeslagen bericht
config.name.updateProxyAddress = Http Proxy-adres voor het controleren van updates
config.description.updateProxyAddress = Http Proxy-adres voor het controleren van updates. Formaat: voorbeeld.com:8080
config.name.editorMode = Editor-modus
config.description.editorMode = Maak tekstgebieden automatisch bewerkbaar wanneer u een Tekst of Script n selecteert
config.name.autoSaveTagModifications = Tagwijzigingen automatisch opslaan
config.description.autoSaveTagModifications = Sla de wijzigingen op wanneer u een nieuwe tag in de boom selecteert
config.name.saveSessionOnExit = Sessie opslaan bij afsluiten
config.description.saveSessionOnExit = Sla de huidige sessie op en open deze opnieuw nadat FFDec opnieuw is opgestart (werkt alleen met echte bestanden)
config.name._showDebugMenu=Toon het FFDec-foutopsporingsmenu
config.description._showDebugMenu=Toont het foutopsporingsmenu in het lint voor het opsporen van fouten in de decompiler.
config.name.allowOnlyOneInstance = Slechts \u00e9\u00e9n FFDec-instantie toestaan \u200b\u200b(alleen Windows OS)
config.description.allowOnlyOneInstance = FFDec kan dan slechts \u00e9\u00e9n keer worden uitgevoerd; alle geopende bestanden worden aan \u00e9\u00e9n venster toegevoegd. Het werkt alleen met het Windows-besturingssysteem.
config.name.scriptExportSingleFile = Exporteer scripts naar \u00e9\u00e9n bestand
config.description.scriptExportSingleFile = Scripts exporteren naar \u00e9\u00e9n bestand in plaats van meerdere
config.name.setFFDecVersionInExportedFont = Stel het FFDec-versienummer in het ge\u00ebxporteerde lettertype in
config.description.setFFDecVersionInExportedFont = Wanneer deze instelling is uitgeschakeld, voegt FFDec het huidige FFDec-versienummer niet toe aan het ge\u00ebxporteerde lettertype.
config.name.gui.skin = Iterlijk van gebruikersinterface
config.description.gui.skin = Kijk en voel uiterlijk
config.name.lastSessionFiles = Bestanden van laatste sessie
config.description.lastSessionFiles = Bevat de geopende bestanden van de laatste sessie
config.name.lastSessionSelection = Selectie laatste sessie
config.description.lastSessionSelection = Bevat de selectie uit de laatste sessie
config.name.loopMedia = Herhaal (loop) geluiden en sprites
config.description.loopMedia = Herstart automatisch het afspelen van de geluiden en sprites
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Interne) Locatie van de tijdlijnsplitter
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Afbeeldingen cachen
config.description.cacheImages = Cache de gedecodeerde afbeeldingsobjecten
config.name.swfSpecificConfigs = SWF-specifieke configuraties
config.description.swfSpecificConfigs = Bevat de SWF-specifieke configuraties
config.name.exeExportMode = EXE-exportmodus
config.description.exeExportMode = EXE-exportmodus
config.name.ignoreCLikePackages = Negeer FlashCC / Alchemy of soortgelijke pakketten
config.description.ignoreCLikePackages = FlashCC/Alchemy-pakketten kunnen doorgaans niet correct worden gedecompileerd. U kunt ze uitschakelen om de decompilatie van andere pakketten te versnellen.
config.name.overwriteExistingFiles = Overschrijf de bestaande bestanden
config.description.overwriteExistingFiles = Overschrijf de bestaande bestanden tijdens het exporteren. Momenteel alleen voor AS2/3-script
config.name.smartNumberFormatting = Gebruik slimme getalnotatie
config.description.smartNumberFormatting = Formaat speciale getallen (bijvoorbeeld kleuren en tijden)
config.name.enableScriptInitializerDisplay = (VERWIJDERD) Initialisatieprogramma's voor weergavescripts
config.description.enableScriptInitializerDisplay = Schakel de weergave en bewerking van scriptinitialisaties in. Deze instelling kan \u00e9\u00e9n nieuwe regel aan elk klassenbestand toevoegen om te markeren.
config.name.autoOpenLoadedSWFs = Geladen SWF's openen tijdens uitvoering (externe viewer = alleen WIN)
config.description.autoOpenLoadedSWFs = Opent automatisch alle SWF's die zijn geladen door AS3 Class Loader door SWF uit te voeren wanneer ze worden afgespeeld in de externe FFDec-speler. Deze functie is alleen beschikbaar voor Windows.
config.name.lastSessionFileTitles = Bestandstitels van de laatste sessie
config.description.lastSessionFileTitles = Bevat de geopende bestandstitels van de laatste sessie (bijvoorbeeld wanneer geladen vanaf URL enz.)
config.group.name.paths = Paden
config.group.description.paths = Locatie van benodigde bestanden
config.group.tip.paths = Download projector en Playerglobal op de <a href="%link1%">adobe-webpagina</a>. Flex SDK kan worden gedownload op <a href="%link2%">apache web</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Flash Player-projectorpad
config.description.playerLocation = Locatie van het uitvoerbare bestand van de standalone Flash Player. Gebruikt voor Run-actie.
config.name.playerDebugLocation = 2) Foutopsporingspad voor Flash Player-projectorinhoud
config.description.playerDebugLocation = Locatie van het zelfstandige uitvoerbare bestand van Flash Player voor foutopsporing. Gebruikt voor foutopsporingsactie.
config.name.playerLibLocation = 3) PlayerGlobal-pad (.swc).
config.description.playerLibLocation = Locatie van de flash player-bibliotheek playerglobal.swc. Het wordt meestal gebruikt voor AS3-compilatie.
config.name.debugHalt = Stop de uitvoering bij het starten van de foutopsporing
config.description.debugHalt = Pauzeer SWF bij het starten van foutopsporing.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Intern) Locatie van de foutopsporingsmenusplitter
tip = Tip: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Intern) AS1/2 Locatie van de debug-menusplitter
config.name.setMovieDelay = Vertraging voordat de SWF in een externe speler wordt gewijzigd in ms
config.description.setMovieDelay = Het wordt niet aanbevolen om deze waarde onder de 1000 ms te wijzigen
config.name.warning.svgImport = Waarschuw bij SVG-import
config.name.shapeImport.useNonSmoothedFill = Gebruik een niet-vloeiende vulling wanneer een vorm wordt vervangen door een afbeelding
config.name.internalFlashViewer.execute.as12=AS1/2 in eigen flashviewer (Experimenteel)
config.description.internalFlashViewer.execute.as12=Probeer ActionScript 1/2 uit te voeren tijdens het afspelen van SWF met FFDec Flash Viewer
config.name.warning.hexViewNotUpToDate = Toon Hex View niet up-to-date waarschuwing
config.name.displayDupInstructions = Toon \u00a7\u00a7dup-instructies
config.description.displayDupInstructions = Geef \u00a7\u00a7dup-instructies in de code weer. Zonder deze kan de code gemakkelijk worden gecompileerd, maar sommige gedupeerde code met bijwerkingen kan twee keer worden uitgevoerd.
config.name.useRegExprLiteral = Decompileer RegExp als /pattern/mod letterlijk.
config.description.useRegExprLiteral = Gebruik de syntaxis /pattern/mod bij het decompileren van reguliere expressies. new RegExp("pat", "mod") wordt anders gebruikt
config.name.handleSkinPartsAutomatically = Verwerk [SkinPart]-metadata automatisch
config.description.handleSkinPartsAutomatically = Decompileert en bewerkt [SkinPart] metadata automatisch. Wanneer uitgeschakeld, zijn het kenmerk _skinParts en de gettermethode zichtbaar en handmatig bewerkbaar.
config.name.simplifyExpressions = Vereenvoudig expressies
config.description.simplifyExpressions = Evalueer en vereenvoudig expressies om code leesbaarder te maken
config.name.resetLetterSpacingOnTextImport = Letterafstand opnieuw instellen bij tekstimport
config.description.resetLetterSpacingOnTextImport = Handig voor cyrillische lettertypen, omdat deze breder zijn
config.name.flexSdkLocation = 4) Flex SDK-mappad
config.description.flexSdkLocation = Locatie van Adobe Flex SDK. Het wordt meestal gebruikt voor AS3-compilatie.
config.name.useFlexAs3Compiler=Gebruik de Flex SDK AS3-compiler
config.description.useFlexAs3Compiler=Gebruik de AS3-compiler van Flex SDK tijdens directe bewerking van ActionScript (Flex SDK-map moet worden ingesteld)
config.name.showSetAdvanceValuesMessage = Toon opnieuw informatie over het instellen van geavanceerde waarden
config.description.showSetAdvanceValuesMessage = Toon opnieuw informatie over het instellen van geavanceerde waarden
config.name.gui.fontSizeMultiplier = Vermenigvuldiger van de lettergrootte
config.description.gui.fontSizeMultiplier = Vermenigvuldiger van de lettergrootte
config.name.graphVizDotLocation = 5) Uitvoerbaar pad van GraphViz Dot
config.description.graphVizDotLocation = Pad naar dot.exe (of vergelijkbaar voor Linux) van de GraphViz-applicatie voor het weergeven van grafieken.
#Do not translate the Font Styles which is in the parenthesis:(Plain,Bold,Italic,BoldItalic)
config.name.gui.sourceFont = Bronlettertype
config.description.gui.sourceFont = Lettertypenaam-Lettertypestijl (Normaal, Vet, Cursief, VetCursief) - Lettergrootte
#after 11.1.0
config.name.as12DeobfuscatorExecutionLimit=Uitvoeringslimiet voor AS1/2-deobfuscator
config.description.as12DeobfuscatorExecutionLimit=Maximaal aantal instructies dat wordt verwerkt tijdens de deobfuscatie van AS1/2-uitvoering
#option that ignore in 8.0.1 and other versions
config.name.showOriginalBytesInPcodeHex = (Intern)Toon originele bytes
config.description.showOriginalBytesInPcodeHex = toon originele bytes in Pcode Hex
config.name.showFileOffsetInPcodeHex = (Intern) Toon bestandsoffset
config.description.showFileOffsetInPcodeHex = toon bestandsoffset in Pcode Hex
config.name._enableFlexExport=(Intern) schakelFlexExport in
config.description.enableFlexExport = schakel Flex-export in
config.name._ignoreAdditionalFlexClasses=(Intern) negeer aanvullende Flex-klassen
config.description.ignoreAdditionalFlexClasses = negeer aanvullende Flex-klassen
config.name.hwAcceleratedGraphics = 
config.description.hwAcceleratedGraphics = 
config.name.gui.avm2.splitPane.docs.dividerLocationPercent=(Interne) splitPanedocsdividerLocationPercent
config.description.gui.avm2.splitPane.docs.dividerLocationPercent=splitPane docs Divider Locatie Percentage
config.name.gui.dump.splitPane.dividerLocationPercent = (Intern) dumpsplitPanedividerLocationPercent
config.description.gui.dump.splitPane.dividerLocationPercent = dump splitPane-verdeler Locatie Percent
#after 11.3.0
config.name.useAdobeFlashPlayerForPreviews = (Verouderd) Gebruik Adobe Flash Player voor een voorbeeld van objecten
config.description.useAdobeFlashPlayerForPreviews = Gebruik Adobe Flash player voor een voorbeeld van objecten. WAARSCHUWING: FlashPlayer is stopgezet op 12-01-2021
#after 12.0.1
config.name.showLineNumbersInPCodeGraphvizGraph = Toon lijnnummers in Graphviz-grafieken
config.description.showLineNumbersInPCodeGraphvizGraph = Toon lijnnummers in de P-code grafiek.
config.name.padAs3PCodeInstructionName=Vul AS3 P-code-instructienamen in
config.description.padAs3PCodeInstructionName=Vul AS3 P-code-instructienamen in met spaties
#after 13.0.2
config.name.indentAs3PCode=AS3 P-code inspringen
config.description.indentAs3PCode=AS3 P-codeblokken zoals eigenschap/body/code inspringen
config.name.labelOnSeparateLineAs3PCode=Label in AS3 P-code op aparte regel
config.description.labelOnSeparateLineAs3PCode=Label maken in AS3 P-codestandaard op aparte lijn
config.name.useOldStyleGetSetLocalsAs3PCode=Gebruik getlocal_x in oude stijl in plaats van getlocalx in AS3 P-code
config.description.useOldStyleGetSetLocalsAs3PCode=Gebruik oude stijl getlocal_x, setlocal_x van FFDec 12.x of ouder
config.name.useOldStyleLookupSwitchAs3PCode=Gebruik de ouderwetse lookupswitch zonder haakjes in AS3 P-code
config.description.useOldStyleLookupSwitchAs3PCode=Gebruik de ouderwetse lookupswitch van FFDec 12.x of ouder
#after 13.0.3
config.name.checkForModifications = Controleer op bestandswijzigingen buiten FFDec
config.description.checkForModifications = Controleer op wijzigingen van bestanden door andere applicaties en vraag om opnieuw te laden
config.name.warning.initializers = Waarschuw bij AS3-slot/const-bewerking over initializers
config.description.warning.initializers = Waarschuwing weergeven bij AS3-slot/const-bewerking over initializers
config.name.parametersPanelInSearchResults = Parameterpaneel weergeven in zoekresultaten
config.description.parametersPanelInSearchResults = Toon paneel met parameters zoals zoektekst / hoofdlettergebruik negeren / regexp in het zoekresultatenvenster
config.name.displayAs3PCodeDocsPanel=Toon documentenpaneel in AS3 P-code
config.description.displayAs3PCodeDocsPanel=Toon paneel met documentatie van instructies en codestructuur in AS3 P-codebewerking en weergave
config.name.displayAs3TraitsListAndConstantsPanel=Toon AS3-eigenschappenlijst en constantenpaneel
config.description.displayAs3TraitsListAndConstantsPanel=Toon paneel met lijst met eigenschappen en constanten onder de tagboom voor AS3
#after 14.1.0
config.name.useAsTypeIcons = Gebruik scriptpictogrammen op basis van het itemtype
config.description.useAsTypeIcons = Gebruik verschillende pictogrammen voor verschillende scripttypen (klasse/interface/frame/...)
config.name.limitAs3PCodeOffsetMatching=Limiet voor aanpassing van de AS3 P-code-offset
config.description.limitAs3PCodeOffsetMatching=Beperking van instructies in AS3 P-code die gecompenseerd zijn voor AS3-script
#after 14.2.1
config.name.showSlowRenderingWarning = Logwaarschuwing wanneer het renderen te langzaam is
config.description.showSlowRenderingWarning = Logt een waarschuwing wanneer de interne Flash-viewer te traag is om inhoud weer te geven
#after 14.3.1
config.name.autoCloseQuotes = Enkele aanhalingstekens automatisch sluiten bij scriptbewerking
config.description.autoCloseQuotes = Voegt automatisch het tweede enkele aanhalingsteken in wanneer u het eerste typt
config.name.autoCloseDoubleQuotes = Sluit dubbele aanhalingstekens automatisch bij scriptbewerking
config.description.autoCloseDoubleQuotes = Voegt automatisch een tweede dubbel aanhalingsteken " in bij het typen van de eerste
config.name.autoCloseBrackets = Sluit haakjes automatisch bij scriptbewerking
config.description.autoCloseBrackets = Voegt automatisch sluithaakje ] in bij typopening [
config.name.autoCloseParenthesis = Sluit haakje automatisch bij scriptbewerking
config.description.autoCloseParenthesis = Voegt automatisch een haakje sluiten in bij het typen van de opening (
config.name.showDialogOnError = Toon een foutdialoog bij elke fout
config.description.showDialogOnError = Geeft automatisch een foutdialoog weer bij elke fout
#after 14.4.0
config.name.limitSameChars = Limiet van dezelfde tekens voor \\{xx}C (herhaal) escape
config.description.limitSameChars = Maximaal aantal dezelfde tekens op rij in P-codereeksen of onduidelijke namen voordat deze worden vervangen door \\{xx}C herhalings escape
#after 14.5.2
config.name.showImportScriptsInfo = Toon informatie voordat u scripts importeert
config.description.showImportScriptsInfo = Geeft informatie weer over hoe het importeren van scripts werkt nadat u op Scripts importeren in het menu hebt geklikt.
config.name.showImportTextInfo = Toon informatie voordat u tekst importeert
config.description.showImportTextInfo = Geeft informatie weer over hoe het importeren van tekst werkt nadat u op Tekst importeren in het menu hebt geklikt.
config.name.showImportSymbolClassInfo = Toon informatie voordat u een  symbool-klasse importeert
config.description.showImportSymbolClassInfo = Geeft informatie weer over hoe het importeren van symboolklassen werkt nadat u in het menu op Symbol-klasse importeren hebt geklikt.
config.name.showImportXmlInfo = Toon informatie voordat u XML importeert
config.description.showImportXmlInfo = Geeft informatie weer over hoe XML-import werkt nadat u op XML importeren in het menu hebt geklikt.
#after 15.1.1
config.name.lastSessionTagListSelection = Laatste sessie taglijstselectie
config.description.lastSessionTagListSelection = Bevat de selectie van de laatste sessie in de lijstselectieweergave
config.name.lastView = Laatste weergave
config.description.lastView = Laatst weergegeven weergavemodus
config.name.swfSpecificCustomConfigs = SWF-specifieke aangepaste configuraties
config.description.swfSpecificCustomConfigs = Bevat de SWF-specifieke configuraties in aangepast formaat
config.name.warningOpeningReadOnly = Waarschuw bij het openen van een alleen-lezen SWF
config.description.warningOpeningReadOnly = Toon een waarschuwing bij het openen van SWF vanuit een alleen-lezen bron
# after 16.1.0
config.name.showImportImageInfo = Toon informatie voordat u afbeeldingen importeert
config.description.showImportImageInfo = Geeft informatie weer over hoe het importeren van afbeeldingen werkt nadat u op Afbeeldingen importeren in het menu hebt geklikt.
config.name.autoPlaySwfs = Speel SWF-voorbeelden automatisch af
config.description.autoPlaySwfs = Vouw het eerste niveau van de structuur uit bij het laden van SWF
config.name.expandFirstLevelOfTreeOnLoad = Vouw het eerste niveau van de structuur uit bij het laden van SWF
config.description.expandFirstLevelOfTreeOnLoad = Breidt automatisch het eerste niveau van knooppunten in de boom uit op SWF geopend.
# after 16.2.0
config.name.allowPlacingDefinesIntoSprites = Sta het plaatsen van definieertags in DefineSprite toe
config.description.allowPlacingDefinesIntoSprites = Maakt het plaatsen (verplaatsen/kopi\u00ebren/slepen) van definieer typetags in DefineSp mogelijk
config.name.allowDragAndDropInTagListTree = Sta slepen en neerzetten toe in de taglijstweergave
config.description.allowDragAndDropInTagListTree = Maakt het verplaatsen/kopi\u00ebren van tags mogelijk met slepen en neerzetten in de boomstructuur van de taglijstweergave.
config.name.allowMiterClipLinestyle = (VERWIJDERD) Lijnstijlen voor verstekclips toestaan \u200b\u200b(LANGZAAM)
config.description.allowMiterClipLinestyle = Sta het gebruik van een aangepaste renderer toe die verstekcliplijnstijlen ondersteunt, maar langzaam is.
advancedSettings.search = Zoekopdracht:
# after 16.3.1
config.name.animateSubsprites = Animeer subsprites in preview
config.description.animateSubsprites = Sta subsprite-animatie toe in het tijdlijnvoorbeeld.
config.name.autoPlayPreviews = Speel voorvertoningen automatisch af
config.description.autoPlayPreviews = Speel automatisch voorbeelden af.
config.name.maxCachedTime = Maximale tijdelijke cachetijd
config.description.maxCachedTime = Maximale tijd in milliseconden voordat het item (dat sindsdien niet meer is geopend) uit de cache wordt verwijderd. Zet dit op 0 voor onbeperkt caching.
config.name.airLibLocation = 6) AIR-bibliotheekpad (airglobal.swc)
config.description.airLibLocation = Locatie van de AIR-bibliotheek airglobal.swc. Het kan voornamelijk worden gebruikt voor AS3-compilatie.
config.name.showImportShapeInfo = Show information before importing shapes
config.description.showImportShapeInfo = Geeft informatie weer over hoe het importeren van vormen werkt nadat u op vormen importeren in het menu hebt geklikt.
config.name.pinnedItemsTagTreePaths = Paden voor vastgezette items in de tagstructuur
config.description.pinnedItemsTagTreePaths = Paden van knooppunten van de tagboom die zijn vastgezet.
config.name.pinnedItemsTagListPaths = Paden voor vastgezette items in de boomstructuur van de tagslijst
config.description.pinnedItemsTagListPaths = Paden van knooppunten van de taglijstweergaveboom die zijn vastgezet.
config.name.flattenASPackages = Maak ActionScript-pakketten plat
config.description.flattenASPackages = Maak \u00e9\u00e9n item per pakket in plaats van een pakketboom.
config.name.gui.scale = UI-schaalfactor
config.description.gui.scale = Schaalfactor van grafische interface. Stel dit in op 2.0 op Mac-retina-displays. Echt afsluiten van de applicatie (niet alleen opnieuw opstarten na het vragen) is vereist.
config.name.warning.video.vlc = Waarschuw voor ontbrekende VLC
config.description.warning.video.vlc = Waarschuwing weergeven dat VLC-mediaspeler vereist is bij het openen van SWF's met DefineVideoStream-tags wanneer VLC niet beschikbaar is.
config.name.playFrameSounds = Speel framegeluiden af
config.description.playFrameSounds = Speel geluiden af \u200b\u200bop het weergeven van frames.
config.name.fixAntialiasConflation = Probeer antialias-conflatie op te lossen (EXPERIMENTEEL, LANGZAAM)
config.description.fixAntialiasConflation = Probeert samensmeltingsartefacten tussen aangrenzende vormen, veroorzaakt door anti-aliasing, op te lossen. Dit vertraagt \u200b\u200bhet renderen. Experimentele functie.
config.name.autoPlaySounds = Geluiden automatisch afspelen
config.description.autoPlaySounds = Speel automatisch geluiden af \u200b\u200b(DefineSound) op boomknooppunt
config.name.deobfuscateAs12RemoveInvalidNamesAssignments=AS1/2 deobfuscatie: verwijder variabeledeclaraties met versluierde namen
config.description.deobfuscateAs12RemoveInvalidNamesAssignments=Tijdens de deobfuscatie van AS1/2 verwijdert u de variabeledeclaraties die een niet-standaard naam hebben. WAARSCHUWING: Dit kan SWF's beschadigen die afhankelijk zijn van onduidelijke namen.
config.name.gui.splitPanePlace.dividerLocationPercent = (Interne) Splitterplaatslocatie
config.name.gui.splitPaneTransform1.dividerLocationPercent=(Interne) Locatie splittertransformatie1
config.name.gui.splitPaneTransform2.dividerLocationPercent=(Interne) Locatie splittertransformatie2
config.name.gui.transform.lastExpandedCards = (Intern) Laatste uitgebreide transformatiekaarten
config.name.doubleClickNodeToEdit = Dubbelklik om te beginnen met bewerken
config.description.doubleClickNodeToEdit = Dubbelklikken op het boomknooppunt start de bewerking ervan.
config.name.warningDeobfuscation = Waarschuw bij overschakelingsdeobfuscatie
config.description.warningDeobfuscation = Waarschuwing weergeven bij het in-/uitschakelen van deobfuscatie.
config.name.warningRenameIdentifiers = Waarschuw bij het wisselen van identifiers voor automatisch hernoemen
config.description.warningRenameIdentifiers = Waarschuwing weergeven bij het inschakelen van de functie voor automatisch hernoemen van ID's.
config.name.showImportMovieInfo = Toon informatie voordat u films importeert
config.description.showImportMovieInfo = Geeft informatie weer over hoe het importeren van films werkt nadat u op Films importeren in het menu hebt geklikt.
config.name.showImportSoundInfo = Toon informatie voordat u geluiden importeert
config.description.showImportSoundInfo = Geeft informatie weer over hoe het importeren van geluiden werkt nadat u op Geluiden importeren in het menu hebt geklikt.
config.name.svgRetainBounds = Behoud vormgrenzen tijdens SVG-export
config.description.svgRetainBounds = Tijdens SVG-export wordt de x- en y-positie van de vorm precies zo ge\u00ebxporteerd als in SWF (bijvoorbeeld positief of negatief).
config.name.disableBitmapSmoothing = Schakel het verzachten van bitmaps uit
config.description.disableBitmapSmoothing = Schakel vloeiende bitmapvullingen uit tijdens weergave - toon alles als niet-vloeiend (gepixeld).
config.name.pinnedItemsScrollPos = Vastgezette items scrollen/caret-posities
config.description.pinnedItemsScrollPos = Scroll- of cursorposities van vastgezette items.
config.name.maxRememberedScrollposItems = Maximaal aantal onthouden scrollposities
config.description.maxRememberedScrollposItems = Maximaal aantal onthouden scrollpositie-items.
config.name.rememberScriptsScrollPos = Onthoud scripts scroll/caret-positie
config.description.rememberScriptsScrollPos = De scriptscroll/caret-positie blijft behouden bij het wisselen van items en wordt opgeslagen voor vastgezette items.
config.name.rememberFoldersScrollPos = Onthoud de scrollpositie van mappen
config.description.rememberFoldersScrollPos = De scrollpositie van mappen blijft behouden bij het wisselen van items en wordt opgeslagen voor vastgezette items.
#after 18.3.6
config.name.warning.initializers.class = Waarschuw bij bewerking van AS3-klassekenmerken over scriptinitialisatie
config.description.warning.initializers.class = Waarschuwing weergeven bij bewerking van AS3-klassekenmerken over initialisatie
#after 18.4.1
config.name.maxCachedNum = Maximaal aantal in de cache opgeslagen items per enkele cache
config.description.maxCachedNum = Maximaal aantal in de cache opgeslagen items voordat oudere items uit de cache worden verwijderd. Lagere waarde = minder geheugen, langzamere app. Hogere waarde = meer geheugen, snellere app. Zet dit op 0 voor onbeperkt caching.
config.name.warning.cannotencrypt = Waarschuw wanneer het niet gecodeerd kan worden opgeslagen
config.description.warning.cannotencrypt = Waarschuwing weergeven wanneer SWF-bestand dat is gecodeerd met HARMAN Air niet kan worden opgeslagen
#after 18.5.0
config.name.lastExportEnableEmbed = Laatste instelling voor het exporteren van ingesloten assets
config.description.lastExportEnableEmbed = Laatste instelling voor het exporteren van ingebedde middelen via [Insluiten]-metagegevens.
config.name.lastFlaExportVersion = Laatste FLA-exportversie
config.description.lastFlaExportVersion = Laatst ge\u00ebxporteerde FLA-versie
config.name.lastFlaExportCompressed = Laatste FLA-export gecomprimeerd
config.description.lastFlaExportCompressed = Laatst ge\u00ebxporteerde FLA-versie gecomprimeerd
#after 19.0.0
config.name.showImportSpriteInfo = Toon informatie voordat sprites worden ge\u00efmporteerd
config.description.showImportSpriteInfo = Toont wat informatie over hoe het importeren van sprites werkt nadat je op Sprites importeren in het menu hebt geklikt.
config.name.displayAs12PCodeDocsPanel=Toon documentenpaneel in AS1/2 P-code
config.description.displayAs12PCodeDocsPanel=Toon paneel met documentatie van acties in AS1/2 P-code bewerking en weergave
config.name.gui.action.splitPane.docs.dividerLocationPercent = (Intern) AS 1/2 splitPanedocsdividerLocationPercent
config.description.action.avm2.splitPane.docs.dividerLocationPercent=AS 1/2 splitPane docs-verdeler Locatie Percentage
#after 19.1.2
config.name.rememberLastScreen = Onthoud het laatst gebruikte scherm (op meerdere monitoren)
config.description.rememberLastScreen = Onthoud het laatst gebruikte scherm bij configuratie met meerdere schermapparaten (monitoren)
config.name.lastMainWindowScreenIndex = Laatste hoofdvenster schermindex
config.description.lastMainWindowScreenIndex = Laatste hoofdvenster schermindex
config.name.lastMainWindowScreenX = Laatste hoofdvensterscherm X
config.description.lastMainWindowScreenX = Laatste hoofdvensterscherm X-co\u00f6rdinaat
config.name.lastMainWindowScreenY = Laatste hoofdvensterscherm Y
config.description.lastMainWindowScreenY = Laatste hoofdvensterscherm Y-co\u00f6rdinaat
config.name.lastMainWindowScreenWidth = Schermbreedte laatste hoofdvenster
config.description.lastMainWindowScreenWidth = Schermbreedte laatste hoofdvenster
config.name.lastMainWindowScreenHeight = Schermbreedte laatste hoofdvenster
config.description.lastMainWindowScreenHeight = Schermbreedte laatste hoofdvenster
config.name.displayAs12PCodePanel=Toon AS1/2 P-code paneel
config.description.displayAs12PCodePanel=Toon paneel met gedeassembleerde P-codeacties voor ActionScript 1 en 2
config.name.displayAs3PCodePanel=Toon AS3 P-code paneel
config.description.displayAs3PCodePanel=Toon paneel met gedeassembleerde P-code-instructies voor ActionScript 3
config.name.flaExportUseMappedFontLayout = FLA-export - gebruik toegewezen lettertype-indeling
config.description.flaExportUseMappedFontLayout = letterafstand wanneer het daadwerkelijke lettertype geen lay-out heeft tijdens FLA-export.
#after 20.0.0
config.name.formatting.tab.size = Tab-grootte
config.description.formatting.tab.size = Aantal spaties per tab
config.name.boxBlurPixelsLimit = Box vervangingsfilter pixels limiet
config.description.boxBlurPixelsLimit = Maximaal aantal pixels om het boxblur-filter te berekenen. De werkelijke limiet is dit getal vermenigvuldigd met 10.000. Als het aantal pixels groter is, worden blurX en blurY verlaagd.
config.name.as3ExportNamesUseClassNamesOnly=Ge\u00ebxporteerde objecten hebben namen die alleen op klassen zijn gebaseerd (AS3)
config.description.as3ExportNamesUseClassNamesOnly=Ge\u00ebxporteerde objectbestanden (afbeeldingen, geluid, ...) nemen alleen namen over van de SymbolClass-tag - de aan hen toegewezen klassen. Er is geen teken-ID toegevoegd. Ook wanneer meerdere klassen aan hetzelfde item zijn toegewezen, wordt het meerdere keren ge\u00ebxporteerd. (Voor ActionScript 3 SWF's)
config.name.jnaTempDirectory = JNA Tijdelijke map
config.description.jnaTempDirectory = Tijdelijk mappad voor JNA DLL's, enz. Dit moet worden ingesteld op een pad dat geen Unicode-tekens bevat. Als dit niet is ingesteld, wordt de huidige TEMP-directory van de gebruiker gebruikt.
config.name.flaExportFixShapes = FLA-export - vormen repareren (langzaam)
config.description.flaExportFixShapes = Pas de procedure toe voor het splitsen van overlappende randen om ontbrekende vullingen op bepaalde vormen te corrigeren. Dit kan bij sommige complexe vormen erg traag zijn
