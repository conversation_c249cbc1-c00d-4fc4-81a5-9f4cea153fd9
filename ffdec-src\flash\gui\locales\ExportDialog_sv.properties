# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Former
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 Canvas
texts = Texter
texts.plain = Oformaterad text
texts.formatted = Formaterad text
texts.svg = SVG
images = Bilder
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Filmer
movies.flv = FLV (Inget Ljud)
sounds = Ljud
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (Bara ljud)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Skript
scripts.as = AS
scripts.pcode = P-Kod
scripts.pcode_hex=P-Kod med Hex
scripts.hex = Hex
scripts.constants = Constants
binaryData = Bin\u00e4r data
binaryData.raw = R\u00e5
dialog.title = Exportera...
button.ok = Godk\u00e4nn
button.cancel = Avbryt
morphshapes = MorphFormer
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 Canvas
frames = Frames
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 Canvas
frames.pdf = PDF
frames.bmp = BMP
sprites = Sprites
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = HTML5 Canvas
sprites.pdf = PDF
sprites.bmp = BMP
buttons = Knappar
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
fonts = Typsnitt
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Zoom
zoom.percent = %
zoom.invalid = Ogiltigt zoomv\u00e4rde.
symbolclass = Symbol Klass 
symbolclass.csv = CSV
