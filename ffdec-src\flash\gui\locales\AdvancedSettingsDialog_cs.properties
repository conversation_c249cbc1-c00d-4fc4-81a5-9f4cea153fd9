# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Pokro\u010dil\u00e1 nastaven\u00ed
advancedSettings.restartConfirmation = N\u011bkter\u00e9 zm\u011bny vy\u017eaduj\u00ed restart programu, ne\u017e se projev\u00ed. Chcete program restartovat nyn\u00ed?
advancedSettings.columns.name = N\u00e1zev
advancedSettings.columns.value = Hodnota
advancedSettings.columns.description = Popis
default = v\u00fdchoz\u00ed
config.group.name.export = Export
config.group.description.export = Nastaven\u00ed export\u016f
config.group.name.script = Skripty
config.group.description.script = Nastaven\u00ed dekompilace ActionScriptu
config.group.name.update = Aktualizace
config.group.description.update = Nastaven\u00ed kontroly aktualizac\u00ed
config.group.name.format = Form\u00e1tov\u00e1n\u00ed
config.group.description.format = Form\u00e1tov\u00e1n\u00ed ActionScript k\u00f3du
config.group.name.limit = Limity
config.group.description.limit = Limity dekompilace pro obfuskovan\u00fd k\u00f3d apod.
config.group.name.ui = Rozhran\u00ed
config.group.description.ui = Konfigurace u\u017eiavetelsk\u00e9ho rozhran\u00ed
config.group.name.debug = Lad\u011bn\u00ed
config.group.description.debug = Nastaven\u00ed pro lad\u011bn\u00ed
config.group.name.display = Zobrazen\u00ed
config.group.description.display = Zobrazen\u00ed flash objekt\u016f apod.
config.group.name.decompilation = Dekompilace
config.group.description.decompilation = Glob\u00e1ln\u00ed nastaven\u00ed ohledn\u011b dekompilace
config.group.name.other = Ostatn\u00ed
config.group.description.other = Ostatn\u00ed nastaven\u00ed bez kategorie
config.name.openMultipleFiles = Otev\u00edrat v\u00edce soubor\u016f najednou
config.description.openMultipleFiles = Povol\u00ed otev\u0159\u00edt v\u00edce jak 1 soubor sou\u010dasn\u011b v jednom okn\u011b.
config.name.decompile = Zobrazit zdrojov\u00fd soubor ActionScriptu
config.description.decompile = Toto lze zak\u00e1zat pro zobrazen\u00ed pouh\u00e9ho P-k\u00f3du.
config.name.dumpView = Dump zobrazen\u00ed
config.description.dumpView = Zobrazit dump raw dat.
config.name.useHexColorFormat = Hexadecim\u00e1ln\u00ed form\u00e1t barvy
config.description.useHexColorFormat = Zobrazit barvy v hexadecim\u00e1ln\u00edm form\u00e1tu.
config.name.parallelSpeedUp = Paraleln\u00ed zrychlen\u00ed
config.description.parallelSpeedUp = Paralelismus m\u016f\u017ee urychlit dekompilaci.
config.name.parallelSpeedUpThreadCount = Po\u010det vl\u00e1ken (0 = automaticky)
config.description.parallelSpeedUpThreadCount = Po\u010det vl\u00e1ken pro paraleln\u00ed zrychlen\u00ed. 0 = po\u010det procesor\u016f - 1.
config.name.autoDeobfuscate = Automatick\u00e1 deobfuskace
config.description.autoDeobfuscate = Spustit deobfuskaci pro ka\u017ed\u00fd soubor p\u0159ed dekompilac\u00ed ActionScriptu.
config.name.cacheOnDisk = Pou\u017e\u00edt cachov\u00e1n\u00ed na disku
config.description.cacheOnDisk = Cachovat ji\u017e dekompilovan\u00e9 \u010d\u00e1sti na disku m\u00edsto v pam\u011bti.
config.name.internalFlashViewer = Pou\u017e\u00edvat vlastn\u00ed prohl\u00ed\u017ee\u010d Flashe
config.description.internalFlashViewer = Pou\u017e\u00edvat intern\u00ed prohl\u00ed\u017ee\u010d flashe JPEXS m\u00edsto standardn\u00edho Flash Playeru pro zobrazen\u00ed \u010d\u00e1st\u00ed flashe.
config.name.gotoMainClassOnStartup = P\u0159ej\u00edt na hlavn\u00ed t\u0159\u00eddu po startu (AS3)
config.description.gotoMainClassOnStartup = P\u0159ejde na hlavn\u00ed t\u0159\u00eddu dokumentu psan\u00e9ho v AS3 p\u0159i otev\u0159en\u00ed SWF.
config.name.autoRenameIdentifiers = Automaticky p\u0159ejmenovat identifik\u00e1tory
config.description.autoRenameIdentifiers = Automaticky p\u0159ejmenovat neplatn\u00e9 identifik\u00e1tory p\u0159i na\u010dten\u00ed SWF.
config.name.offeredAssociation = (Intern\u00ed) Asociace se SWF soubory zobrazena
config.description.offeredAssociation = Dialog o asociaci se SWF soubory byl ji\u017e zobrazen.
config.name.decimalAddress = Pou\u017e\u00edvat dekadick\u00e9 adresy
config.description.decimalAddress = Pou\u017e\u00edvat dekadick\u00e9 adresy m\u00edsto hexadecim\u00e1ln\u00edch.
config.name.showAllAddresses = Zobrazit v\u0161echny adresy
config.description.showAllAddresses = Zobraz\u00ed adresy v\u0161ech instrukc\u00ed ActionScriptu.
config.name.useFrameCache = Pou\u017e\u00edvat sn\u00edmkovou cache
config.description.useFrameCache = Cachovat sn\u00edmky p\u0159ed renderov\u00e1n\u00edm znovu.
config.name.useRibbonInterface = Ribbon rozhran\u00ed
config.description.useRibbonInterface = Od\u0161krtn\u011bte pro pou\u017eit\u00ed klasick\u00e9ho rozhran\u00ed bez ribbon nab\u00eddky.
config.name.openFolderAfterFlaExport = Otev\u0159\u00edt slo\u017eku po exportu do FLA
config.description.openFolderAfterFlaExport = Zobraz\u00ed v\u00fdstupn\u00ed adres\u00e1\u0159 po exportu do FLA souboru.
config.name.useDetailedLogging = Detailn\u00ed logov\u00e1n\u00ed
config.description.useDetailedLogging = Logovat detailn\u00ed chybov\u00e9 zpr\u00e1vy a informace pro \u00fa\u010dely lad\u011bn\u00ed.
config.name._debugMode = FFDec v lad\u00edc\u00edm m\u00f3du
config.description._debugMode = M\u00f3d pro lad\u011bn\u00ed FFDecu. Zap\u00edn\u00e1 lad\u00edc\u00ed menu. Toto nem\u00e1 nic spole\u010dn\u00e9ho s funkcionalitou debuggeru.
config.name.resolveConstants = Dek\u00f3dovat konstanty v AS1/2 p-code
config.description.resolveConstants = Po deaktivaci zobraz\u00ed 'constantxx' m\u00edsto prav\u00fdch hodnot v okn\u011b P-k\u00f3du.
config.name.sublimiter = Omezen\u00ed po\u010dtu vno\u0159en\u00fdch vol\u00e1n\u00ed
config.description.sublimiter = Omezen\u00ed po\u010dtu vno\u0159en\u00fdch vol\u00e1n\u00ed pro obfuskovan\u00fd k\u00f3d.
config.name.exportTimeout = Celkov\u00fd \u010dasov\u00fd limit pro export (vte\u0159iny)
config.description.exportTimeout = Dekompil\u00e1tor ukon\u010d\u00ed export po dosa\u017een\u00ed tohoto limitu.
config.name.decompilationTimeoutFile = \u010casov\u00fd limit pro dekompilaci 1 souboru (vte\u0159iny)
config.description.decompilationTimeoutFile = Dekompil\u00e1tor ukon\u010d\u00ed dekompilaci ActionScriptu po dosa\u017een\u00ed tohoto limitu v jednom souboru.
config.name.paramNamesEnable = Povolit n\u00e1zvy parametr\u016f v AS3
config.description.paramNamesEnable = Pou\u017e\u00edv\u00e1n\u00ed n\u00e1zv\u016f parametr\u016f m\u016f\u017ee zp\u016fsobit probl\u00e9my proto\u017ee ofici\u00e1ln\u00ed programy jako Flash CS 5.5 vkl\u00e1daj\u00ed \u0161patn\u00e9 indexy n\u00e1zv\u016f parametr\u016f.
config.name.displayFileName = Zobrazit n\u00e1zev SWF souboru v titulku
config.description.displayFileName = Zobraz\u00ed n\u00e1zev SWF souboru/url v titulku okna (Lze pak d\u011blat screenshoty).
config.name._debugCopy = FFDec lad\u00edc\u00ed rekompilace
config.description._debugCopy = Zkus\u00ed zkompilovat SWF soubor znovu hned po otev\u0159en\u00ed, aby se ujistitl, \u017ee v\u00fdstupn\u00ed bin\u00e1rn\u00ed k\u00f3d je toto\u017en\u00fd. Pou\u017e\u00edvat jen pro LAD\u011aN\u00cd FFDecu!.
config.name.dumpTags = Vypisovat tagy do konzole
config.description.dumpTags = Vypisovat tagy do konzole p\u0159i \u010dten\u00ed SWF souboru.
config.name.decompilationTimeoutSingleMethod = AS3: \u010casov\u00fd limit na dekompilaci jedn\u00e9 metody (vte\u0159iny)
config.description.decompilationTimeoutSingleMethod = Dekompil\u00e1tor zastav\u00ed dekompilaci ActionScriptu po dosa\u017een\u00ed tohoto \u010dasu v jedn\u00e9 metod\u011b.
config.name.lastRenameType = (Intern\u00ed) Posledn\u00ed typ p\u0159ejmenov\u00e1n\u00ed
config.description.lastRenameType = Naposledy pou\u017eit\u00fd typ p\u0159ejmenov\u00e1n\u00ed identifik\u00e1tor\u016f.
config.name.lastSaveDir = (Intern\u00ed) Posledn\u00ed ukl\u00e1dac\u00ed adres\u00e1\u0159
config.description.lastSaveDir = Naposledy pou\u017eit\u00fd ukl\u00e1dac\u00ed adres\u00e1\u0159.
config.name.lastOpenDir = (Intern\u00ed) Posledn\u00ed otev\u00edrac\u00ed adres\u00e1\u0159
config.description.lastOpenDir = Naposledy pou\u017eit\u00fd otev\u00edrac\u00ed adres\u00e1\u0159.
config.name.lastExportDir = (Intern\u00ed) Posledn\u00ed adres\u00e1\u0159 exportu
config.description.lastExportDir = Naposledy pou\u017eit\u00fd adres\u00e1\u0159 exportu.
config.name.locale = Jazyk
config.description.locale = Identifik\u00e1tor n\u00e1rodn\u00edho prost\u0159ed\u00ed.
config.name.registerNameFormat = Form\u00e1t prom\u011bnn\u00fdch registr\u016f
config.description.registerNameFormat = Form\u00e1t lok\u00e1ln\u00edch n\u00e1zv\u016f prom\u011bnn\u00fdch registr\u016f. Pou\u017eijte %d pro \u010d\u00edslo registru.
config.name.maxRecentFileCount = Maxim\u00e1ln\u00ed po\u010det ned\u00e1vno otev\u0159en\u00fdch
config.description.maxRecentFileCount = Maxim\u00e1ln\u00ed po\u010det naposledy otev\u0159en\u00fdch soubor\u016f.
config.name.recentFiles = (Intern\u00ed) Posledn\u00ed soubory
config.description.recentFiles = Naposledy otev\u0159en\u00e9 soubory.
config.name.fontPairingMap = (Intern\u00ed) P\u00e1ry p\u00edsem pro import
config.description.fontPairingMap = P\u00e1ry p\u00edsem pro importov\u00e1n\u00ed nov\u00fdch znak\u016f.
config.name.lastUpdatesCheckDate = (Intern\u00ed) Datum posledn\u00ed kontroly aktualizac\u00ed
config.description.lastUpdatesCheckDate = Datum kdy byly naposledy zkontrolov\u00e1ny aktualizace na serveru.
config.name.gui.window.width = (Intern\u00ed) Posledn\u00ed \u0161\u00ed\u0159ka okna
config.description.gui.window.width = Naposledy ulo\u017een\u00e1 \u0161\u00ed\u0159ka okna.
config.name.gui.window.height = (Intern\u00ed) Posledn\u00ed v\u00fd\u0161ka okna 
config.description.gui.window.height = Naposledy ulo\u017een\u00e1 v\u00fd\u0161ka okna.
config.name.gui.window.maximized.horizontal = (Intern\u00ed) Maximalizace okna horizont\u00e1ln\u011b
config.description.gui.window.maximized.horizontal = Posledn\u00ed stav okna - maximalizov\u00e1n\u00ed horizont\u00e1ln\u011b.
config.name.gui.window.maximized.vertical = (Intern\u00ed)  Maximalizace okna vertik\u00e1ln\u011b
config.description.gui.window.maximized.vertical = Posledn\u00ed stav okna - maximalizov\u00e1n\u00ed vertik\u00e1ln\u011b.
config.name.gui.avm2.splitPane.dividerLocationPercent = (Intern\u00ed) AS3 pozice rozd\u011blova\u010de
config.description.gui.avm2.splitPane.dividerLocationPercent = 
config.name.gui.actionSplitPane.dividerLocationPercent = (Intern\u00ed) AS1/2 pozice rozd\u011blova\u010de
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Intern\u00ed) Pozice rozd\u011blova\u010de n\u00e1hledu
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent = (Intern\u00ed) Pozice rozd\u011blova\u010de 1
config.description.gui.splitPane1.dividerLocationPercent = 
config.name.gui.splitPane2.dividerLocationPercent = (Intern\u00ed) Pozice rozd\u011blova\u010de 2
config.description.gui.splitPane2.dividerLocationPercent = 
config.name.saveAsExeScaleMode = Zv\u011bt\u0161ovac\u00ed m\u00f3d ulo\u017een\u00ed jako EXE
config.description.saveAsExeScaleMode = M\u00f3d zv\u011bt\u0161ov\u00e1n\u00ed pro EXE export.
config.name.syntaxHighlightLimit = Maxim\u00e1ln\u00ed po\u010det znak\u016f pro zv\u00fdrazn\u011bn\u00ed syntaxe
config.description.syntaxHighlightLimit = Maxim\u00e1ln\u00ed po\u010det znak\u016f kdy se je\u0161t\u011b pou\u017eije zv\u00fdraz\u0148ova\u010d syntaxe.
config.name.guiFontPreviewSampleText = (Intern\u00ed) Posledn\u00ed text n\u00e1hledu fontu
config.description.guiFontPreviewSampleText = Po\u0159ad\u00ed v seznamu naposledy pou\u017eit\u00e9ho n\u00e1hledu fontu.
config.name.gui.fontPreviewWindow.width = (Intern\u00ed) Posledn\u00ed \u0161\u00ed\u0159ka okna n\u00e1hledu fontu
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Intern\u00ed) Posledn\u00ed v\u00fd\u0161ka okna n\u00e1hledu fontu
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Intern\u00ed) Posledn\u00ed x pozice okna n\u00e1hledu fontu
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Intern\u00ed) Posledn\u00ed y pozice okna n\u00e1hledu fontu
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Znak\u016f na odsazen\u00ed
config.description.formatting.indent.size = Po\u010det mezer (nebo tab\u016f) na jedno odsazen\u00ed.
config.name.formatting.indent.useTabs = Tabel\u00e1tor jako odsazen\u00ed
config.description.formatting.indent.useTabs = Pou\u017e\u00edvat tabel\u00e1tor jako odsazen\u00ed m\u00edsto mezer.
config.name.beginBlockOnNewLine = Slo\u017een\u00e1 z\u00e1vorka na nov\u00e9m \u0159\u00e1dku
config.description.beginBlockOnNewLine = Slo\u017een\u00e1 z\u00e1vorka za\u010d\u00e1tku bloku je na nov\u00e9 \u0159\u00e1dce.
config.name.check.updates.delay = Interval kontroly aktualizac\u00ed
config.description.check.updates.delay = Minim\u00e1ln\u00ed \u010das mezi kontrolami na aktualizace p\u0159i startu aplikace.
config.name.check.updates.stable = Kontrolovat stabiln\u00ed verze
config.description.check.updates.stable = Kontrolovat aktualizace stabiln\u00edch verz\u00ed.
config.name.check.updates.nightly = Kontrolovat nightly verze
config.description.check.updates.nightly = Kontrolovat aktualizace nightly verz\u00ed.
config.name.check.updates.enabled = Kontrolovat aktualizace
config.description.check.updates.enabled = Automaticky kontrolovat aktualizace p\u0159i startu aplikace.
config.name.export.formats = (Intern\u00ed) Form\u00e1ty exportu
config.description.export.formats = Naposledy pou\u017eit\u00e9 form\u00e1ty exportu.
config.name.textExportSingleFile = Exportovat texty do jednoho souboru
config.description.textExportSingleFile = Exportovat texty do jednoho souboru m\u00edsto do v\u00edce.
config.name.textExportSingleFileSeparator = Odd\u011blova\u010d text\u016f p\u0159i exportu do 1 souboru
config.description.textExportSingleFileSeparator = Text vkl\u00e1dan\u00fd mezi texty do exportu 1 souboru.
config.name.textExportSingleFileRecordSeparator = Odd\u011blova\u010d z\u00e1znam\u016f p\u0159i exportu do 1 souboru
config.description.textExportSingleFileRecordSeparator = Text vkl\u00e1dan\u00fd mezi z\u00e1znamy do exportu 1 souboru.
config.name.warning.experimental.as12edit = Varovat p\u0159i p\u0159\u00edm\u00e9 editaci AS1/2
config.description.warning.experimental.as12edit = Zobrazovat varov\u00e1n\u00ed p\u0159i AS1/2 experiment\u00e1ln\u00ed p\u0159\u00edm\u00e9 editaci.
config.name.warning.experimental.as3edit = Varovat p\u0159i p\u0159\u00edm\u00e9 editaci AS 3
config.description.warning.experimental.as3edit = Zobrazovat varov\u00e1n\u00ed p\u0159i AS3 experiment\u00e1ln\u00ed p\u0159\u00edm\u00e9 editaci.
config.name.packJavaScripts = Komprimovat JavaScripty
config.description.packJavaScripts = Spou\u0161t\u011bt komprim\u00e1tor JavaScript\u016f na skripty vytvo\u0159en\u00e9 p\u0159i exportu do Canvasu.
config.name.textExportExportFontFace = Pou\u017e\u00edvat font-face v SVG exportu
config.description.textExportExportFontFace = Vkl\u00e1dat soubory p\u00edsem do SVG pou\u017eit\u00edm font-face m\u00edsto tvar\u016f.
config.name.lzmaFastBytes = LZMA fast bytes (platn\u00e9 hodnoty: 5-255)
config.description.lzmaFastBytes = Parametr fast bytes LZMA enkoderu.
config.name.pluginPath = Plugin Path
config.description.pluginPath = -
config.name.showMethodBodyId = Zobrazovat id body metod
config.description.showMethodBodyId = Zobrazuje id body metody pro import p\u0159es p\u0159\u00edkazovou \u0159\u00e1dku.
config.name.export.zoom = (Internal) P\u0159ibl\u00ed\u017een\u00ed exportu
config.description.export.zoom = Naposledy pou\u017eit\u00e9 nastaven\u00ed p\u0159ibl\u00ed\u017een\u00ed.
config.name.debuggerPort = Port Debuggeru
config.description.debuggerPort = Port pou\u017e\u00edvan\u00fd pro lad\u011bn\u00ed p\u0159es sockety.
config.name.displayDebuggerInfo = (Internal) Zobrazit informace o debuggeru
config.description.displayDebuggerInfo = Zobraz\u00ed informace o debuggeru p\u0159edt\u00edm, ne\u017e je zapnut.
config.name.randomDebuggerPackage = Pou\u017e\u00edt n\u00e1hodn\u00e9 jm\u00e9no bal\u00ed\u010dku pro Debugger
config.description.randomDebuggerPackage = Toto p\u0159ejmenuje bal\u00ed\u010dek debuggeru na n\u00e1hodn\u00fd \u0159et\u011bzec co\u017e zt\u00ed\u017e\u00ed detekci ActionScriptem.
config.name.lastDebuggerReplaceFunction = (Internal) Last selected trace replacement
config.description.lastDebuggerReplaceFunction = Function name which was last selected in replace trace function with debugger.
config.name.getLocalNamesFromDebugInfo = AS3: Z\u00edsk\u00e1vat n\u00e1zvy lok\u00e1ln\u00edch registr\u016f z lad\u00edc\u00edch informac\u00ed
config.description.getLocalNamesFromDebugInfo = Pokud jsou p\u0159\u00edtomn\u00e9 informace o lad\u011bn\u00ed, p\u0159ejmenovat lok\u00e1ln\u00ed registry z _loc_x_ na re\u00e1ln\u00e9 n\u00e1zvy. Toto lze vypnout proto\u017ee n\u011bkter\u00e9 obfusk\u00e1tory na tomto m\u00edst\u011b pou\u017e\u00edvaj\u00ed neplatn\u00e9 n\u00e1zvy.
config.name.tagTreeShowEmptyFolders = Zobrazovat pr\u00e1zdn\u00e9 slo\u017eky
config.description.tagTreeShowEmptyFolders = Zobrazovat pr\u00e1zdn\u00e9 slo\u017eky ve stromu tag\u016f.
config.name.autoLoadEmbeddedSwfs = Automaticky na\u010d\u00edtat vlo\u017een\u00e1 SWF
config.description.autoLoadEmbeddedSwfs = Automaticky na\u010d\u00edtat vlo\u017een\u00e1 SWF z DefineBinaryData tag\u016f.
config.name.overrideTextExportFileName = Zm\u011bnit n\u00e1zev souboru pro export textu
config.description.overrideTextExportFileName = M\u016f\u017eete p\u0159\u00edzp\u016fsobit n\u00e1zev souboru exportovan\u00e9ho textu. Pou\u017eijte z\u00e1stupn\u00fd text {filename} pro pou\u017eit\u00ed n\u00e1zvu aktu\u00e1ln\u00edho SWF.
config.name.showOldTextDuringTextEditing = Zobrazovat star\u00fd text b\u011bhem editace textu
config.description.showOldTextDuringTextEditing = Zobrazit origin\u00e1ln\u00ed text z textov\u00e9ho tagu \u0161edivou barvou v oblasti n\u00e1hledu.
config.group.name.import = Import
config.group.description.import = Nastaven\u00ed importu
config.name.textImportResizeTextBoundsMode = M\u00f3d roz\u0161i\u0159ov\u00e1n\u00ed okraj\u016f textu
config.description.textImportResizeTextBoundsMode = M\u00f3d roz\u0161\u00ed\u0159en\u00ed okraj\u016f textu po editaci.
config.name.showCloseConfirmation = Zobrazit znovu potvrzen\u00ed zav\u0159en\u00ed SWF
config.description.showCloseConfirmation = Zobrazit znovu potvrzen\u00ed zav\u0159en\u00ed SWF p\u0159i zm\u011bn\u011b souboru. .
config.name.showCodeSavedMessage = Zobrazit znovu potvrzen\u00ed o ulo\u017een\u00ed k\u00f3du
config.description.showCodeSavedMessage = Zobrazit znovu potvrzen\u00ed o ulo\u017een\u00ed k\u00f3du.
config.name.showTraitSavedMessage = Zobrazit znovu potvrzen\u00ed o ulo\u017een\u00ed vlastnosti
config.description.showTraitSavedMessage = Zobrazit znovu potvrzen\u00ed o ulo\u017een\u00ed vlastnosti.
config.name.updateProxyAddress = Adresa http Proxy pro kontrolu aktualizac\u00ed
config.description.updateProxyAddress = Adresa http proxy address pro kontrolu aktualizac\u00ed. Form\u00e1t: example.com:8080.
config.name.editorMode = M\u00f3d editoru
config.description.editorMode = Textov\u00e1 pol\u00ed\u010dka budou automaticky editovateln\u00e1 kdy\u017e vyberete text nebo skript.
config.name.autoSaveTagModifications = Automaticky ukl\u00e1dat zm\u011bny tag\u016f
config.description.autoSaveTagModifications = Ulo\u017eit zm\u011bny kdy\u017e vyberete jin\u00fd tag ve stromu.
config.name.saveSessionOnExit = Ulo\u017eit session p\u0159i zav\u0159en\u00ed
config.description.saveSessionOnExit = Ulo\u017eit aktu\u00e1ln\u00ed session a otev\u0159\u00edt po restartu dekompileru (funguje jen s re\u00e1ln\u00fdmi soubory).
config.name._showDebugMenu = Zobrazit lad\u00edc\u00ed menu FFDecu
config.description._showDebugMenu = Zobraz\u00ed lad\u00edc\u00ed menu v ribbon pruhu pro lad\u011bn\u00ed dekompileru.
config.name.allowOnlyOneInstance = Povolit pouze jednu instanci FFDec (Pouze na OS Windows)
config.description.allowOnlyOneInstance = FFDec lze pot\u00e9 spustit pouze jednou, v\u0161echny otev\u00edran\u00e9 soubory budou p\u0159id\u00e1ny do jednoho okna. Funguje to pouze s opera\u010dn\u00edm syst\u00e9mem Windows.
config.name.scriptExportSingleFile = Exportovat skripty do jednoho souboru
config.description.scriptExportSingleFile = Export skript\u016f do jednoho souboru m\u00edsto do mnoha.
config.name.setFFDecVersionInExportedFont = Nastavit verzi FFDec do exportovan\u00e9ho p\u00edsma
config.description.setFFDecVersionInExportedFont = Kdy\u017e je toto nastaven\u00ed zak\u00e1z\u00e1no, FFDec nep\u0159id\u00e1 \u010d\u00edslo aktu\u00e1ln\u00ed verze do exportovan\u00e9ho p\u00edsma.
config.name.gui.skin = Vzhled u\u017eivatelsk\u00e9ho rozhran\u00ed
config.description.gui.skin = Barevbn\u00e9 sch\u00e9ma vzhledu.
config.name.lastSessionFiles = Posledn\u00ed soubory v session
config.description.lastSessionFiles = Obsahuje otev\u0159en\u00e9 soubory z posledn\u00ed session.
config.name.lastSessionSelection = V\u00fdb\u011br v posledn\u00ed session
config.description.lastSessionSelection = Obsahuje v\u00fdb\u011br z posledn\u00ed session.
config.name.loopMedia = P\u0159ehr\u00e1vat zvuky a sprity dokola
config.description.loopMedia = Automaticky restartovat p\u0159ehr\u00e1v\u00e1n\u00ed zvuk\u016f a sprit\u016f.
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Internal) Um\u00edst\u011bn\u00ed rozd\u011blova\u010de timeline
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Ke\u0161ovat obr\u00e1zky
config.description.cacheImages = Ke\u0161ovat dek\u00f3dovan\u00e9 objekty obr\u00e1zk\u016f.
config.name.swfSpecificConfigs = Konfigurace specifick\u00e9 pro jednotliv\u00e1 SWF
config.description.swfSpecificConfigs = Obsahuje konfigurace pro jednotliv\u00e1 SWF.
config.name.exeExportMode = Export m\u00f3d EXE
config.description.exeExportMode = Export m\u00f3d EXE.
config.name.ignoreCLikePackages = Ignorovat FlashCC / Alchemy a podobn\u00e9 bal\u00ed\u010dky
config.description.ignoreCLikePackages = FlashCC/Alchemy bal\u00ed\u010dky nelze obvykle korektn\u011b dekompilovaty. M\u016f\u017eete je skr\u00fdt pro zrychlen\u00ed dekompilace jin\u00fdch bal\u00ed\u010dk\u016f.
config.name.overwriteExistingFiles = P\u0159episovat existuj\u00edc\u00ed soubory
config.description.overwriteExistingFiles = P\u0159episovat existuj\u00edc\u00ed soubory b\u011bhem exportu. Moment\u00e1ln\u011b pouze pro AS2/3 skripty.
config.name.smartNumberFormatting = Pou\u017e\u00edvat chytr\u00e9 form\u00e1tov\u00e1n\u00ed \u010d\u00edsel
config.description.smartNumberFormatting = Form\u00e1tovat speci\u00e1ln\u00ed \u010d\u00edsla (nap\u0159\u00edklad barvy a \u010dasy).
config.name.enableScriptInitializerDisplay = (ODSTRAN\u011aNO) Zobrazit inicializ\u00e1tory skriptu
config.description.enableScriptInitializerDisplay = Povol\u00ed zobrazen\u00ed inicializ\u00e1tor\u016f skript\u016f a jejich editaci. Tohle nastaven\u00ed m\u016f\u017ee p\u0159idat jednu \u0159\u00e1dku do ka\u017ed\u00e9 t\u0159\u00eddy pro zv\u00e1raz\u0148ov\u00e1n\u00ed.
config.name.autoOpenLoadedSWFs = Otev\u00edrat na\u010d\u00edtan\u00e1 SWF b\u011bhem p\u0159ehr\u00e1v\u00e1n\u00ed (Extern\u00ed p\u0159ehr\u00e1va\u010d = jen WIN)
config.description.autoOpenLoadedSWFs = Otev\u00edr\u00e1 automaticky v\u0161echna SWF na\u010d\u00edtan\u00e1 AS3 t\u0159\u00eddou Loader b\u011bhem p\u0159ehr\u00e1v\u00e1n\u00ed v extern\u00edm p\u0159ehr\u00e1va\u010di v FFDec. Toto funguje pouze ve Windows.
config.name.lastSessionFileTitles = Titulky soubor\u016f z posledn\u00ed session
config.description.lastSessionFileTitles = Obsahuje titulky souboru z naposledy otev\u0159en\u00e9 session (nap\u0159\u00edklad kdy\u017e byly otev\u0159eny z URL apod.).
config.group.name.paths = Cesty
config.group.description.paths = Um\u00edst\u011bn\u00ed pot\u0159ebn\u00fdch soubor\u016f
config.group.tip.paths = Stahn\u011bte si projector a Playerglobal na <a href="%link1%">str\u00e1nk\u00e1ch adobe</a>. Flex SDK lze stahnout na <a href="%link2%">webu apache</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Cesta k Flash Player projectoru
config.description.playerLocation = Um\u00edst\u011bn\u00ed spustiteln\u00e9ho flash playeru. Pou\u017e\u00edvan\u00e9 pro akci Spustit.
config.name.playerDebugLocation = 2) Cesta k Flash Player projector content debuggeru
config.description.playerDebugLocation = Um\u00edst\u011bn\u00ed spustiteln\u00e9ho debug flash playeru. Pou\u017e\u00edvan\u00e9 pro akci Ladit.
config.name.playerLibLocation = 3) Cesta k PlayerGlobal (.swc)
config.description.playerLibLocation = Um\u00edst\u011bn\u00ed knihovny playerglobal.swc. Pou\u017e\u00edvan\u00e9 hlavn\u011b pro kompilaci AS3.
config.name.debugHalt = Pozastavit spou\u0161t\u011bn\u00ed po startu lad\u011bn\u00ed
config.description.debugHalt = Zapauzovat SWF ihned po spu\u0161t\u011bn\u00ed v re\u017eimu lad\u011bn\u00ed.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent = (Intern\u00ed) Um\u00edst\u011bn\u00ed rozd\u011blovn\u00edku lad\u00edc\u00edho menu
config.description.gui.avm2.splitPane.vars.dividerLocationPercent = 
tip = Tip: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Intern\u00ed) Um\u00edst\u011bn\u00ed rozd\u011blovn\u00edku lad\u00edc\u00edho menu v AS 1/2
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = Zpo\u017ed\u011bn\u00ed p\u0159ed zm\u011bnou SWF souboru v extern\u00edm p\u0159ehr\u00e1va\u010di v milisekund\u00e1ch
config.description.setMovieDelay = Nedoporu\u010dujeme m\u011bnit tuto hodnotu pod 1000ms.
config.name.warning.svgImport = Varovat p\u0159i SVG importu
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Pou\u017e\u00edvat nevyhlazovanou(non-smoothed) v\u00fdpl\u0148 kdy\u017e je tvar nahrazen obr\u00e1zkem
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12 = AS1/2 v intern\u00edm prohl\u00ed\u017ee\u010di flashe (Experiment\u00e1ln\u00ed)
config.description.internalFlashViewer.execute.as12 = Pokusit se spustit ActionScript 1/2 b\u011bhem p\u0159ehr\u00e1v\u00e1n\u00ed SWF pomoc\u00ed zobrazova\u010de integrovan\u00e9ho v dekompil\u00e1toru.
config.name.warning.hexViewNotUpToDate = Zobrazit Hex zobrazen\u00ed nen\u00ed aktu\u00e1ln\u00ed
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = Zobrazit \u00a7\u00a7dup instrukce
config.description.displayDupInstructions = Zobrazit \u00a7\u00a7dup instrukce k k\u00f3du. Bez nich, k\u00f3d m\u016f\u017ee j\u00edt l\u00e9pe zkompilovat, ale n\u011bkter\u00fd duplikovan\u00fd k\u00f3d se sideefekty m\u016f\u017ee b\u00fdt spou\u0161ten dvakr\u00e1t.
config.name.useRegExprLiteral = Dekompilovat RegExp jako /vzor/mod liter\u00e1l.
config.description.useRegExprLiteral = Pou\u017e\u00edt /vzor/mod syntaxi p\u0159i dekompilaci regul\u00e1rn\u00edch v\u00fdraz\u016f. new RegExp("pat","mod") je pou\u017eito v opa\u010dn\u00e9m p\u0159\u00edpad\u011b.
config.name.handleSkinPartsAutomatically = Automaticky po\u0159e\u0161it [SkinPart] metadata
config.description.handleSkinPartsAutomatically = Dekompiluje a p\u0159\u00edmo edituje [SkinPart] metadata automaticky. P\u0159i vypnut\u00ed je vid\u011bt a editovateln\u00fd _skinParts atribut a jeho getter.
config.name.simplifyExpressions = Zjednodu\u0161it v\u00fdrazy
config.description.simplifyExpressions = Vyhodnotit a zjednodu\u0161it v\u00fdrazy aby byl k\u00f3d l\u00e9pe \u010diteln\u00fd.
config.name.resetLetterSpacingOnTextImport = Resetovat mezery mezi p\u00edsmeny p\u0159i importu textu
config.description.resetLetterSpacingOnTextImport = Vhodn\u00e9 pro p\u00edsma s cyrilic\u00ed, proto\u017ee jsou \u0161ir\u0161\u00ed.
config.name.flexSdkLocation = 4) Cesta k Flex SDK slo\u017ece
config.description.flexSdkLocation = Um\u00edst\u011bn\u00ed Adobe Flex SDK. Je pou\u017e\u00edv\u00e1n nejv\u00edce pro AS3 kompilaci.
config.name.useFlexAs3Compiler = Pou\u017e\u00edt Flex SDK kompil\u00e1tor AS3
config.description.useFlexAs3Compiler = Pou\u017e\u00edt kompil\u00e1tor AS3 z Flex SDK b\u011bhem p\u0159\u00edm\u00e9 editace ActionScriptu. (Flex SDK slo\u017eka mus\u00ed b\u00fdt nastavena).
config.name.showSetAdvanceValuesMessage = Zobrazit znovu informace o nastaven\u00ed advance hodnot
config.description.showSetAdvanceValuesMessage = Zobrazit znovu informace o nastaven\u00ed advance hodnot.
config.name.gui.fontSizeMultiplier = N\u00e1sobitel velikosti p\u00edsma
config.description.gui.fontSizeMultiplier = N\u00e1sobitel velikosti p\u00edsma.
config.name.graphVizDotLocation = 5) Cesta k spustiteln\u00e9mu souboru GraphViz Dot
config.description.graphVizDotLocation = Cesta k dot.exe (\u010di podobn\u00e9mu pro linux) appikace GraphViz pro zobrazen\u00ed graf\u016f.
#Do not translate the Font Styles which is in the parenthesis:(Plain,Bold,Italic,BoldItalic)
config.name.gui.sourceFont = Zdrojov\u00fd styl p\u00edsma
config.description.gui.sourceFont = N\u00e1zevFontu-StylP\u00edsma(Plain,Bold,Italic,BoldItalic)-VelikostP\u00edsma.
#after 11.1.0
config.name.as12DeobfuscatorExecutionLimit = AS1/2 spou\u0161t\u011bc\u00ed limit deobfusk\u00e1toru
config.description.as12DeobfuscatorExecutionLimit = Maxim\u00e1ln\u00ed po\u010det instrukc\u00ed zpracovan\u00fdch b\u011bhem AS1/2 spou\u0161t\u011bc\u00ed deobfuskace.
#option that ignore in 8.0.1 and other versions
config.name.showOriginalBytesInPcodeHex = (Internal) Zobrazit p\u016fvodn\u00ed byty
config.description.showOriginalBytesInPcodeHex = zobrazit p\u016fvodn\u00ed byty v P-k\u00f3d hex.
config.name.showFileOffsetInPcodeHex = (Internal) Zobrazit pozici v souboru
config.description.showFileOffsetInPcodeHex = zobrazit pozici v souboru v P-k\u00f3d hex.
config.name._enableFlexExport = (Internal) enableFlexExport
config.description.enableFlexExport = povolit Flex Export.
config.name._ignoreAdditionalFlexClasses = (Internal) ignoreAdditionalFlexClasses
config.description.ignoreAdditionalFlexClasses = ignorovat P\u0159\u00eddavn\u00e9 Flex t\u0159\u00eddy.
config.name.hwAcceleratedGraphics = Hardwarov\u00e1 akcelerace grafiky
config.description.hwAcceleratedGraphics = Tohle zapne volbu "sun.java2d.opengl" pro hardwarov\u011b akcelerovanou grafiku.
config.name.gui.avm2.splitPane.docs.dividerLocationPercent = (Internal) splitPanedocsdividerLocationPercent
config.description.gui.avm2.splitPane.docs.dividerLocationPercent = pozice v procentech um\u00edst\u011bn\u00ed dokumenta\u010dn\u00edho splitPane.
config.name.gui.dump.splitPane.dividerLocationPercent = (Internal) dumpsplitPanedividerLocationPercent
config.description.gui.dump.splitPane.dividerLocationPercent = pozice v procentech um\u00edst\u011bn\u00ed dump splitPane.
#after 11.3.0
config.name.useAdobeFlashPlayerForPreviews = (P\u0159ekon\u00e1no) Pou\u017e\u00edvat Adobe Flash player pro n\u00e1hledy objekt\u016f
config.description.useAdobeFlashPlayerForPreviews = Pou\u017e\u00edvat Adobe Flash player pro n\u00e1hledy objekt\u016f. VAROV\u00c1N\u00cd: FlashPlayer nen\u00ed podporov\u00e1n\u00ed od 2021-01-12.
#after 12.0.1
config.name.showLineNumbersInPCodeGraphvizGraph = Zobrazovat \u010d\u00edsla \u0159\u00e1dek v Graphviz grafech
config.description.showLineNumbersInPCodeGraphvizGraph = Zobrazovat \u010d\u00edsla \u0159\u00e1dek v graphviz grafech P-k\u00f3du.
config.name.padAs3PCodeInstructionName = Zarovnat n\u00e1zvy instrukc\u00ed AS3 P-k\u00f3du
config.description.padAs3PCodeInstructionName = Zarovnat n\u00e1zvy instrukc\u00ed AS3 P-k\u00f3du mezerami.
#after 13.0.2
config.name.indentAs3PCode = Odsadit AS3 P-k\u00f3d
config.description.indentAs3PCode = Odsadit AS3 P-k\u00f3d bloky jako trait/body/code.
config.name.labelOnSeparateLineAs3PCode = N\u00e1v\u011b\u0161t\u00ed v AS3 P-k\u00f3du na zvl\u00e1\u0161tn\u00edm \u0159\u00e1dku
config.description.labelOnSeparateLineAs3PCode = N\u00e1v\u011b\u0161t\u00ed v AS3 P-k\u00f3du budou st\u00e1t na zvl\u00e1\u0161tn\u00edm \u0159\u00e1dku.
config.name.useOldStyleGetSetLocalsAs3PCode = Pou\u017e\u00edvat p\u016fvodn\u00ed form\u00e1t getlocal_x m\u00edsto getlocalx v AS3 P-k\u00f3du
config.description.useOldStyleGetSetLocalsAs3PCode = Pou\u017e\u00edvat p\u016fvodn\u00ed form\u00e1t getlocal_x, setlocal_x z FFDec 12.x a star\u0161\u00edho.
config.name.useOldStyleLookupSwitchAs3PCode = Pou\u017e\u00edvat star\u00fd styl lookupswitch bez hranat\u00fdch z\u00e1vorek v AS3 P-k\u00f3du
config.description.useOldStyleLookupSwitchAs3PCode = Pou\u017e\u00edvat star\u00fd styl lookupswitch z FFDec 12.x a star\u0161\u00edho.
#after 13.0.3
config.name.checkForModifications = Kontrolovat zm\u011bny soubor\u016f mimo FFDec
config.description.checkForModifications = Kontrolovat zm\u011bny soubor\u016f jin\u00fdmi aplikacemi a pt\u00e1t se na nov\u00e9 na\u010dten\u00ed.
config.name.warning.initializers = Varovat p\u0159i AS3 slot/const editaci o inicializ\u00e1torech
config.description.warning.initializers = Zobrazovat varov\u00e1n\u00ed p\u0159i editaci AS3 slot/const o inicializ\u00e1torech.
config.name.parametersPanelInSearchResults = Zobrazit panel s parametry ve v\u00fdsledc\u00edch vyhled\u00e1v\u00e1n\u00ed
config.description.parametersPanelInSearchResults = Zobraz\u00ed panel s parametry jako hledan\u00fd text / ignorovat velikost / regexp v okn\u011b v\u00fdsledk\u016f hled\u00e1n\u00ed.
config.name.displayAs3PCodeDocsPanel = Zobrazit panel s dokumentac\u00ed v AS3 P-k\u00f3du
config.description.displayAs3PCodeDocsPanel = Zobraz\u00ed panel s dokumentac\u00ed instrukc\u00ed a struktury k\u00f3du p\u0159i editaci a zobrazen\u00ed AS3 P-k\u00f3du.
config.name.displayAs3TraitsListAndConstantsPanel = Zobrazit panel se seznamem AS3 vlastnost\u00ed a konstant
config.description.displayAs3TraitsListAndConstantsPanel = Zobraz\u00ed panel se seznamem AS3 vlastnost\u00ed a konstant pod stromem tag\u016f.
#after 14.1.0
config.name.useAsTypeIcons = Pou\u017e\u00edt ikony skript\u016f na z\u00e1klad\u011b typu polo\u017eky 
config.description.useAsTypeIcons = Pou\u017eije r\u016fzn\u00e9 ikony pro r\u016fzn\u00e9 typy skript\u016f (t\u0159\u00edda/interface/frame/...).
config.name.limitAs3PCodeOffsetMatching = Limit matchov\u00e1n\u00ed offset\u016f v AS3 p-k\u00f3du
config.description.limitAs3PCodeOffsetMatching = Limit instrukc\u00ed v AS3 p-k\u00f3du kter\u00e9 jsou matchovan\u00e9 offsetem na AS3 skript.
#after 14.2.1
config.name.showSlowRenderingWarning = Zalogovat varov\u00e1n\u00ed kdy\u017e je renderov\u00e1n\u00ed p\u0159\u00edli\u0161 pomal\u00e9
config.description.showSlowRenderingWarning = Zaloguje varov\u00e1n\u00ed kdy\u017e intern\u00ed zobrazova\u010d flashe je p\u0159\u00edli\u0161 pomal\u00fd na zobrazen\u00ed obsahu.
#after 14.3.1
config.name.autoCloseQuotes = Automaticky uzav\u00edrat jednoduch\u00e9 uvozovky p\u0159i editov\u00e1n\u00ed skriptu
config.description.autoCloseQuotes = Automaticky vlo\u017e\u00ed druhou jednoduchou uvozovku ' po naps\u00e1n\u00ed prvn\u00ed.
config.name.autoCloseDoubleQuotes = Automaticky uzav\u00edrat dvojit\u00e9 uvozovky p\u0159i editov\u00e1n\u00ed skriptu
config.description.autoCloseDoubleQuotes = Automaticky vlo\u017e\u00ed druhou dvojitou uvozovku " po naps\u00e1n\u00ed prvn\u00ed.
config.name.autoCloseBrackets = Automaticky uzav\u00edrat hranat\u00e9 z\u00e1vorky p\u0159i editov\u00e1n\u00ed skriptu
config.description.autoCloseBrackets = Automaticky vlo\u017e\u00ed uzav\u00edrac\u00ed hranatou z\u00e1vorku ] p\u0159i naps\u00e1n\u00ed otev\u00edrac\u00ed [.
config.name.autoCloseParenthesis = Automaticky uzav\u00edrat kulat\u00e9 z\u00e1vorky p\u0159i editov\u00e1n\u00ed skriptu
config.description.autoCloseParenthesis = Automaticky vlo\u017e\u00ed uzav\u00edrac\u00ed kulatou z\u00e1vorku ) p\u0159i naps\u00e1n\u00ed otev\u00edrac\u00ed (.
config.name.showDialogOnError = Zobrazit dialog s chybami p\u0159i ka\u017ed\u00e9 chyb\u011b
config.description.showDialogOnError = Automaticky zobaz\u00ed chybov\u00fd dialog p\u0159i ka\u017ed\u00e9m v\u00fdskytu chyby.
#after 14.4.0
config.name.limitSameChars = Limit stejn\u00fdch znak\u016f pro \\{xx}C (opakovac\u00ed) escape sekvenci
config.description.limitSameChars = Maxim\u00e1ln\u00ed po\u010det stejn\u00fdch znak\u016f za sebou v P-code \u0159et\u011bzc\u00edch \u010di obfuskovan\u00fdch jm\u00e9nech p\u0159ed nahrazen\u00edm \\{xx}C opakovac\u00ed sekvenc\u00ed.
#after 14.5.2
config.name.showImportScriptsInfo = Zobrazit informaci p\u0159ed importem skript\u016f
config.description.showImportScriptsInfo = Zobraz\u00ed informace o tom jak import skript\u016f funguje po kliku na import skript\u016f v menu.
config.name.showImportTextInfo = Zobrazit informaci p\u0159ed importem textu
config.description.showImportTextInfo = Zobraz\u00ed informace o tom jak import text\u016f funguje po kliku na import skript\u016f v menu.
config.name.showImportSymbolClassInfo = Zobrazit informaci p\u0159ed importem Symbol-Class
config.description.showImportSymbolClassInfo = Zobraz\u00ed informace o tom jak import Symbol-Class funguje po kliku na import Symbol-Class v menu.
config.name.showImportXmlInfo = Zobrazit informaci p\u0159ed importem XML
config.description.showImportXmlInfo = Zobraz\u00ed informace o tom jak import XML funguje po kliku na import XML v menu.
#after 15.1.1
config.name.lastSessionTagListSelection = V\u00fdb\u011br ze seznamu tag z posledn\u00ed session
config.description.lastSessionTagListSelection = Obsahuje v\u00fdb\u011br z posledn\u00ed sessiony v zobrazen\u00ed seznamu tag\u016f.
config.name.lastView = Posledn\u00ed m\u00f3d zobrazen\u00ed
config.description.lastView = Naposledy zvolen\u00fd m\u00f3d zobrazen\u00ed.
config.name.swfSpecificCustomConfigs = Vlastn\u00ed konfigurace specifick\u00e1 pro jednotliv\u00e9 SWF
config.description.swfSpecificCustomConfigs = Obsahuje konfiguraci specifickou pro jednotliv\u00e1 SWF ve vlastn\u00edm form\u00e1tu.
config.name.warningOpeningReadOnly = Varovat p\u0159i otev\u00edr\u00e1n\u00ed SWF co jsou jen ke \u010dten\u00ed
config.description.warningOpeningReadOnly = Zobrazovat varov\u00e1n\u00ed p\u0159i otev\u00edr\u00e1n\u00ed SWF ze zdroj\u016f co jsou jen ke \u010dten\u00ed.
# after 16.1.0
config.name.showImportImageInfo = Zobrazit informaci p\u0159ed importem obr\u00e1zk\u016f
config.description.showImportImageInfo = Zobraz\u00ed informace o tom jak import obr\u00e1zk\u016f funguje po kliku na import obr\u00e1zk\u016f v menu.
config.name.autoPlaySwfs = Automaticky p\u0159ehr\u00e1vat n\u00e1hledy SWF
config.description.autoPlaySwfs = Automaticky p\u0159ehr\u00e1vat n\u00e1hled SWF p\u0159i v\u00fdb\u011bru SWF polo\u017eky.
config.name.expandFirstLevelOfTreeOnLoad = Rozbalit prvn\u00ed \u00farove\u0148 stromu p\u0159i na\u010dten\u00ed SWF
config.description.expandFirstLevelOfTreeOnLoad = Automaticky rozbal\u00ed prvn\u00ed \u00farove\u0148 polo\u017eek stromu p\u0159i otev\u0159en\u00ed SWF.
# after 16.2.0
config.name.allowPlacingDefinesIntoSprites = Povolit umis\u0165ov\u00e1n\u00ed defini\u010dn\u00edch tag\u016f do DefineSprite
config.description.allowPlacingDefinesIntoSprites = Povol\u00ed umis\u0165ov\u00e1n\u00ed (p\u0159esun/kop\u00edrov\u00e1n\u00ed/ta\u017een\u00ed dovnit\u0159) defini\u010dn\u00edch tag\u016f do DefineSprite.
config.name.allowDragAndDropInTagListTree = Povolit drag and drop v zobrazen\u00ed seznamu tag\u016f
config.description.allowDragAndDropInTagListTree = Povol\u00ed p\u0159esouv\u00e1n\u00ed / kop\u00edrov\u00e1n\u00ed tag\u016f pomoc\u00ed drag and drop v stromu zobrazen\u00ed seznamu tag\u016f.
config.name.allowMiterClipLinestyle = (REMOVED) Povolit styl \u010d\u00e1ry miter clip  (POMAL\u00c9)
config.description.allowMiterClipLinestyle = Povolit animace podsprit\u016f v n\u00e1hledu timeliny.
advancedSettings.search = Hledat:
# after 16.3.1
config.name.animateSubsprites = Animovat podsprity v n\u00e1hledu
config.description.animateSubsprites = Povolit animace podsprit\u016f na n\u00e1hledu \u010dasov\u00e9 osy.
config.name.autoPlayPreviews = Automaticky p\u0159ehr\u00e1vat n\u00e1hledy
config.description.autoPlayPreviews = Automaticky p\u0159ehr\u00e1vat n\u00e1hledy.
config.name.maxCachedTime = Maxim\u00e1ln\u00ed \u010das do\u010dasn\u00e9 cache
config.description.maxCachedTime = Maxim\u00e1ln\u00ed \u010das v milisekund\u00e1ch, kter\u00fd mus\u00ed ub\u011bhnout, aby byla polo\u017eka(kter\u00e1 nebyla mezit\u00edm aktivn\u00ed) odstran\u011bna z cache. Nastavte sem hodnotu 0 pro nekone\u010dn\u00e9 cachov\u00e1n\u00ed.
config.name.airLibLocation = 6) Cesta ke knihovn\u011b AIR (airglobal.swc)
config.description.airLibLocation = Um\u00edst\u011bn\u00ed knihovny AIR s n\u00e1zvem "airglobal.swc". M\u016f\u017ee b\u00fdt pou\u017eitou zejm\u00e9na pro AS3 kompilaci.
config.name.showImportShapeInfo = Zobrazit informaci p\u0159ed importem tvar\u016f
config.description.showImportShapeInfo = Zobraz\u00ed informace o tom jak import tvar\u016f funguje po kliku na import tvar\u016f v menu.
config.name.pinnedItemsTagTreePaths = Cesty p\u0159ipnut\u00fdch polo\u017eek v stromu tag\u016f
config.description.pinnedItemsTagTreePaths = Cesty uzl\u016f v stromu tag\u016f, kter\u00e9 jsou p\u0159ipnuty.
config.name.pinnedItemsTagListPaths = Cesty p\u0159ipnut\u00fdch polo\u017eek v stromu seznamu tag\u016f
config.description.pinnedItemsTagListPaths = Cesty uzl\u016f v stromu seznamu tag\u016f, kter\u00e9 jsou p\u0159ipnuty.
config.name.flattenASPackages = Zplo\u0161tit bal\u00ed\u010dky ActionScriptu
config.description.flattenASPackages = Zobraz\u00ed bal\u00ed\u010dky jako jednu polo\u017eku pro bal\u00ed\u010dek m\u00edsto stromu bal\u00ed\u010dku.
config.name.gui.scale = UI faktor zv\u011bt\u0161en\u00ed
config.description.gui.scale = Zv\u011bt\u0161ovac\u00ed faktor grafick\u00e9ho rozhran\u00ed. Nastavte toto na 2.0 na retina displej\u00edch na Macu. Je vy\u017eadov\u00e1no tvrd\u00e9 ukon\u010den\u00ed aplikace (nejen restart po dotazu).
config.name.warning.video.vlc = Varovat p\u0159i chyb\u011bj\u00edc\u00edm VLC
config.description.warning.video.vlc = Zobrazovat varov\u00e1n\u00ed o vy\u017eadovan\u00e9m VLC media playeru p\u0159i otev\u00edr\u00e1n\u00ed SWF s DefineVideoStream tagy kdy\u017e je VLC nedostupn\u00fd.
config.name.playFrameSounds = P\u0159ehr\u00e1vat sn\u00edmkov\u00e9 zvuky
config.description.playFrameSounds = P\u0159ehr\u00e1vat zvuky p\u0159i zobrazov\u00e1n\u00ed sn\u00edmk\u016f.
config.name.fixAntialiasConflation = Roz\u0161\u00ed\u0159it plochu tvaru pro opravu konflace antialiasingu
config.description.fixAntialiasConflation = Oprav\u00ed konfla\u010dn\u00ed artefakty mezi soused\u00edc\u00edmi tvary zp\u016fsoben\u00e9 antialiasingem roz\u0161\u00ed\u0159en\u00edm obrysu tvaru o p\u016fl pixelu.
config.name.autoPlaySounds = Automaticky p\u0159ehr\u00e1vat zvuky
config.description.autoPlaySounds = Automaticky p\u0159ehr\u00e1vat zvuky (DefineSound) p\u0159i v\u00fdb\u011bru polo\u017eky ve stromu.
config.name.deobfuscateAs12RemoveInvalidNamesAssignments = AS1/2 deobfuskace: Odstranit deklarace prom\u011bnn\u00fdch s obfuskovan\u00fdmi n\u00e1zvy
config.description.deobfuscateAs12RemoveInvalidNamesAssignments = B\u011bhem deobfuskace AS1/2 odstranit deklarace prom\u011bnn\u00fdch, kter\u00e9 maj\u00ed nestandardn\u00ed n\u00e1zev. VAROV\u00c1N\u00cd: Toto m\u016f\u017ee po\u0161kodit SWF soubory, kter\u00e9 z\u00e1vis\u00ed na obfuskovan\u00fdch n\u00e1zvech.
config.name.gui.splitPanePlace.dividerLocationPercent = (Intern\u00ed) Pozice rozd\u011blova\u010de place
config.description.gui.splitPanePlace.dividerLocationPercent = 
config.name.gui.splitPaneTransform1.dividerLocationPercent = (Intern\u00ed) Pozice rozd\u011blova\u010de transformace1
config.description.gui.splitPaneTransform1.dividerLocationPercent = 
config.name.gui.splitPaneTransform2.dividerLocationPercent = (Intern\u00ed) Pozice rozd\u011blova\u010de transformace2
config.description.gui.splitPaneTransform2.dividerLocationPercent = 
config.name.gui.transform.lastExpandedCards = (Intern\u00ed) Posledn\u00ed rozbalen\u00e9 transforma\u010dn\u00ed karty
config.description.gui.transform.lastExpandedCards = 
config.name.doubleClickNodeToEdit = Dvojit\u00fd klik za\u010dne editaci
config.description.doubleClickNodeToEdit = Dvojit\u00e9 kliknut\u00ed na polo\u017eku ve stromu za\u010dne jej\u00ed editaci.
config.name.warningDeobfuscation = Varovat p\u0159i p\u0159ep\u00edn\u00e1n\u00ed deobfuskace
config.description.warningDeobfuscation = Zobrazovat varov\u00e1n\u00ed b\u011bhem zap\u00edn\u00e1n\u00ed/vyp\u00edn\u00e1n\u00ed deobfuskace.
config.name.warningRenameIdentifiers = Varovat p\u0159i p\u0159ep\u00edn\u00e1n\u00ed autop\u0159ejm\u00e9nov\u00e1n\u00ed identifik\u00e1tor\u016f
config.description.warningRenameIdentifiers = Zobrazovat varov\u00e1n\u00ed b\u011bhem zap\u00edn\u00e1n\u00ed featury automatick\u00e9ho p\u0159ejmenov\u00e1n\u00ed identifik\u00e1tor\u016f.
config.name.showImportMovieInfo = Zobrazit informaci p\u0159ed importem vide\u00ed
config.description.showImportMovieInfo = Zobraz\u00ed informace o tom jak import vide\u00ed funguje po kliku na import vide\u00ed v menu.
config.name.showImportSoundInfo = Zobrazit informaci p\u0159ed importem zvuk\u016f
config.description.showImportSoundInfo = Zobraz\u00ed informace o tom jak import zvuk\u016f funguje po kliku na import zvuk\u016f v menu.
config.name.svgRetainBounds = Zachovat hranice tvaru b\u011bhem exportu SVG
config.description.svgRetainBounds = B\u011bhem exportu SVG bude pozice tvaru x a y exportov\u00e1na p\u0159esn\u011b jako v SWF (tj. pozitivn\u00ed \u010di negativn\u00ed).
config.name.disableBitmapSmoothing = Zak\u00e1zat vyhlazov\u00e1n\u00ed bitmap
config.description.disableBitmapSmoothing = Zak\u00e1\u017ee vyhlazovan\u00e9 bitmapov\u00e9 v\u00fdpln\u011b b\u011bhem zobrazen\u00ed - zobraz\u00ed v\u0161e jako nevyhlazen\u00e9 (pixelovan\u00e9). Toto se neaplikuje na exportovan\u00e9 obr\u00e1zky.
config.name.pinnedItemsScrollPos = Pozice odskrolov\u00e1n\u00ed/kurzoru pro p\u0159ipnut\u00e9 polo\u017eky
config.description.pinnedItemsScrollPos = Pozice odskrolov\u00e1n\u00ed \u010di kurzoru p\u0159ipnut\u00fdch polo\u017eek.
config.name.maxRememberedScrollposItems = Maxim\u00e1ln\u00ed po\u010det zapamatovan\u00fdch pozic odskrolov\u00e1n\u00ed
config.description.maxRememberedScrollposItems = Maxim\u00e1ln\u00ed po\u010det zapamatovan\u00fdch pozic odskrolov\u00e1n\u00ed.
config.name.rememberScriptsScrollPos = Zapamatovat pozici odskrolov\u00e1n\u00ed/kurzoru u skript\u016f
config.description.rememberScriptsScrollPos = Pozice odskrolov\u00e1n\u00ed/kurzoru u skript\u016f je zachov\u00e1na p\u0159i p\u0159ep\u00edn\u00e1n\u00ed mezi polo\u017ekami a ukl\u00e1d\u00e1na pro p\u0159ipnut\u00e9 polo\u017eky.
config.name.rememberFoldersScrollPos = Zapamatovat pozici odskrolov\u00e1n\u00ed u slo\u017eek
config.description.rememberFoldersScrollPos = Pozice odskrolov\u00e1n\u00ed u slo\u017eek je zachov\u00e1na p\u0159i p\u0159ep\u00edn\u00e1n\u00ed mezi polo\u017ekami a ukl\u00e1d\u00e1na pro p\u0159ipnut\u00e9 polo\u017eky.
#after 18.3.6
config.name.warning.initializers.class = Varovat p\u0159i AS3 class editaci o inicializ\u00e1toru
config.description.warning.initializers.class = Zobrazovat varov\u00e1n\u00ed p\u0159i editaci AS3 class o inicializ\u00e1toru.
#after 18.4.1
config.name.maxCachedNum = Maxim\u00e1ln\u00ed po\u010det prvk\u016f v jedn\u00e9 cache
config.description.maxCachedNum = Maxim\u00e1ln\u00ed po\u010det cachovan\u00fdch prvk\u016f p\u0159ed t\u00edm, ne\u017e jsou star\u0161\u00ed prvky vymaz\u00e1ny. Ni\u017e\u0161\u00ed hodnota = m\u00e9n\u011b pam\u011bti, pomalej\u0161\u00ed aplikace. Vy\u0161\u0161\u00ed hodnota = v\u00edce pam\u011bti, rychlej\u0161\u00ed aplikace. Nastavte sem hodnotu 0 pro nekone\u010dn\u00e9 cachov\u00e1n\u00ed.
config.name.warning.cannotencrypt = Varovat kdy\u017e nelze ulo\u017eit za\u0161ifrovan\u00e9
config.description.warning.cannotencrypt = Zobrazit varov\u00e1n\u00ed kdy\u017e nelze ulo\u017eit SWF soubor kter\u00fd byl \u0161ifrov\u00e1n pomoc\u00ed HARMAN Air \u0161ifrov\u00e1n\u00ed.
#after 18.5.0
config.name.lastExportEnableEmbed = Posledn\u00ed nastaven\u00ed exportu vlo\u017een\u00fdch zdroj\u016f
config.description.lastExportEnableEmbed = Posledn\u00ed nastaven\u00ed exportov\u00e1n\u00ed vlo\u017een\u00fdch zdroj\u016f skrze [Embed] metadata.
config.name.lastFlaExportVersion = Posledn\u00ed verze FLA exportu
config.description.lastFlaExportVersion = Posledn\u00ed exportovan\u00e1 verze FLA.
config.name.lastFlaExportCompressed = Posledn\u00ed komprese FLA exportu
config.description.lastFlaExportCompressed = Posledn\u00ed exportovan\u00e1 komprese FLA.
#after 19.0.0
config.name.showImportSpriteInfo = Zobrazit informaci p\u0159ed importem sprit\u016f
config.description.showImportSpriteInfo = Zobraz\u00ed informace o tom jak import sprit\u016f funguje po kliku na import sprit\u016f v menu.
config.name.displayAs12PCodeDocsPanel = Zobrazit panel s dokumentac\u00ed v AS1/2 P-k\u00f3du
config.description.displayAs12PCodeDocsPanel = Zobraz\u00ed panel s dokumentac\u00ed akc\u00ed p\u0159i editaci a zobrazen\u00ed AS1/2 P-k\u00f3du.
config.name.gui.action.splitPane.docs.dividerLocationPercent = (Internal) AS 1/2 splitPanedocsdividerLocationPercent
config.description.action.avm2.splitPane.docs.dividerLocationPercent = AS 1/2 pozice rozd\u011blova\u010de dokumentace v procentech.
#after 19.1.2
config.name.rememberLastScreen = Pamatovat si naposledy pou\u017eitou obrazovku (p\u0159i v\u00edce monitorech)
config.description.rememberLastScreen = Pamatovat si naposledy pou\u017eitou obrazovku na konfigurac\u00edch s v\u00edce zobrazovac\u00edmi za\u0159\u00edzen\u00edmi (monitory).
config.name.lastMainWindowScreenIndex = Posledn\u00ed index obrazovky hlavn\u00edho okna
config.description.lastMainWindowScreenIndex = Posledn\u00ed index obrazovky hlavn\u00edho okna.
config.name.lastMainWindowScreenX = Posledn\u00ed X obrazovky hlavn\u00edho okna
config.description.lastMainWindowScreenX = Posledn\u00ed Xov\u00e1 sou\u0159adn\u00edce obrazovky hlavn\u00edho okna.
config.name.lastMainWindowScreenY = Posledn\u00ed Y obrazovky hlavn\u00edho okna
config.description.lastMainWindowScreenY = Posledn\u00ed Yov\u00e1 sou\u0159adn\u00edce obrazovky hlavn\u00edho okna.
config.name.lastMainWindowScreenWidth = Posledn\u00ed \u0161\u00ed\u0159ka obrazovky hlavn\u00edho okna
config.description.lastMainWindowScreenWidth = Posledn\u00ed \u0161\u00ed\u0159ka obrazovky hlavn\u00edho okna.
config.name.lastMainWindowScreenHeight = Posledn\u00ed v\u00fd\u0161ka obrazovky hlavn\u00edho okna
config.description.lastMainWindowScreenHeight = Posledn\u00ed v\u00fd\u0161ka obrazovky hlavn\u00edho okna.
config.name.displayAs12PCodePanel = Zobrazit AS1/2 panel s P-k\u00f3dem
config.description.displayAs12PCodePanel = Zobrazit panel s akcemi disassemblovan\u00e9ho P-k\u00f3du pro ActionScript 1 a 2.
config.name.displayAs3PCodePanel = Zobrazit AS3 panel s P-k\u00f3dem
config.description.displayAs3PCodePanel = Zobrazit panel s instrukcemi disassemblovan\u00e9ho P-k\u00f3du pro ActionScript 3.
config.name.flaExportUseMappedFontLayout = FLA export - pou\u017e\u00edvat namapovan\u00e9 rozvr\u017een\u00ed p\u00edsem
config.description.flaExportUseMappedFontLayout = B\u011bhem FLA exportu pou\u017e\u00edvat advance hodnoty z p\u0159i\u0159azen\u00e9ho zdrojov\u00e9ho p\u00edsma p\u0159i ur\u010dov\u00e1n\u00ed letterspacing hodnoty kdy\u017e vlastn\u00ed p\u00edsmo nem\u00e1 rozvr\u017een\u00ed.
#after 20.0.0
config.name.formatting.tab.size = Velikost tabel\u00e1toru
config.description.formatting.tab.size = Po\u010det mezer v tabel\u00e1toru.
config.name.boxBlurPixelsLimit = Limit pixel\u016f box blur filtru
config.description.boxBlurPixelsLimit = Maxim\u00e1ln\u00ed po\u010det pixel\u016f pro v\u00fdpo\u010det boxblur filtru. Aktu\u00e1ln\u00ed limit je toto \u010d\u00edslo kr\u00e1t 10000. Pokud je po\u010det pixel\u016f v\u011bt\u0161\u00ed, je zmen\u0161en blurX, blurY.
config.name.as3ExportNamesUseClassNamesOnly = Exportovan\u00e9 zdroje maj\u00ed n\u00e1zvy zalo\u017een\u00e9 pouze na n\u00e1zvech t\u0159\u00edd (AS3)
config.description.as3ExportNamesUseClassNamesOnly = Exportovan\u00e9 soubory zdroj\u016f (obr\u00e1zky, zvuky, ...) p\u0159eberou n\u00e1zvy z SymbolClass tagu - jejich p\u0159i\u0159azen\u00e9 t\u0159\u00eddy. Id charakteru nebude p\u0159id\u00e1no. Tak\u00e9 pokud bude v\u00edce t\u0159\u00edd p\u0159i\u0159azeno jednomu assetu, bude exportov\u00e1n v\u00edcekr\u00e1t. (Pro ActionScript 3 SWF soubory).
config.name.jnaTempDirectory = Do\u010dasn\u00fd adres\u00e1\u0159 JNA
config.description.jnaTempDirectory = Cesta k do\u010dasn\u00e9mu adres\u00e1\u0159i pro JNA DLL knihovny, atd. Toto mus\u00ed b\u00fdt nastaveno na cestu, kter\u00e1 neobsahuje \u017e\u00e1dn\u00e9 Unicode znaky. Pokud nen\u00ed nastaveno, je pou\u017eit TEMP adres\u00e1\u0159 aktu\u00e1ln\u00edho u\u017eivatele.
config.name.flaExportFixShapes = FLA export - opravovat tvary (pomal\u00e9)
config.description.flaExportFixShapes = Aplikovat proceduru rozd\u011blov\u00e1n\u00ed p\u0159ekr\u00fdvaj\u00edc\u00edch se hran pro opravu chyb\u011bj\u00edc\u00edch v\u00fdpln\u00ed n\u011bkter\u00fdch druh\u016f tvar\u016f. Tohle m\u016f\u017ee b\u00fdt velmi pomal\u00e9 na n\u011bkter\u00fdch slo\u017eit\u011bj\u0161\u00edch tvarech.
config.name.lastExportResampleWav = Posledn\u00ed nastaven\u00ed p\u0159evzorkov\u00e1n\u00ed wavu
config.description.lastExportResampleWav = Posledn\u00ed nastaven\u00ed p\u0159evzorkov\u00e1n\u00ed wavu na 44kHz.
config.name.previewResampleSound = P\u0159evzorkovat v n\u00e1hledech zvuku
config.description.previewResampleSound = P\u0159evzorkovat na 44kHz v n\u00e1hledech zvuku.
config.name.lastExportTransparentBackground = Posledn\u00ed nastaven\u00ed ignorov\u00e1n\u00ed barvy pozad\u00ed v exportu sn\u00edmk\u016f
config.description.lastExportTransparentBackground = Posledn\u00ed nastaven\u00ed ignorov\u00e1n\u00ed barvy pozad\u00ed v exportu sn\u00edmk\u016f pro pou\u017eit\u00ed pr\u016fhledn\u00e9ho pozad\u00ed.
config.name.warningAbcClean = Varovat p\u0159i Abc \u010di\u0161t\u011bn\u00ed
config.description.warningAbcClean = Zobrazovat varov\u00e1n\u00ed p\u0159ed proveden\u00edm Abc \u010di\u0161t\u011bn\u00ed.
config.name.warningAddFunction = Varovat p\u0159i p\u0159id\u00e1v\u00e1n\u00ed nov\u00e9 funkce v AS3 P-k\u00f3du
config.description.warningAddFunction = Zobrazovat varov\u00e1n\u00ed p\u0159ed p\u0159id\u00e1n\u00edm nov\u00e9 funkce v AS3 P-k\u00f3du. Tak\u00e9 to zobrazuje n\u011bjak\u00e9 informace o tom, jak tato akce funguje.
#after 21.0.2
config.name.linkAllClasses = P\u0159idat vazbu na v\u0161echny t\u0159\u00eddy (zvuky, p\u00edsma, obr\u00e1zky)
config.description.linkAllClasses = P\u0159id\u00e1 speci\u00e1ln\u00ed skript kter\u00fd odkazuje na v\u0161echny t\u0159\u00eddy (zvuky, p\u00edsma, obr\u00e1zky) v SWF souboru. Tohle je u\u017eite\u010dn\u00e9 pokud \u017e\u00e1dn\u00fd jin\u00fd skript na n\u011b neodkazuje, aby byly st\u00e1le sou\u010d\u00e1st\u00ed zkompilovan\u00e9ho souboru.

#after 21.1.0
config.name.recentColors = Posledn\u00ed barvy
config.description.recentColors = Posledn\u00ed barvy v dialogu barev.

#after 21.1.1
config.name.gui.splitPaneEasyVertical.dividerLocationPercent = (Internal) Jednoduch\u00e9 UI vertik\u00e1ln\u00ed um\u00edst\u011bn\u00ed rozd\u011blovn\u00edku
config.description.gui.splitPaneEasyVertical.dividerLocationPercent = 

config.name.gui.splitPaneEasyHorizontal.dividerLocationPercent = (Internal) Jednoduch\u00e9 UI horizont\u00e1ln\u00ed um\u00edst\u011bn\u00ed rozd\u011blovn\u00edku
config.description.gui.splitPaneEasyHorizontal.dividerLocationPercent = 

config.name.lastSessionEasySwf = Posledn\u00ed soubor ze sezen\u00ed Jednoduch\u00e9ho editoru
config.description.lastSessionEasySwf = Obsahuje vybran\u00e9 SWF z posledn\u00edho sezen\u00ed v Jednoduch\u00e9m editoru.

config.name.maxScriptLineLength = Maxim\u00e1ln\u00ed d\u00e9lka \u0159\u00e1dku skriptu
config.description.maxScriptLineLength = Maxim\u00e1ln\u00ed d\u00e9lka \u0159\u00e1dku v editoru skriptu p\u0159ed t\u00edm ne\u017e bude zalomen. 0 = neomezen\u011b. Na linuxu mohou b\u00fdt probl\u00e9my se zobrazen\u00edm velmi dlouh\u00e9 \u0159\u00e1dky, proto je to ve v\u00fdchoz\u00edm nastaven\u00ed omezen\u00e9.

#after 21.1.3
config.name.lastSolEditorDirectory = Posledn\u00ed slo\u017eka Sol editoru
config.description.lastSolEditorDirectory = Adres\u00e1\u0159, kde byl posledn\u00ed SOL soubor otev\u0159en/ulo\u017een.

#after 22.0.2
config.name.halfTransparentParentLayersEasy = Pr\u016fsvitn\u00e9 nad\u0159azen\u00e9 vrstvy v Simple editoru
config.description.halfTransparentParentLayersEasy = Zobraz\u00ed nad\u0159azen\u00e9 vrstvy p\u0159i editaci animovan\u00fdch podklip\u016f pr\u016fsvitn\u011b. Vypnuto = nezobraz\u00ed nad\u0159azen\u00e9 vrstvy v\u016fbec.

config.name.showRuler = Zobrazit prav\u00edtko
config.description.showRuler = Zobrazit prav\u00edtko p\u0159i zobrazov\u00e1n\u00ed / editaci SWF objekt\u016f.

config.name.snapToGuides = P\u0159ichytit k vod\u00edtk\u00e1m
config.description.snapToGuides = Povol\u00ed p\u0159ichycen\u00ed kurzoru k cel\u00fdm pixel\u016fm b\u011bhem p\u0159esouv\u00e1n\u00ed objekt\u016f.

config.name.snapToObjects = P\u0159ichytit k objekt\u016fm
config.description.snapToObjects = Povol\u00ed p\u0159ichycen\u00ed kurzoru k ostatn\u00edm objekt\u016fm b\u011bhem p\u0159esouv\u00e1n\u00ed objektu.

config.name.snapToPixels = P\u0159ichytit k pixel\u016fm
config.description.snapToPixels = Povol\u00ed p\u0159ichycen\u00ed kurzoru k cel\u00fdm pixel\u016fm b\u011bhem p\u0159esouv\u00e1n\u00ed objekt\u016f.

config.name.snapAlign = P\u0159ichytit k zarovn\u00e1n\u00ed
config.description.snapAlign = Povol\u00ed p\u0159ichycen\u00ed kurzoru na \u010d\u00e1ry zarovn\u00e1n\u00ed b\u011bhem p\u0159esouv\u00e1n\u00ed objekt\u016f.

config.name.showGuides = Zobrazit vod\u00edtka
config.description.showGuides = Toto vypn\u011bte pro skryt\u00ed vod\u00edtek.

config.name.lockGuides = Uzamknout vod\u00edtka
config.description.lockGuides = Zak\u00e1\u017ee posouv\u00e1n\u00ed vod\u00edtek, tak\u017ee z\u016fst\u00e1vaj\u00ed na sv\u00e9 pozici a nelze je p\u0159esouvat.

config.name.showGrid = Zobrazit m\u0159\u00ed\u017eku
config.description.showGrid = Zobraz\u00ed m\u0159\u00ed\u017eku na sc\u00e9n\u011b.

config.name.gridVerticalSpace = Vertik\u00e1ln\u00ed mezery m\u0159\u00ed\u017eky (px)
config.description.gridVerticalSpace = Vertik\u00e1ln\u00ed mezery mezi \u010darami m\u0159\u00ed\u017eky v pixelech.

config.name.gridHorizontalSpace = Horizont\u00e1ln\u00ed mezery m\u0159\u00ed\u017eky (px)
config.description.gridHorizontalSpace = Horizont\u00e1ln\u00ed mezery mezi \u010darami m\u0159\u00ed\u017eky v pixelech.

config.name.snapToGrid = P\u0159ichytit k m\u0159\u00ed\u017ece
config.description.snapToGrid = Povol\u00ed p\u0159ichycen\u00ed kurzoru k m\u0159\u00ed\u017ece b\u011bhem p\u0159esouv\u00e1n\u00ed objekt\u016f.

config.name.gridOverObjects = Zobrazit m\u0159\u00edzku p\u0159es objekty
config.description.gridOverObjects = Pokud je zobrazena m\u0159\u00ed\u017eka, je vid\u011bt p\u0159es objekty ns sc\u00e9n\u011b.

config.name.gridColor = Barva m\u0159\u00ed\u017eky
config.description.gridColor = Barva vykreslovan\u00e9 m\u0159\u00ed\u017eky.

config.name.guidesColor = Barva vod\u00edtek
config.description.guidesColor = Barva vykreslovan\u00fdch vod\u00edtek.

config.name.gridSnapAccuracy = P\u0159esnost p\u0159ichycen\u00ed m\u0159\u00ed\u017eky
config.description.gridSnapAccuracy = Jak daleko mus\u00ed b\u00fdt kurzor od m\u0159\u00ed\u017eky aby byl p\u0159ichycen.

config.name.guidesSnapAccuracy = P\u0159esnost p\u0159ichycen\u00ed k vod\u00edtk\u00e1m
config.description.guidesSnapAccuracy = Jak daleko mus\u00ed b\u00fdt kurzor od vod\u00edtka aby byl p\u0159ichycen.

config.name.snapAlignObjectHorizontalSpace = Horizont\u00e1ln\u00ed mezera v p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed objekt\u016f
config.description.snapAlignObjectHorizontalSpace = Horizont\u00e1ln\u00ed mezery mezi objekty b\u011bhem p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed.

config.name.snapAlignObjectVerticalSpace = Vertik\u00e1ln\u00ed mezera v p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed objekt\u016f
config.description.snapAlignObjectVerticalSpace = Vertik\u00e1ln\u00ed mezery mezi objekty b\u011bhem p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed.

config.name.snapAlignStageBorder = Okraj sc\u00e9ny pro p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed
config.description.snapAlignStageBorder = Mezera od okraje sc\u00e9ny b\u011bhem p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed.

config.name.snapAlignCenterAlignmentHorizontal = Horizont\u00e1ln\u00ed zarovn\u00e1n\u00ed st\u0159edu v p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed
config.description.snapAlignCenterAlignmentHorizontal = Povol\u00ed p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed st\u0159edu objektu horizont\u00e1ln\u011b.

config.name.snapAlignCenterAlignmentVertical = Vertik\u00e1ln\u00ed zarovn\u00e1n\u00ed st\u0159edu v p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed
config.description.snapAlignCenterAlignmentVertical = Povol\u00ed p\u0159ichycen\u00ed k zarovn\u00e1n\u00ed st\u0159edu objektu vertik\u00e1ln\u011b.

#after 23.0.1
config.name.warning.linkTypes = Varovat p\u0159i kliknut\u00ed na link na extern\u00ed soubor
config.description.warning.linkTypes = Zobrazovat varov\u00e1n\u00ed p\u0159i kliknut\u00ed na odkaz v editoru skript\u016f, kter\u00fd vede do jin\u00e9ho SWF souboru.

config.name.showCodeCompletionOnDot = Zobrazit dokon\u010dov\u00e1n\u00ed k\u00f3du po kl\u00e1vese te\u010dka(.)
config.description.showCodeCompletionOnDot = Automaticky zobraz\u00ed okno dokon\u010dov\u00e1n\u00ed k\u00f3du po stisku kl\u00e1vesy te\u010dka(.). P\u0159i zak\u00e1z\u00e1n\u00ed se d\u00e1 dokon\u010dov\u00e1n\u00ed k\u00f3du vyvolat pomoc\u00ed Ctrl+mezera.
