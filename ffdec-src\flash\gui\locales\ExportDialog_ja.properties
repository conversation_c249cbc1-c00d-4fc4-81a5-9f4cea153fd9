# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = \u56f3\u5f62
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 Canvas
shapes.swf = SWF
texts = \u30c6\u30ad\u30b9\u30c8
texts.plain = \u30d7\u30ec\u30fc\u30f3\u30c6\u30ad\u30b9\u30c8
texts.formatted = \u30d5\u30a9\u30fc\u30de\u30c3\u30c8\u3055\u308c\u305f\u30c6\u30ad\u30b9\u30c8
texts.svg = SVG
images = \u753b\u50cf
images.png_gif_jpeg=PNG / GIF / JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = \u52d5\u753b
movies.flv = FLV (\u97f3\u58f0\u306a\u3057)
sounds = \u97f3\u58f0
sounds.mp3_wav_flv=MP3 / WAV / FLV
sounds.flv = FLV (\u97f3\u58f0\u306e\u307f)
sounds.mp3_wav=MP3 / WAV
sounds.wav = WAV
scripts = \u30b9\u30af\u30ea\u30d7\u30c8
scripts.as = ActionScript
scripts.pcode = P-code
scripts.pcode_hex=16\u9032\u6570\u306eP-code
scripts.hex = 16\u9032\u6570
scripts.constants = \u5b9a\u6570
scripts.as_method_stubs=ActionScript \u30e1\u30bd\u30c3\u30c9 \u30b9\u30bf\u30d6
scripts.pcode_graphviz=P-code GraphViz
binaryData = \u30d0\u30a4\u30ca\u30ea\u30c7\u30fc\u30bf
binaryData.raw = Raw
dialog.title = \u30a8\u30af\u30b9\u30dd\u30fc\u30c8...
button.ok = OK
button.cancel = \u30ad\u30e3\u30f3\u30bb\u30eb
morphshapes = \u30e2\u30fc\u30d5\u56f3\u5f62
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 Canvas
morphshapes.swf = SWF
frames = \u30d5\u30ec\u30fc\u30e0
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 Canvas
frames.pdf = PDF
frames.bmp = BMP
frames.swf = SWF
sprites = \u30b9\u30d7\u30e9\u30a4\u30c8
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = HTML5 Canvas
sprites.pdf = PDF
sprites.bmp = BMP
sprites.swf = SWF
buttons = \u30dc\u30bf\u30f3
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
buttons.swf = SWF
fonts = \u30d5\u30a9\u30f3\u30c8
fonts.ttf = TTF
fonts.woff = WOFF
zoom = \u30ba\u30fc\u30e0
zoom.percent = %
zoom.invalid = \u30ba\u30fc\u30e0\u5024\u304c\u7121\u52b9\u3067\u3059
symbolclass = Symbol\u30af\u30e9\u30b9 \u30de\u30c3\u30d4\u30f3\u30b0
symbolclass.csv = CSV
