# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Alakzatok
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 V\u00e1szon
texts = Sz\u00f6vegek
texts.plain = Egyszer\u0171 sz\u00f6veg
texts.formatted = Form\u00e1zott sz\u00f6veg
texts.svg = SVG
images = K\u00e9pek
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Mozg\u00f3k\u00e9pek
movies.flv = FLV (Hang n\u00e9lk\u00fcl)
sounds = Hangok
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (Csak hang)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Szkriptek
scripts.as = ActionScript
scripts.pcode = P-code
scripts.pcode_hex=P-code \u00e9s Hexa
scripts.hex = Hexa
scripts.constants = Konstansok
binaryData = Binary data
binaryData.raw = Nyers
dialog.title = Export\u00e1l\u00e1s...
button.ok = OK
button.cancel = M\u00e9gse
morphshapes = Morph alakzatok
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 V\u00e1szon
frames = Keretek
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 V\u00e1szon
frames.pdf = PDF
frames.bmp = BMP
sprites = Szpr\u00e1jtok
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = HTML5 V\u00e1szon
sprites.pdf = PDF
sprites.bmp = BMP
buttons = Gombok
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
fonts = Bet\u0171t\u00edpusok
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Nagy\u00edt\u00e1s
zoom.percent = %
zoom.invalid = \u00c9rv\u00e9nytelen nagy\u00edt\u00e1si \u00e9rt\u00e9k.
symbolclass = Szimb\u00f3lum oszt\u00e1ly
symbolclass.csv = CSV
