# Copyright (C) 2023 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
title = ABC Gezgini
abc = ABC:
abc.info = %index% > %count%, v%major%.%minor%, %size%, \u00c7er\u00e7eve %frame%
abc.info.standalone = v%major%.%minor%, %size%
show.script = Komut dosyas\u0131n\u0131 ana pencerede g\u00f6ster
show.method = Y\u00f6ntemi ana pencerede g\u00f6ster
show.trait = <PERSON> pencerede \u00f6<PERSON>li\u011fi g\u00f6ster
show.class = Ana pencerede s\u0131n\u0131f\u0131 g\u00f6ster
copy.row = Sat\u0131r\u0131 panoya kopyala
copy.typeid = Kimlik t\u00fcr\u00fcn\u00fc panoya kopyala
copy.title = Ba\u015fl\u0131\u011f\u0131 panoya kopyala
copy.value = De\u011feri panoya kopyala
copy.rawstring = Ham dize de\u011ferini panoya kopyala
#after 20.1.0
usages = %item% kullan\u0131mlar\u0131
copy.path = Yolu panoya kopyala
copy.paths = Se\u00e7ilen yollar\u0131 panoya kopyala
copy.paths.all = T\u00fcm yollar\u0131 panoya kopyala
hilight.usage = Se\u00e7ili yolu vurgula
goto.path = Yola git
goto.path.label = Gidilecek yolu gir
button.clean = Temizle - kullan\u0131lmayan \u00f6\u011feleri kald\u0131r
