# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
checkbox.ignorecase = \u0406\u0433\u043d\u043e\u0440\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0433\u0456\u0441\u0442\u0440
checkbox.regexp = \u0420\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u0438\u0439 \u0432\u0438\u0440\u0430\u0437
button.ok = \u0413\u0430\u0440\u0430\u0437\u0434
button.cancel = \u0421\u043a\u0430\u0441\u0443\u0432\u0430\u0442\u0438
label.searchtext = \u0428\u0443\u043a\u0430\u0442\u0438 \u0442\u0435\u043a\u0441\u0442:
#dialog.title = \u041f\u043e\u0448\u0443\u043a \u043f\u043e ActionScript
dialog.title = \u041f\u043e\u0448\u0443\u043a \u0442\u0435\u043a\u0441\u0442\u0443
error = \u041f\u043e\u043c\u0438\u043b\u043a\u0430
error.invalidregexp = \u041d\u0435\u0432\u0456\u0440\u043d\u0438\u0439 \u0440\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u0438\u0439 \u0432\u0438\u0440\u0430\u0437
checkbox.searchText = \u0428\u0443\u043a\u0430\u0442\u0438 \u0432 \u0442\u0435\u043a\u0441\u0442\u0430\u0445
checkbox.searchAS = \u0428\u0443\u043a\u0430\u0442\u0438 \u0432 ActionScript
