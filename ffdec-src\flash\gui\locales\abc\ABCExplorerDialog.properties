# Copyright (C) 2023 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
title = ABC Explorer
abc = ABC:
abc.info = %index% of %count%, v%major%.%minor%, %size%, Frame %frame%
abc.info.standalone = v%major%.%minor%, %size%
show.script = Show script in main window
show.method = Show method in main window
show.trait = Show trait in main window
show.class = Show class in main window
copy.row = Copy row to clipboard
copy.typeid = Copy typeId to clipboard
copy.title = Copy title to clipboard
copy.value = Copy value to clipboard
copy.rawstring = Copy raw string value to clipboard
#after 20.1.0
usages = Usages of %item%
copy.path = Copy path to clipboard
copy.paths = Copy selected paths to clipboard
copy.paths.all = Copy all paths to clipboard
hilight.usage = Hilight selected path
goto.path = Go to path
goto.path.label = Enter path to navigate to
button.clean = Clean - remove unused items
