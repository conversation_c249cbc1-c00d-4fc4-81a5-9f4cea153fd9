# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Formas
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 Canvas
texts = Textos
texts.plain = Texto plano
texts.formatted = Texto formateado
texts.svg = SVG
images = Im\u00e1genes
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Pel\u00edculas
movies.flv = FLV (Sin audio)
sounds = Sonidos
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (Solo audio)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Scripts
scripts.as = ActionScript
scripts.pcode = P-code
scripts.pcode_hex=P-code con Hexadecimal
scripts.hex = Hexadecimal
scripts.constants = Constantes
binaryData = Datos binarios
binaryData.raw = Crudo
dialog.title = Exportar...
button.ok = OK
button.cancel = Cancelar
morphshapes = Morphshapes
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 Canvas
frames = Marcos
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 Canvas
frames.pdf = PDF
frames.bmp = BMP
sprites = Sprites
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = Canvas HTML5
sprites.pdf = PDF
sprites.bmp = BMP
buttons = Botones
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
fonts = Fonts
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Ampliar
zoom.percent = %
zoom.invalid = Valor inv\u00e1lido para ampliar.
symbolclass = Clase Symbol
symbolclass.csv = CSV
