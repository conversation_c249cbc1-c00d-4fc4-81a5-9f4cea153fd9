/*
 *  Copyright (C) 2010-2025 JPEXS
 * 
 *  This program is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 * 
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 * 
 *  You should have received a copy of the GNU General Public License
 *  along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.jpexs.decompiler.flash.gui.abc;

import java.awt.BorderLayout;
import javax.swing.JPanel;

/**
 * <AUTHOR>
 */
public class MethodTraitDetailPanel extends JPanel implements TraitDetail {

    public MethodCodePanel methodCodePanel;

    public ABCPanel abcPanel;

    private boolean active = false;

    public MethodTraitDetailPanel(ABCPanel abcPanel) {
        this.abcPanel = abcPanel;
        methodCodePanel = new MethodCodePanel(abcPanel.decompiledTextArea);
        setLayout(new BorderLayout());
        add(methodCodePanel, BorderLayout.CENTER);
    }

    @Override
    public boolean save() {
        return methodCodePanel.save();
    }

    @Override
    public void setEditMode(boolean val) {
        methodCodePanel.setEditMode(val);
    }

    @Override
    public void setActive(boolean val) {
        this.active = val;
    }
}
