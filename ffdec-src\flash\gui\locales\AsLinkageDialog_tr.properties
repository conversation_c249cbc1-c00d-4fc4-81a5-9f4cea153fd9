# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = AS ba\u011flant\u0131s\u0131
button.ok = Tamam
button.proceed = Devam et
button.cancel = \u0130ptal
identifier = ActionScript tan\u0131mlay\u0131c\u0131s\u0131:
classname = ActionScript2 s\u0131n\u0131f\u0131 (tam nitelikli):
class.parentname = \u00dcst s\u0131n\u0131f ad\u0131 (tam nitelikli):
error.alreadyExistsClass = Hata: Bu s\u0131n\u0131f zaten var
error.cannotRemoveIdentifierClassExists = Hata: Tan\u0131mlay\u0131c\u0131 kald\u0131r\u0131lam\u0131yor, s\u0131n\u0131f zaten var
linkage.notfound.exportAssets.where = Ba\u011flant\u0131 verilerinin depolanaca\u011f\u0131 yer:
linkage.notfound.exportAssets.where.existing = Mevcut ExportAssets etiketi
linkage.notfound.exportAssets.where.new = Yeni ExportAssets etiketi
