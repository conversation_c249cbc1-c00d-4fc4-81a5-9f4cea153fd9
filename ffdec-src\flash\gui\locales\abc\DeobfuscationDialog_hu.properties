# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = \u00d6sszes oszt\u00e1ly feldolgoz\u00e1sa
dialog.title = PCode deobfuszk\u00e1l\u00e1s
deobfuscation.level = K\u00f3d deobfuszk\u00e1l\u00e1si szint:
deobfuscation.removedeadcode = Halott k\u00f3d elt\u00e1vol\u00edt\u00e1sa
deobfuscation.removetraps = Csapd\u00e1k elt\u00e1vol\u00edt\u00e1sa
deobfuscation.restorecontrolflow = Vez\u00e9rl\u00e9si-folyam helyre\u00e1ll\u00edt\u00e1s
button.ok = OK
button.cancel = M\u00e9gse
