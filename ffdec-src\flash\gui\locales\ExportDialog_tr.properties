# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
titleFormat = %title%:
shapes = \u015eekiller
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 Tuval
shapes.swf = SWF
texts = Metinler
texts.plain = D\u00fcz metin
texts.formatted = Bi\u00e7imlendirilmi\u015f metin
texts.svg = SVG
images = G\u00f6r\u00fcnt\u00fcler
images.png_gif_jpeg = PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Filmler
movies.flv = FLV (Ses yok)
sounds = Sesler
sounds.mp3_wav_flv = MP3/WAV/FLV
sounds.flv = FLV (Sadece ses)
sounds.mp3_wav = MP3/WAV
sounds.wav = WAV
scripts = Komutlar
scripts.as = ActionScript
scripts.pcode = P kodu
scripts.pcode_hex = Onalt\u0131l\u0131 P kodu
scripts.hex = Onalt\u0131l\u0131
scripts.constants = Sabitler
scripts.as_method_stubs = ActionScript y\u00f6ntem taslaklar\u0131
scripts.pcode_graphviz = P kodu GraphViz
binaryData = \u0130kili veri
binaryData.raw = Ham
dialog.title = D\u0131\u015fa aktar...
button.ok = OK
button.cancel = \u0130ptal
morphshapes = Morf \u015eekilleri
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 Tuval
morphshapes.swf = SWF
morphshapes.bmp_start_end = BMP (ba\u015flang\u0131\u00e7, biti\u015f)
morphshapes.png_start_end = PNG (ba\u015flang\u0131\u00e7, biti\u015f)
morphshapes.svg_start_end = SVG (ba\u015flang\u0131\u00e7, biti\u015f)
frames = \u00c7er\u00e7eveler
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 Tuval
frames.pdf = PDF
frames.bmp = BMP
frames.swf = SWF
sprites = Hareketli Grafikler
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = HTML5 Tuval
sprites.pdf = PDF
sprites.bmp = BMP
sprites.swf = SWF
buttons = D\u00fc\u011fmeler
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
buttons.swf = SWF
fonts = Yaz\u0131 Tipleri
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Yak\u0131nla\u015ft\u0131r
zoom.percent = %
zoom.invalid = Ge\u00e7ersiz yak\u0131nla\u015ft\u0131rma de\u011feri.
symbolclass = Sembol-S\u0131n\u0131f e\u015fle\u015ftirmesi
symbolclass.csv = CSV
#after 18.0.0
images.png_gif_jpeg_alpha = PNG/GIF/JPEG+alfa
#after 18.5.0
fonts4 = DefineFont4
fonts4.cff = CFF
embed = G\u00f6m\u00fcl\u00fc varl\u0131klar\u0131 [Embed] yoluyla d\u0131\u015fa aktar\u0131n
#after 20.1.0
resampleWav = Wav'\u0131 44kHz'e yeniden \u00f6rnekle
transparentFrameBackground = Arka plan rengini yoksay (saydam yap)
