const fs = require('fs');
const path = require('path');
const customAMF3 = require('./amf3');

/**
 * 直接处理AMF3数据而不通过JSON中转
 */
function processAMF3Directly() {
  try {
    // 读取原始AMF3文件
    const filePath = path.join(__dirname, 'data.bin');
    const fileContent = fs.readFileSync(filePath);
    console.log('AMF3文件读取成功，文件大小:', fileContent.length, '字节');

    // 添加调试信息
    console.log('前10个字节(十六进制):', Array.from(fileContent.slice(0, 10))
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join(' '));

    // 反序列化为JavaScript对象
    console.log('开始反序列化AMF3数据...');
    const decodedData = customAMF3.decode(fileContent);
    console.log('AMF3数据反序列化成功');

    // 输出属性值
    // console.log(decodedData.p1.level.value)
    // console.log(decodedData.p1.bag.suppliesBag[0].times.value)
    // console.log(decodedData.logNameSave)
    // // 等级改成99（100-1）
    // decodedData.p1.level.value.num = 100
    // decodedData.p1.level.value.random = 1
    // // 第一个药水数量改成88
    // decodedData.p1.bag.suppliesBag[0].times.value.num = 89
    // decodedData.p1.bag.suppliesBag[0].times.value.random = 1

    // 暂时注释掉字段重命名，测试不重命名的情况
    // const fieldMappings = {
    //   'JiHua': '_JiHua',
    //   'JiHua2': '_JiHua2',
    //   'chengjiu': '_chengjiu',
    //   'sArr2': '_sArr2',
    //   'RenWu': '_RenWu',
    //   'logNameSave': '_logNameSave',
    //   // 添加更多需要重命名的字段
    // };

    // // 批量重命名字段
    // function renameFields(obj, mappings) {
    //   for (const [oldName, newName] of Object.entries(mappings)) {
    //     if (obj[oldName] !== undefined) {
    //       obj[newName] = obj[oldName];
    //       delete obj[oldName];
    //       console.log(`已将字段 ${oldName} 重命名为 ${newName}`);
    //     }
    //   }
    // }

    // // 使用函数重命名字段
    // renameFields(decodedData, fieldMappings);

    // 打印所有顶级字段名
    console.log('对象的所有字段名:');
    const fieldNames = Object.keys(decodedData);
    console.log(fieldNames);
    console.log(`共有 ${fieldNames.length} 个顶级字段`);

    // 重新序列化为AMF3
    console.log('开始重新序列化为AMF3...');
    const encodedData = customAMF3.encode(decodedData);
    console.log('对象重新序列化为AMF3成功');

    // 生成带时间戳的文件名
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');
    const second = String(now.getSeconds()).padStart(2, '0');
    const timeString = `${year}年${month}月${day}日${hour}时${minute}分${second}秒`;
    const fileName = `output - ${timeString}.bin`;

    // 保存到新文件
    // 确保输出目录存在
    const outputDir = path.join(__dirname, 'output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    const outputPath = path.join(outputDir, fileName);
    fs.writeFileSync(outputPath, encodedData);
    console.log(`AMF3数据已保存到 ${outputPath}`);
    console.log(`文件大小: ${encodedData.length} 字节`);

    // 比较原始文件和新文件的大小
    const sizeDiff = encodedData.length - fileContent.length;
    const percentChange = ((sizeDiff / fileContent.length) * 100).toFixed(2);
    console.log(`文件大小变化: ${sizeDiff > 0 ? '+' : ''}${sizeDiff} 字节 (${percentChange}%)`);

    return encodedData;
  } catch (error) {
    console.error('处理过程中出错:', error.message);
    console.error('错误堆栈:', error.stack);
    throw error;
  }
}

// 执行处理
processAMF3Directly();