# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = AS3\u7c7b\u94fe\u63a5
button.ok = \u786e\u5b9a
button.proceed = \u7ee7\u7eed
button.cancel = \u53d6\u6d88
classname = \u5b8c\u5168\u9650\u5b9a\u7c7b\u540d\u79f0:
error.multipleClasses = \u9519\u8bef:\u8fd9\u4e2a\u5b57\u7b26\u5df2\u88ab\u5206\u914d\u7ed9\u4e86\u591a\u4e2a\u7c7b,\u65e0\u6cd5\u901a\u8fc7\u6b64\u5de5\u5177\u91cd\u65b0\u547d\u540d.\u5c3d\u7ba1\u5982\u6b64,\u60a8\u4ecd\u53ef\u4ee5\u81ea\u884c\u624b\u52a8\u4fee\u6539\u7b26\u53f7\u7c7b(SymbolClass)\u6807\u7b7e
error.alreadyAssignedClass = \u9519\u8bef:\u8fd9\u4e2a\u7c7b\u5df2\u7ecf\u88ab\u5206\u914d\u7ed9\u4e86\u4e0d\u540c\u7684\u5b57\u7b26
error.needToModify = \u5c06\u7c7b\u540d\u4fee\u6539\u4e3a\u65b0\u540d\u79f0
class.found = \u5df2\u627e\u5230\u5177\u6709\u8be5\u540d\u79f0\u7684\u73b0\u6709\u7c7b
class.notfound = \u5177\u6709\u8be5\u540d\u79f0\u7684\u7c7b\u5c1a\u4e0d\u5b58\u5728
symbolClassAppropriate = \u5c06\u4fee\u6539\u6216\u521b\u5efa\u6700\u5408\u9002\u5e27/\u6846\u67b6(frame)\u7684\u7b26\u53f7\u7c7b(SymbolClass)\u6807\u7b7e
class.notfound.createAsk = \u60a8\u60f3\u8981\u521b\u5efa\u8fd9\u4e2a\u7c7b\u5417
class.notfound.create = \u662f\u7684,\u521b\u5efa\u7c7b
class.notfound.create.parentType = \u7236\u7c7b\u540d\u79f0(\u5b8c\u5168\u9650\u5b9a\u7684)
class.notfound.create.abc.where = \u5728\u54ea\u91cc\u521b\u5efa\u5b57\u8282\u7801:
class.notfound.create.abc.where.existing = \u73b0\u6709\u7684DoABC\u6807\u7b7e
class.notfound.create.abc.where.new = \u65b0\u7684ABC\u6807\u7b7e
class.notfound.onlySetClassName = \u4e0d,\u4ec5\u5206\u914d\u7c7b\u540d\u79f0
class.notfound.onlySetClassName.symbolClass.where = \u94fe\u63a5\u6570\u636e\u7684\u5b58\u50a8\u4f4d\u7f6e:
class.notfound.onlySetClassName.symbolClass.where.existing = \u73b0\u6709\u7684\u7b26\u53f7\u7c7b\u6807\u7b7e
class.notfound.onlySetClassName.symbolClass.where.new = \u65b0\u7684\u7b26\u53f7\u7c7b\u6807\u7b7e
