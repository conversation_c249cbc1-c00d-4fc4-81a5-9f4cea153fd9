# Copyright (C) 2023 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
title = ABC-verkenner
abc = ABC:
abc.info = %index% of %count%, v%major%.%minor%, %size%, Frame %frame%
abc.info.standalone = v%major%.%minor%, %size%
show.script = Toon script in hoofdvenster
show.method = Toon methode in hoofdvenster
show.trait = Toon eigenschap in hoofdvenster
show.class = Toon klasse in hoofdvenster
copy.row = Kopieer de rij naar het klembord
copy.typeid = Kopieer het typeId naar het klembord
copy.title = Kopieer de titel naar het klembord
copy.value = <PERSON><PERSON><PERSON> de waarde naar het klembord
copy.rawstring = Kopieer de onbewerkte tekenreekswaarde naar het klembord
