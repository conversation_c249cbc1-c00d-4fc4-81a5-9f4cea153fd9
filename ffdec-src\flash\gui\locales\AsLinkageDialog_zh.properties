# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = AS\u94fe\u63a5
button.ok = \u786e\u5b9a
button.proceed = \u7ee7\u7eed
button.cancel = \u53d6\u6d88
identifier = ActionScript\u6807\u8bc6\u7b26
classname = ActionScript2\u7c7b(\u5b8c\u5168\u9650\u5b9a\u7684)
class.parentname = \u7236\u7c7b\u540d\u79f0(\u5b8c\u5168\u9650\u5b9a\u7684)
error.alreadyExistsClass = \u9519\u8bef:\u8be5\u7c7b\u5df2\u5b58\u5728
error.cannotRemoveIdentifierClassExists = \u9519\u8bef:\u65e0\u6cd5\u79fb\u9664\u6807\u8bc6\u7b26,\u7c7b\u5df2\u5b58\u5728
linkage.notfound.exportAssets.where = \u94fe\u63a5\u6570\u636e\u7684\u5b58\u50a8\u4f4d\u7f6e:
linkage.notfound.exportAssets.where.existing = \u73b0\u6709\u7684\u5bfc\u51fa\u7d20\u6750(ExportAssets)\u6807\u7b7e
linkage.notfound.exportAssets.where.new = \u65b0\u7684\u5bfc\u51fa\u7d20\u6750(ExportAssets)\u6807\u7b7e
