# Copyright (C) 2023 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
title = Explorador ABC
abc = ABC:
abc.info = %index% de %count%, v%major%.%minor%, %size%, Quadro %frame%
abc.info.standalone = v%major%.%minor%, %size%
show.script = Mostrar script na janela principal
show.method = Mostrar m\u00e9todo na janela principal
show.trait = Mostrar tra\u00e7o na janela principal
show.class = Mostrar classe na janela principal
copy.row = Copiar linha para a \u00e1rea de transfer\u00eancia
copy.typeid = Copie typeId para a \u00e1rea de transfer\u00eancia
copy.title = Copiar t\u00edtulo para a \u00e1rea de transfer\u00eancia
copy.value = Copie valor para a \u00e1rea de transfer\u00eancia
copy.rawstring = Copie o valor bruto da string para a \u00e1rea de transfer\u00eancia
