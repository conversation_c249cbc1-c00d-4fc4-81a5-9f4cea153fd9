/*
 *  Copyright (C) 2010-2025 JPEXS
 * 
 *  This program is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 * 
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 * 
 *  You should have received a copy of the GNU General Public License
 *  along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.jpexs.decompiler.flash.gui;

/**
 * <AUTHOR>
 */
public enum TreeNodeType {

    UNKNOWN,
    FLASH,
    FONT,
    TEXT,
    IMAGE,
    SHAPE,
    MORPH_SHAPE,
    SPRITE,
    BUTTON,
    BUTTON_RECORD,
    AS,
    AS_CLASS,
    AS_INTERFACE,
    AS_FRAME,
    AS_FUNCTION,
    AS_VAR,
    AS_CONST,
    AS_BUTTON,
    AS_CLIP,
    AS_INIT,
    PACKAGE,
    FRAME,
    SCENE,
    SHOW_FRAME,
    MOVIE,
    SOUND,
    BINARY_DATA,
    OTHER_TAG,
    FOLDER,
    FOLDER_OPEN,
    BUNDLE_ZIP,
    BUNDLE_SWC,
    BUNDLE_BINARY,
    BUNDLE_IGGY,
    HEADER,
    SET_BACKGROUNDCOLOR,
    FILE_ATTRIBUTES,
    METADATA,
    PLACE_OBJECT,
    REMOVE_OBJECT,
    SCALING_GRID,
    END,
    ERROR,
    ABC,
    COOKIE
}
