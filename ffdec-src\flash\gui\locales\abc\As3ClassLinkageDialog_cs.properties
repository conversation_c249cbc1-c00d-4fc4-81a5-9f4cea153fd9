# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = Vazba na AS3 t\u0159\u00eddu
button.ok = OK
button.proceed = Pokra\u010dovat
button.cancel = Storno
classname = Pln\u011b kvalifikovan\u00fd n\u00e1zev t\u0159\u00eddy:
error.multipleClasses = Chyba: Tento charakter m\u00e1 ji\u017e p\u0159i\u0159azeno v\u00edce jak jednu t\u0159\u00eddu, nelze ho p\u0159ejmenovat t\u00edmto n\u00e1strojem. Ka\u017edopadn\u011b m\u016f\u017eete st\u00e1le upravit SymbolClass tag ru\u010dn\u011b.
error.alreadyAssignedClass = Chyba: Tato t\u0159\u00edda je ji\u017e p\u0159i\u0159azena jin\u00e9mu charakteru
error.needToModify = Upravte n\u00e1zev t\u0159\u00eddy na n\u011bjak\u00fd nov\u00fd n\u00e1zev.
class.found = Nalezena existuj\u00edc\u00ed t\u0159\u00edda s t\u00edmto n\u00e1zvem.
class.notfound = T\u0159\u00edda s t\u00edmto n\u00e1zvem zat\u00edm neexistuje.
symbolClassAppropriate = Bude vytvo\u0159en nebo upraven SymbolClass tag v nejbli\u017e\u0161\u00edm vhodn\u00e9m framu.
class.notfound.createAsk = Chcete tuto t\u0159\u00eddu vytvo\u0159it?
class.notfound.create = Ano, vytvo\u0159it t\u0159\u00eddu
class.notfound.create.parentType = N\u00e1zev rodi\u010dovsk\u00e9 t\u0159\u00eddy (pln\u011b kvalifikovan\u00fd):
class.notfound.create.abc.where = Kam um\u00edstit bajt k\u00f3d:
class.notfound.create.abc.where.existing = Existuj\u00edc\u00ed DoABC tag
class.notfound.create.abc.where.new = Nov\u00fd DoABC tag
class.notfound.onlySetClassName = Ne, jen p\u0159i\u0159adit n\u00e1zev t\u0159\u00eddy
class.notfound.onlySetClassName.symbolClass.where = Kam um\u00edstit data o vazb\u011b:
class.notfound.onlySetClassName.symbolClass.where.existing = Existuj\u00edc\u00ed SymbolClass tag
class.notfound.onlySetClassName.symbolClass.where.new = Nov\u00fd SymbolClass tag