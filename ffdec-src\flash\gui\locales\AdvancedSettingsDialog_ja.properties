# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = \u8a73\u7d30\u8a2d\u5b9a
advancedSettings.restartConfirmation = \u4e00\u90e8\u306e\u5909\u66f4\u3092\u6709\u52b9\u306b\u3059\u308b\u306b\u306f\u3001\u30d7\u30ed\u30b0\u30e9\u30e0\u3092\u518d\u8d77\u52d5\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059\u3002\u4eca\u3059\u3050\u518d\u8d77\u52d5\u3057\u307e\u3059\u304b\uff1f
advancedSettings.columns.name = \u540d\u524d
advancedSettings.columns.value = \u5024
advancedSettings.columns.description = \u8aac\u660e
default = \u30c7\u30d5\u30a9\u30eb\u30c8
config.group.name.export = \u30a8\u30af\u30b9\u30dd\u30fc\u30c8
config.group.description.export = \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u306e\u8a2d\u5b9a
config.group.name.script = \u30b9\u30af\u30ea\u30d7\u30c8
config.group.description.script = ActionScript \u306e\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u95a2\u9023
config.group.name.update = \u30a2\u30c3\u30d7\u30c7\u30fc\u30c8
config.group.description.update = \u66f4\u65b0\u306e\u78ba\u8a8d
config.group.name.format = \u66f8\u5f0f
config.group.description.format = ActionScript \u30b3\u30fc\u30c9\u306e\u66f8\u5f0f
config.group.name.limit = \u5236\u9650
config.group.description.limit = \u96e3\u8aad\u5316\u3055\u308c\u305f\u30b3\u30fc\u30c9\u306e\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u5236\u9650\u306a\u3069
config.group.name.ui = \u30a4\u30f3\u30bf\u30fc\u30d5\u30a7\u30fc\u30b9
config.group.description.ui = \u30e6\u30fc\u30b6\u30fc\u30a4\u30f3\u30bf\u30fc\u30d5\u30a7\u30fc\u30b9\u306e\u8a2d\u5b9a
config.group.name.debug = \u30c7\u30d0\u30c3\u30b0
config.group.description.debug = \u30c7\u30d0\u30c3\u30b0\u8a2d\u5b9a
config.group.name.display = \u8868\u793a
config.group.description.display = Flash \u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u306e\u8868\u793a\u306a\u3069
config.group.name.decompilation = \u9006\u30b3\u30f3\u30d1\u30a4\u30eb
config.group.description.decompilation = \u30b0\u30ed\u30fc\u30d0\u30eb\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u95a2\u9023\u6a5f\u80fd
config.group.name.other = \u305d\u306e\u4ed6
config.group.description.other = \u305d\u306e\u4ed6\u306e\u672a\u5206\u985e\u306e\u8a2d\u5b9a
config.name.openMultipleFiles = \u8907\u6570\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u958b\u304f
config.description.openMultipleFiles = 1\u3064\u306e\u30a6\u30a3\u30f3\u30c9\u30a6\u3067\u3001\u540c\u6642\u306b\u8907\u6570\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u958b\u304f\u3053\u3068\u304c\u3067\u304d\u307e\u3059
config.name.decompile = ActionScript \u30bd\u30fc\u30b9\u3092\u8868\u793a\u3059\u308b
config.description.decompile = \u3053\u308c\u3092\u7121\u52b9\u306b\u3059\u308b\u3068\u3001AS \u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3092\u7121\u52b9\u306b\u3057\u3066\u3001P-code \u306e\u307f\u3092\u8868\u793a\u3057\u307e\u3059
config.name.dumpView = \u30c0\u30f3\u30d7\u3092\u898b\u308b
config.description.dumpView = \u751f\u306e16\u9032\u30c0\u30f3\u30d7\u3092\u898b\u307e\u3059
config.name.useHexColorFormat = 16\u9032\u30ab\u30e9\u30fc\u5f62\u5f0f
config.description.useHexColorFormat = \u8272\u309216\u9032\u6570\u3067\u8868\u793a\u3057\u307e\u3059
config.name.parallelSpeedUp = \u4e26\u5217\u51e6\u7406\u306b\u3088\u308b\u9ad8\u901f\u5316
config.description.parallelSpeedUp = \u4e26\u5217\u51e6\u7406\u306b\u3088\u308a\u3001\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3092\u9ad8\u901f\u5316\u3067\u304d\u307e\u3059
config.name.parallelSpeedUpThreadCount = \u30b9\u30ec\u30c3\u30c9\u6570 (0 = auto)
config.description.parallelSpeedUpThreadCount = \u4e26\u5217\u51e6\u7406\u306b\u3088\u308b\u9ad8\u901f\u5316\u306e\u305f\u3081\u306e\u30b9\u30ec\u30c3\u30c9\u6570. 0 = processor count - 1.
config.name.autoDeobfuscate = \u96e3\u8aad\u5316\u306e\u81ea\u52d5\u89e3\u9664
config.description.autoDeobfuscate = ActionScript \u3092\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3059\u308b\u524d\u306b\u3001\u3059\u3079\u3066\u306e\u30d5\u30a1\u30a4\u30eb\u306e\u96e3\u8aad\u5316\u89e3\u9664\u3092\u884c\u3044\u307e\u3059
config.name.cacheOnDisk = \u30b9\u30c8\u30ec\u30fc\u30b8\u306b\u30ad\u30e3\u30c3\u30b7\u30e5\u3059\u308b
config.description.cacheOnDisk = \u3059\u3067\u306b\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3057\u305f\u90e8\u5206\u3092\u3001\u30e1\u30e2\u30ea\u3067\u306f\u306a\u304f\u30b9\u30c8\u30ec\u30fc\u30b8\u306b\u30ad\u30e3\u30c3\u30b7\u30e5\u3057\u307e\u3059
config.name.internalFlashViewer = \u72ec\u81ea\u306e Flash \u30d3\u30e5\u30fc\u30a2\u3092\u4f7f\u7528\u3059\u308b
config.description.internalFlashViewer = Flash \u30d1\u30fc\u30c4\u306e\u8868\u793a\u306b\u3001\u6a19\u6e96\u306e Flash Player \u3067\u306f\u306a\u304f\u3001JPEXS Flash Viewer \u3092\u4f7f\u7528\u3057\u307e\u3059
config.name.gotoMainClassOnStartup = \u958b\u59cb\u6642\u306b\u30e1\u30a4\u30f3\u30af\u30e9\u30b9\u3078\u79fb\u52d5\u3059\u308b (AS3)
config.description.gotoMainClassOnStartup = SWF \u3092\u958b\u304f\u3068\u304d\u306b\u3001AS3 \u30d5\u30a1\u30a4\u30eb\u306e\u30c9\u30ad\u30e5\u30e1\u30f3\u30c8\u30af\u30e9\u30b9\u3078\u79fb\u52d5\u3057\u307e\u3059
config.name.autoRenameIdentifiers = \u7121\u52b9\u306a\u8b58\u5225\u5b50\u306e\u81ea\u52d5\u30ea\u30cd\u30fc\u30e0
config.description.autoRenameIdentifiers = SWF \u306e\u8aad\u307f\u8fbc\u307f\u6642\u306b\u3001\u7121\u52b9\u306a\u8b58\u5225\u5b50\u306e\u540d\u524d\u3092\u81ea\u52d5\u7684\u306b\u5909\u66f4\u3057\u307e\u3059
config.name.offeredAssociation = \uff08\u5185\u90e8\uff09SWF \u30d5\u30a1\u30a4\u30eb\u3068\u306e\u95a2\u9023\u4ed8\u3051\u304c\u8868\u793a\u6e08\u307f
config.description.offeredAssociation = \u30d5\u30a1\u30a4\u30eb\u306e\u95a2\u9023\u4ed8\u3051\u306b\u95a2\u3059\u308b\u30c0\u30a4\u30a2\u30ed\u30b0\u304c\u3059\u3067\u306b\u8868\u793a\u3055\u308c\u307e\u3057\u305f
config.name.decimalAddress = 10\u9032\u6570\u306e\u30a2\u30c9\u30ec\u30b9\u3092\u4f7f\u7528\u3059\u308b
config.description.decimalAddress = 16\u9032\u6570\u306e\u4ee3\u308f\u308a\u306b\u300110\u9032\u6570\u306e\u30a2\u30c9\u30ec\u30b9\u3092\u4f7f\u7528\u3057\u307e\u3059
config.name.showAllAddresses = \u3059\u3079\u3066\u306e\u30a2\u30c9\u30ec\u30b9\u3092\u8868\u793a\u3059\u308b
config.description.showAllAddresses = \u3059\u3079\u3066\u306e ActionScript \u306e\u547d\u4ee4\u306e\u30a2\u30c9\u30ec\u30b9\u3092\u8868\u793a\u3057\u307e\u3059
config.name.useFrameCache = \u30d5\u30ec\u30fc\u30e0\u30ad\u30e3\u30c3\u30b7\u30e5\u3092\u4f7f\u7528\u3059\u308b
config.description.useFrameCache = \u518d\u5ea6\u30ec\u30f3\u30c0\u30ea\u30f3\u30b0\u3059\u308b\u524d\u306b\u3001\u30d5\u30ec\u30fc\u30e0\u3092\u30ad\u30e3\u30c3\u30b7\u30e5\u3057\u307e\u3059
config.name.useRibbonInterface = \u30ea\u30dc\u30f3\u30a4\u30f3\u30bf\u30fc\u30d5\u30a7\u30fc\u30b9
config.description.useRibbonInterface = \u30ea\u30dc\u30f3\u30e1\u30cb\u30e5\u30fc\u306a\u3057\u3067\u30af\u30e9\u30b7\u30c3\u30af\u30a4\u30f3\u30bf\u30fc\u30d5\u30a7\u30fc\u30b9\u3092\u4f7f\u7528\u3059\u308b\u5834\u5408\u306f\u3001\u30c1\u30a7\u30c3\u30af\u3092\u5916\u3057\u307e\u3059
config.name.openFolderAfterFlaExport = FLA \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u5f8c\u306b\u30d5\u30a9\u30eb\u30c0\u3092\u958b\u304f
config.description.openFolderAfterFlaExport = FLA \u30d5\u30a1\u30a4\u30eb\u306e\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u5f8c\u306b\u3001\u51fa\u529b\u5148\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u3092\u958b\u304d\u307e\u3059
config.name.useDetailedLogging = FFDec \u306e\u8a73\u7d30\u306a\u30ed\u30b0
config.description.useDetailedLogging = FFDec \u306e\u30c7\u30d0\u30c3\u30b0\u306e\u305f\u3081\u306b\u3001\u8a73\u7d30\u306a\u30a8\u30e9\u30fc\u30e1\u30c3\u30bb\u30fc\u30b8\u3068\u60c5\u5831\u3092\u30ed\u30b0\u306b\u8a18\u9332\u3057\u307e\u3059
config.name._debugMode=FFDec \u306e\u30c7\u30d0\u30c3\u30b0\u30e2\u30fc\u30c9
config.description._debugMode=FFDec \u3092\u30c7\u30d0\u30c3\u30b0\u3059\u308b\u305f\u3081\u306e\u30e2\u30fc\u30c9\u3067\u3059\u3002\u30c7\u30d0\u30c3\u30b0\u30e1\u30cb\u30e5\u30fc\u3092\u30aa\u30f3\u306b\u3057\u307e\u3059\u3002\u3053\u308c\u306f\u30c7\u30d0\u30c3\u30ac\u306e\u6a5f\u80fd\u3068\u306f\u4f55\u306e\u95a2\u4fc2\u3082\u3042\u308a\u307e\u305b\u3093
config.name.resolveConstants = AS1/2 \u306e P-code \u306e\u5b9a\u6570\u3092\u89e3\u6c7a\u3059\u308b
config.description.resolveConstants = \u3053\u308c\u3092\u30aa\u30d5\u306b\u3059\u308b\u3068\u3001P-code \u30a6\u30a3\u30f3\u30c9\u30a6\u306b\u306f\u5b9f\u969b\u306e\u5024\u3067\u306f\u306a\u304f\u300cconstantxx\u300d\u304c\u8868\u793a\u3055\u308c\u307e\u3059
config.name.sublimiter = \u30b3\u30fc\u30c9\u30b5\u30d6\u306e\u5236\u9650
config.description.sublimiter = \u96e3\u8aad\u5316\u3055\u308c\u305f\u30b3\u30fc\u30c9\u306e\u30b3\u30fc\u30c9\u30b5\u30d6\u306e\u5236\u9650
config.name.exportTimeout = \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u306e\u30bf\u30a4\u30e0\u30a2\u30a6\u30c8\u6642\u9593 (\u79d2)
config.description.exportTimeout = \u3053\u306e\u6642\u9593\u7d4c\u3064\u3068\u3001\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3092\u505c\u6b62\u3057\u307e\u3059
config.name.decompilationTimeoutFile = \u5358\u4e00\u30d5\u30a1\u30a4\u30eb\u306e\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u306e\u30bf\u30a4\u30e0\u30a2\u30a6\u30c8\u6642\u9593 (\u79d2)
config.description.decompilationTimeoutFile = 1\u3064\u306e\u30d5\u30a1\u30a4\u30eb\u3067\u3053\u306e\u6642\u9593\u7d4c\u3064\u3068\u3001ActionScript \u306e\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3092\u505c\u6b62\u3057\u307e\u3059
config.name.paramNamesEnable = AS3 \u3067\u30d1\u30e9\u30e1\u30fc\u30bf\u540d\u3092\u6709\u52b9\u306b\u3059\u308b
config.description.paramNamesEnable = \u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3067\u30d1\u30e9\u30e1\u30fc\u30bf\u540d\u3092\u4f7f\u7528\u3059\u308b\u3068\u3001Flash CS 5.5 \u306a\u3069\u306e\u516c\u5f0f\u30d7\u30ed\u30b0\u30e9\u30e0\u304c\u9593\u9055\u3063\u305f\u30d1\u30e9\u30e1\u30fc\u30bf\u540d\u306e\u30a4\u30f3\u30c7\u30c3\u30af\u30b9\u3092\u633f\u5165\u3059\u308b\u305f\u3081\u3001\u554f\u984c\u304c\u767a\u751f\u3059\u308b\u53ef\u80fd\u6027\u304c\u3042\u308a\u307e\u3059
config.name.displayFileName = \u30bf\u30a4\u30c8\u30eb\u306b SWF \u540d\u3092\u8868\u793a\u3059\u308b
config.description.displayFileName = \u30a6\u30a3\u30f3\u30c9\u30a6\u30bf\u30a4\u30c8\u30eb\u306b\u3001SWF \u30d5\u30a1\u30a4\u30eb\u30d1\u30b9 / URL \u3092\u8868\u793a\u3057\u307e\u3059 (\u30b9\u30af\u30ea\u30fc\u30f3\u30b7\u30e7\u30c3\u30c8\u304c\u64ae\u308c\u308b\u3088\u3046\u306b\u306a\u308a\u307e\u3059)
config.name._debugCopy=\u518d\u30b3\u30f3\u30d1\u30a4\u30eb\u3059\u308b
config.description._debugCopy=\u540c\u3058\u30d0\u30a4\u30ca\u30ea\u30b3\u30fc\u30c9\u304c\u751f\u6210\u3055\u308c\u308b\u3053\u3068\u3092\u78ba\u8a8d\u3059\u308b\u305f\u3081\u306b\u3001\u958b\u3044\u305f\u76f4\u5f8c\u306e SWF \u30d5\u30a1\u30a4\u30eb\u3092\u518d\u5ea6\u30b3\u30f3\u30d1\u30a4\u30eb\u3057\u307e\u3059\u3002FFDec \u306e\u30c7\u30d0\u30c3\u30b0\u306b\u306e\u307f\u4f7f\u7528\u3057\u3066\u304f\u3060\u3055\u3044\uff01
config.name.dumpTags = \u30bf\u30b0\u3092\u30b3\u30f3\u30bd\u30fc\u30eb\u306b\u30c0\u30f3\u30d7\u3059\u308b
config.description.dumpTags = SWF \u30d5\u30a1\u30a4\u30eb\u306e\u8aad\u307f\u53d6\u308a\u6642\u306b\u3001\u30bf\u30b0\u3092\u30b3\u30f3\u30bd\u30fc\u30eb\u306b\u30c0\u30f3\u30d7\u3057\u307e\u3059
config.name.decompilationTimeoutSingleMethod = AS3: 1\u3064\u306e\u30e1\u30bd\u30c3\u30c9\u306e\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u306e\u30bf\u30a4\u30e0\u30a2\u30a6\u30c8\u6642\u9593 (\u79d2)
config.description.decompilationTimeoutSingleMethod = 1\u3064\u306e\u30e1\u30bd\u30c3\u30c9\u3067\u3053\u306e\u6642\u9593\u7d4c\u3064\u3068\u3001ActionScript \u306e\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3092\u505c\u6b62\u3057\u307e\u3059
config.name.lastRenameType = \uff08\u5185\u90e8\uff09\u524d\u56de\u306e\u540d\u524d\u5909\u66f4\u30bf\u30a4\u30d7
config.description.lastRenameType = \u6700\u5f8c\u306b\u4f7f\u7528\u3057\u305f\u8b58\u5225\u5b50\u306e\u540d\u524d\u5909\u66f4\u306e\u30bf\u30a4\u30d7
config.name.lastSaveDir = \uff08\u5185\u90e8\uff09\u524d\u56de\u306e\u4fdd\u5b58\u30c7\u30a3\u30ec\u30af\u30c8\u30ea
config.description.lastSaveDir = \u6700\u5f8c\u306b\u4f7f\u7528\u3057\u305f\u4fdd\u5b58\u30c7\u30a3\u30ec\u30af\u30c8\u30ea
config.name.lastOpenDir = \uff08\u5185\u90e8\uff09\u524d\u56de\u958b\u3044\u305f\u30c7\u30a3\u30ec\u30af\u30c8\u30ea
config.description.lastOpenDir = \u6700\u5f8c\u306b\u958b\u3044\u305f\u30c7\u30a3\u30ec\u30af\u30c8\u30ea
config.name.lastExportDir = \uff08\u5185\u90e8\uff09\u524d\u56de\u306e\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u30c7\u30a3\u30ec\u30af\u30c8\u30ea
config.description.lastExportDir = \u6700\u5f8c\u306b\u4f7f\u7528\u3057\u305f\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u5148\u306e\u30c7\u30a3\u30ec\u30af\u30c8\u30ea
config.name.locale = \u8a00\u8a9e
config.description.locale = \u30ed\u30b1\u30fc\u30ebID
config.name.registerNameFormat = \u30ec\u30b8\u30b9\u30bf\u5909\u6570\u306e\u5f62\u5f0f
config.description.registerNameFormat = \u30ed\u30fc\u30ab\u30eb\u30fb\u30ec\u30b8\u30b9\u30bf\u5909\u6570\u540d\u306e\u5f62\u5f0f\u3002\u30ec\u30b8\u30b9\u30bf\u756a\u53f7\u306b\u306f%d\u3092\u4f7f\u7528\u3057\u307e\u3059
config.name.maxRecentFileCount = \u6700\u8fd1\u306e\u30d5\u30a1\u30a4\u30eb\u306e\u6700\u5927\u6570
config.description.maxRecentFileCount = \u6700\u8fd1\u958b\u3044\u305f\u30d5\u30a1\u30a4\u30eb\u306e\u6700\u5927\u6570
config.name.recentFiles = \uff08\u5185\u90e8\uff09\u6700\u8fd1\u306e\u30d5\u30a1\u30a4\u30eb
config.description.recentFiles = \u6700\u8fd1\u958b\u3044\u305f\u30d5\u30a1\u30a4\u30eb
config.name.fontPairingMap = \uff08\u5185\u90e8\uff09\u30a4\u30f3\u30dd\u30fc\u30c8\u7528\u306e\u30d5\u30a9\u30f3\u30c8\u30da\u30a2
config.description.fontPairingMap = \u65b0\u3057\u3044\u6587\u5b57\u3092\u30a4\u30f3\u30dd\u30fc\u30c8\u3059\u308b\u305f\u3081\u306e\u30d5\u30a9\u30f3\u30c8\u30da\u30a2
config.name.lastUpdatesCheckDate = \uff08\u5185\u90e8\uff09\u6700\u7d42\u66f4\u65b0\u30c1\u30a7\u30c3\u30af\u65e5
config.description.lastUpdatesCheckDate = \u30b5\u30fc\u30d0\u30fc\u306e\u66f4\u65b0\u3092\u6700\u5f8c\u306b\u30c1\u30a7\u30c3\u30af\u3057\u305f\u65e5\u4ed8
config.name.gui.window.width = \uff08\u5185\u90e8\uff09\u524d\u56de\u306e\u30a6\u30a3\u30f3\u30c9\u30a6\u5e45
config.description.gui.window.width = \u6700\u5f8c\u306b\u4fdd\u5b58\u3055\u308c\u305f\u30a6\u30a3\u30f3\u30c9\u30a6\u306e\u5e45
config.name.gui.window.height = \uff08\u5185\u90e8\uff09\u524d\u56de\u306e\u30a6\u30a3\u30f3\u30c9\u30a6\u306e\u9ad8\u3055
config.description.gui.window.height = \u6700\u5f8c\u306b\u4fdd\u5b58\u3055\u308c\u305f\u30a6\u30a3\u30f3\u30c9\u30a6\u306e\u9ad8\u3055
config.name.gui.window.maximized.horizontal = \uff08\u5185\u90e8\uff09\u30a6\u30a3\u30f3\u30c9\u30a6\u3092\u6c34\u5e73\u65b9\u5411\u306b\u6700\u5927\u5316
config.description.gui.window.maximized.horizontal = \u30a6\u30a3\u30f3\u30c9\u30a6\u304c\u6c34\u5e73\u65b9\u5411\u306b\u6700\u5927\u5316\u3055\u308c\u3066\u3044\u308b\u304b
config.name.gui.window.maximized.vertical = \uff08\u5185\u90e8\uff09\u30a6\u30a3\u30f3\u30c9\u30a6\u3092\u5782\u76f4\u65b9\u5411\u306b\u6700\u5927\u5316
config.description.gui.window.maximized.vertical = \u30a6\u30a3\u30f3\u30c9\u30a6\u304c\u5782\u76f4\u65b9\u5411\u306b\u6700\u5927\u5316\u3055\u308c\u3066\u3044\u308b\u304b
config.name.gui.avm2.splitPane.dividerLocationPercent=\uff08\u5185\u90e8\uff09AS3 Splitter \u306e\u5834\u6240
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = \uff08\u5185\u90e8\uff09AS1/2 Splitter \u306e\u5834\u6240
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = \uff08\u5185\u90e8\uff09\u30d7\u30ec\u30d3\u30e5\u30fc Splitter \u306e\u5834\u6240
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=\uff08\u5185\u90e8\uff09Splitter \u306e\u5834\u62401
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=\uff08\u5185\u90e8\uff09Splitter \u306e\u5834\u62402
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = EXE \u30b9\u30b1\u30fc\u30eb\u30e2\u30fc\u30c9\u3068\u3057\u3066\u4fdd\u5b58\u3059\u308b
config.description.saveAsExeScaleMode = EXE \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u306e\u30b9\u30b1\u30fc\u30ea\u30f3\u30b0\u30e2\u30fc\u30c9
config.name.syntaxHighlightLimit = \u30b7\u30f3\u30bf\u30c3\u30af\u30b9\u30cf\u30a4\u30e9\u30a4\u30c8\u306e\u6700\u5927\u6587\u5b57\u6570
config.description.syntaxHighlightLimit = \u30b7\u30f3\u30bf\u30c3\u30af\u30b9\u30cf\u30a4\u30e9\u30a4\u30c8\u3092\u5b9f\u884c\u3059\u308b\u6700\u5927\u6587\u5b57\u6570
config.name.guiFontPreviewSampleText = \uff08\u5185\u90e8\uff09\u524d\u56de\u30d7\u30ec\u30d3\u30e5\u30fc\u3057\u305f\u30d5\u30a9\u30f3\u30c8\u306e\u30b5\u30f3\u30d7\u30eb\u30c6\u30ad\u30b9\u30c8
config.description.guiFontPreviewSampleText = \u6700\u5f8c\u306b\u30d7\u30ec\u30d3\u30e5\u30fc\u3057\u305f\u3001\u30d5\u30a9\u30f3\u30c8\u306e\u30b5\u30f3\u30d7\u30eb\u30c6\u30ad\u30b9\u30c8\u3089\u306e\u30a4\u30f3\u30c7\u30c3\u30af\u30b9
config.name.gui.fontPreviewWindow.width = \uff08\u5185\u90e8\uff09\u30d5\u30a9\u30f3\u30c8\u30d7\u30ec\u30d3\u30e5\u30fc\u30a6\u30a3\u30f3\u30c9\u30a6\u306e\u5e45
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = \uff08\u5185\u90e8\uff09\u30d5\u30a9\u30f3\u30c8\u30d7\u30ec\u30d3\u30e5\u30fc\u30a6\u30a3\u30f3\u30c9\u30a6\u306e\u9ad8\u3055
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = \uff08\u5185\u90e8\uff09\u30d5\u30a9\u30f3\u30c8\u30d7\u30ec\u30d3\u30e5\u30fc\u30a6\u30a3\u30f3\u30c9\u30a6\u306eX\u4f4d\u7f6e
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = \uff08\u5185\u90e8\uff09\u30d5\u30a9\u30f3\u30c8\u30d7\u30ec\u30d3\u30e5\u30fc\u30a6\u30a3\u30f3\u30c9\u30a6\u306eY\u4f4d\u7f6e
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = \u30a4\u30f3\u30c7\u30f3\u30c8\u5e45
config.description.formatting.indent.size = 1\u3064\u306e\u30a4\u30f3\u30c7\u30f3\u30c8\u304c\u30b9\u30da\u30fc\u30b9\u4f55\u6587\u5b57\u5206\u306e\u5e45\u304b
config.name.formatting.indent.useTabs = \u30a4\u30f3\u30c7\u30f3\u30c8\u306b\u30bf\u30d6\u3092\u4f7f\u3046
config.description.formatting.indent.useTabs = \u30a4\u30f3\u30c7\u30f3\u30c8\u306b\u3001\u30b9\u30da\u30fc\u30b9\u306e\u4ee3\u308f\u308a\u306b\u30bf\u30d6\u3092\u4f7f\u7528\u3057\u307e\u3059
config.name.beginBlockOnNewLine = \u4e2d\u62ec\u5f27\u3092\u65b0\u3057\u3044\u884c\u306b
config.description.beginBlockOnNewLine = \u65b0\u3057\u3044\u884c\u3067\u3001\u4e2d\u62ec\u5f27\u306e\u30d6\u30ed\u30c3\u30af\u3092\u958b\u59cb\u3057\u307e\u3059
config.name.check.updates.delay = \u66f4\u65b0\u30c1\u30a7\u30c3\u30af\u307e\u3067\u306e\u9045\u5ef6
config.description.check.updates.delay = \u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u8d77\u52d5\u6642\u306b\u3001\u66f4\u65b0\u306e\u81ea\u52d5\u30c1\u30a7\u30c3\u30af\u3092\u884c\u3046\u307e\u3067\u306e\u6700\u5c0f\u6642\u9593
config.name.check.updates.stable = \u5b89\u5b9a\u7248\u306e\u30c1\u30a7\u30c3\u30af
config.description.check.updates.stable = \u5b89\u5b9a\u7248\u306e\u66f4\u65b0\u3092\u30c1\u30a7\u30c3\u30af\u3057\u307e\u3059
config.name.check.updates.nightly = \u30ca\u30a4\u30c8\u30ea\u30fc\uff08\u958b\u767a\uff09\u7248\u306e\u30c1\u30a7\u30c3\u30af
config.description.check.updates.nightly = \u30ca\u30a4\u30c8\u30ea\u30fc\uff08\u958b\u767a\uff09\u7248\u306e\u66f4\u65b0\u3092\u30c1\u30a7\u30c3\u30af\u3057\u307e\u3059
config.name.check.updates.enabled = \u30a2\u30c3\u30d7\u30c7\u30fc\u30c8\u306e\u30c1\u30a7\u30c3\u30af
config.description.check.updates.enabled = \u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u8d77\u52d5\u6642\u306b\u3001\u66f4\u65b0\u3092\u81ea\u52d5\u3067\u30c1\u30a7\u30c3\u30af\u3057\u307e\u3059
config.name.export.formats = \uff08\u5185\u90e8\uff09\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u306e\u5f62\u5f0f
config.description.export.formats = \u6700\u5f8c\u306b\u4f7f\u7528\u3057\u305f\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u306e\u5f62\u5f0f
config.name.textExportSingleFile = \u30c6\u30ad\u30b9\u30c8\u3092\u5358\u4e00\u30d5\u30a1\u30a4\u30eb\u306b\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3059\u308b
config.description.textExportSingleFile = \u30c6\u30ad\u30b9\u30c8\u3092\u8907\u6570\u3067\u306f\u306a\u304f\u30011\u3064\u306e\u30d5\u30a1\u30a4\u30eb\u306b\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3057\u307e\u3059
config.name.textExportSingleFileSeparator = \u5358\u4e00\u30d5\u30a1\u30a4\u30eb\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u6642\u306e\u533a\u5207\u308a\u6587\u5b57
config.description.textExportSingleFileSeparator = 1\u3064\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3059\u308b\u3068\u304d\u306b\u3001\u30c6\u30ad\u30b9\u30c8\u9593\u306b\u633f\u5165\u3059\u308b\u6587\u5b57\u5217
config.name.textExportSingleFileRecordSeparator = \u5358\u4e00\u30d5\u30a1\u30a4\u30eb\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u6642\u306e\u30ec\u30b3\u30fc\u30c9\u306e\u533a\u5207\u308a\u6587\u5b57
config.description.textExportSingleFileRecordSeparator = 1\u3064\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3059\u308b\u3068\u304d\u306b\u3001\u30c6\u30ad\u30b9\u30c8\u30ec\u30b3\u30fc\u30c9\u9593\u306b\u633f\u5165\u3059\u308b\u6587\u5b57\u5217
config.name.warning.experimental.as12edit=AS1/2 \u306e\u76f4\u63a5\u7de8\u96c6\u3092\u8b66\u544a\u3059\u308b
config.description.warning.experimental.as12edit=\u5b9f\u9a13\u7684\u306a AS1/2 \u306e\u76f4\u63a5\u7de8\u96c6\u306b\u3064\u3044\u3066\u306e\u8b66\u544a\u3092\u8868\u793a\u3057\u307e\u3059
config.name.warning.experimental.as3edit=AS3 \u306e\u76f4\u63a5\u7de8\u96c6\u3092\u8b66\u544a\u3059\u308b
config.description.warning.experimental.as3edit=\u5b9f\u9a13\u7684\u306a AS3 \u306e\u76f4\u63a5\u7de8\u96c6\u306b\u3064\u3044\u3066\u306e\u8b66\u544a\u3092\u8868\u793a\u3057\u307e\u3059
config.name.packJavaScripts = JavaScript \u3092\u30d1\u30c3\u30af\u3059\u308b
config.description.packJavaScripts = Canvas Export \u3067\u4f5c\u6210\u3055\u308c\u305f\u30b9\u30af\u30ea\u30d7\u30c8\u3067\u3001JavaScript packer \u3092\u5b9f\u884c\u3057\u307e\u3059
config.name.textExportExportFontFace = SVG \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3067 font-face \u3092\u4f7f\u7528\u3059\u308b
config.description.textExportExportFontFace = SVG \u3078\u306e\u30d5\u30a9\u30f3\u30c8\u30d5\u30a1\u30a4\u30eb\u57cb\u3081\u8fbc\u307f\u3067\u3001shapes \u306e\u4ee3\u308f\u308a\u306b font-face \u3092\u4f7f\u7528\u3057\u307e\u3059
config.name.lzmaFastBytes = LZMA fast bytes (\u6709\u52b9\u306a\u5024: 5\uff5e255)
config.description.lzmaFastBytes = LZMA \u30a8\u30f3\u30b3\u30fc\u30c0\u30fc\u306e fast bytes \u30d1\u30e9\u30e1\u30fc\u30bf\u30fc
config.name.pluginPath = \u30d7\u30e9\u30b0\u30a4\u30f3\u30d1\u30b9
config.description.pluginPath = -
config.name.showMethodBodyId = \u30e1\u30bd\u30c3\u30c9\u672c\u4f53\u306eID\u3092\u8868\u793a\u3059\u308b
config.description.showMethodBodyId = \u30b3\u30de\u30f3\u30c9\u30e9\u30a4\u30f3\u30a4\u30f3\u30dd\u30fc\u30c8\u7528\u306e\u30e1\u30bd\u30c3\u30c9\u672c\u4f53\u306eID\u3092\u8868\u793a\u3057\u307e\u3059
config.name.export.zoom = \uff08\u5185\u90e8\uff09\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u306e\u30ba\u30fc\u30e0\u7387
config.description.export.zoom = \u6700\u5f8c\u306b\u4f7f\u7528\u3057\u305f\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u306e\u30ba\u30fc\u30e0\u7387
config.name.debuggerPort = \u30c7\u30d0\u30c3\u30ac\u30dd\u30fc\u30c8
config.description.debuggerPort = \u30bd\u30b1\u30c3\u30c8\u306e\u30c7\u30d0\u30c3\u30b0\u306b\u4f7f\u7528\u3059\u308b\u30dd\u30fc\u30c8
config.name.displayDebuggerInfo = \uff08\u5185\u90e8\uff09\u30c7\u30d0\u30c3\u30ac\u60c5\u5831\u3092\u8868\u793a\u3059\u308b
config.description.displayDebuggerInfo = \u30c7\u30d0\u30c3\u30ac\u3092\u5207\u308a\u66ff\u3048\u308b\u524d\u306b\u3001\u30c7\u30d0\u30c3\u30ac\u306b\u95a2\u3059\u308b\u60c5\u5831\u3092\u8868\u793a\u3057\u307e\u3059
config.name.randomDebuggerPackage = \u30c7\u30d0\u30c3\u30ac\u306b\u30e9\u30f3\u30c0\u30e0\u306a\u30d1\u30c3\u30b1\u30fc\u30b8\u540d\u3092\u4f7f\u7528\u3059\u308b
config.description.randomDebuggerPackage = \u30c7\u30d0\u30c3\u30ac\u306e\u30d1\u30c3\u30b1\u30fc\u30b8\u540d\u304c\u30e9\u30f3\u30c0\u30e0\u306a\u6587\u5b57\u5217\u306b\u5909\u66f4\u3055\u308c\u3001ActionScript \u306b\u3088\u308b\u3001\u30c7\u30d0\u30c3\u30ac\u306e\u5b58\u5728\u306e\u691c\u51fa\u3092\u3055\u308c\u306b\u304f\u304f\u3057\u307e\u3059
config.name.lastDebuggerReplaceFunction = \uff08\u5185\u90e8\uff09\u6700\u5f8c\u306b\u9078\u629e\u3055\u308c\u305f trace \u306e\u7f6e\u63db
# config.description.lastDebuggerReplaceFunction = \u30c7\u30d0\u30c3\u30ac\u3067trace\u3092\u7f6e\u304d\u63db\u3048\u308b\u969b\u306b\u3001\u6700\u5f8c\u306b\u9078\u629e\u3055\u308c\u305f\u95a2\u6570\u540d
config.name.getLocalNamesFromDebugInfo = AS3: \u30c7\u30d0\u30c3\u30b0\u60c5\u5831\u304b\u3089\u30ed\u30fc\u30ab\u30eb\u30ec\u30b8\u30b9\u30bf\u540d\u3092\u53d6\u5f97\u3059\u308b
config.description.getLocalNamesFromDebugInfo = \u30c7\u30d0\u30c3\u30b0\u60c5\u5831\u304c\u3042\u308b\u5834\u5408\u3001\u30ed\u30fc\u30ab\u30eb\u30ec\u30b8\u30b9\u30bf\u306e\u540d\u524d\u3092 _loc_x_ \u304b\u3089\u5b9f\u969b\u306e\u540d\u524d\u306b\u5909\u66f4\u3057\u307e\u3059\u3002\u4e00\u90e8\u306e\u96e3\u8aad\u5316\u30c4\u30fc\u30eb\u306f\u7121\u52b9\u306a\u30ec\u30b8\u30b9\u30bf\u540d\u3092\u4f7f\u7528\u3059\u308b\u305f\u3081\u3001\u3053\u308c\u306f\u30aa\u30d5\u306b\u3059\u308b\u3053\u3068\u304c\u3067\u304d\u307e\u3059
config.name.tagTreeShowEmptyFolders = \u7a7a\u306e\u30d5\u30a9\u30eb\u30c0\u3092\u8868\u793a\u3059\u308b
config.description.tagTreeShowEmptyFolders = \u30bf\u30b0\u30c4\u30ea\u30fc\u306b\u7a7a\u306e\u30d5\u30a9\u30eb\u30c0\u3092\u8868\u793a\u3057\u307e\u3059
config.name.autoLoadEmbeddedSwfs = \u57cb\u3081\u8fbc\u307e\u308c\u305f SWF \u3092\u81ea\u52d5\u7684\u306b\u8aad\u307f\u8fbc\u3080
config.description.autoLoadEmbeddedSwfs = DefineBinaryData \u30bf\u30b0\u304b\u3089\u57cb\u3081\u8fbc\u307e\u308c\u305f SWF \u3092\u3001\u81ea\u52d5\u7684\u306b\u8aad\u307f\u8fbc\u307f\u307e\u3059
config.name.overrideTextExportFileName = \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3059\u308b\u30c6\u30ad\u30b9\u30c8\u30d5\u30a1\u30a4\u30eb\u540d\u3092\u4e0a\u66f8\u304d\u3059\u308b
config.description.overrideTextExportFileName = \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3055\u308c\u305f\u30c6\u30ad\u30b9\u30c8\u306e\u30d5\u30a1\u30a4\u30eb\u540d\u3092\u30ab\u30b9\u30bf\u30de\u30a4\u30ba\u3067\u304d\u307e\u3059\u3002\u73fe\u5728\u306e SWF \u306e\u30d5\u30a1\u30a4\u30eb\u540d\u3092\u4f7f\u7528\u3059\u308b\u306b\u306f\u3001{filename} \u306e\u30d7\u30ec\u30fc\u30b9\u30db\u30eb\u30c0\u3092\u4f7f\u7528\u3057\u307e\u3059
config.name.showOldTextDuringTextEditing = \u30c6\u30ad\u30b9\u30c8\u7de8\u96c6\u4e2d\u306b\u53e4\u3044\u30c6\u30ad\u30b9\u30c8\u3092\u8868\u793a\u3059\u308b
config.description.showOldTextDuringTextEditing = \u30d7\u30ec\u30d3\u30e5\u30fc\u9818\u57df\u306b\u3001\u30c6\u30ad\u30b9\u30c8\u30bf\u30b0\u306e\u5143\u306e\u30c6\u30ad\u30b9\u30c8\u3092\u3001\u7070\u8272\u3067\u8868\u793a\u3057\u307e\u3059
config.group.name.import = \u30a4\u30f3\u30dd\u30fc\u30c8
config.group.description.import = \u30a4\u30f3\u30dd\u30fc\u30c8\u306e\u8a2d\u5b9a
config.name.textImportResizeTextBoundsMode = \u30c6\u30ad\u30b9\u30c8\u5883\u754c\u306e\u30ea\u30b5\u30a4\u30ba\u30e2\u30fc\u30c9
config.description.textImportResizeTextBoundsMode = \u30c6\u30ad\u30b9\u30c8\u7de8\u96c6\u5f8c\u306e\u3001\u30c6\u30ad\u30b9\u30c8\u5883\u754c\u306e\u30b5\u30a4\u30ba\u5909\u66f4\u30e2\u30fc\u30c9
config.name.showCloseConfirmation = \u5909\u66f4\u3055\u308c\u305f SWF \u3092\u9589\u3058\u308b\u304b\u306e\u78ba\u8a8d\u3092\u8868\u793a\u3059\u308b
config.description.showCloseConfirmation = \u5909\u66f4\u3055\u308c\u305f SWF \u30d5\u30a1\u30a4\u30eb\u3092\u9589\u3058\u308b\u304b\u306e\u78ba\u8a8d\u3092\u8868\u793a\u3057\u307e\u3059
config.name.showCodeSavedMessage = \u30b3\u30fc\u30c9\u4fdd\u5b58\u306e\u30e1\u30c3\u30bb\u30fc\u30b8\u3092\u8868\u793a\u3059\u308b
config.description.showCodeSavedMessage = \u30b3\u30fc\u30c9 (ActionScript) \u304c\u4fdd\u5b58\u3055\u308c\u305f\u3001\u3068\u3044\u3046\u30e1\u30c3\u30bb\u30fc\u30b8\u3092\u8868\u793a\u3057\u307e\u3059
config.name.showTraitSavedMessage = trait \u4fdd\u5b58\u306e\u30e1\u30c3\u30bb\u30fc\u30b8\u3092\u8868\u793a\u3059\u308b
config.description.showTraitSavedMessage = trait (P-code) \u304c\u4fdd\u5b58\u3055\u308c\u305f\u3001\u3068\u3044\u3046\u30e1\u30c3\u30bb\u30fc\u30b8\u3092\u8868\u793a\u3057\u307e\u3059
config.name.updateProxyAddress = \u66f4\u65b0\u3092\u78ba\u8a8d\u3059\u308b\u305f\u3081\u306e http \u30d7\u30ed\u30ad\u30b7\u30a2\u30c9\u30ec\u30b9
config.description.updateProxyAddress = \u66f4\u65b0\u3092\u78ba\u8a8d\u3059\u308b\u305f\u3081\u306e http \u30d7\u30ed\u30ad\u30b7\u30a2\u30c9\u30ec\u30b9 (\u5f62\u5f0f: example.com:8080)
config.name.editorMode = \u30a8\u30c7\u30a3\u30bf\u30e2\u30fc\u30c9
config.description.editorMode = \u30c6\u30ad\u30b9\u30c8\u307e\u305f\u306f\u30b9\u30af\u30ea\u30d7\u30c8\u30ce\u30fc\u30c9\u3092\u9078\u629e\u3057\u305f\u3068\u304d\u306b\u3001\u81ea\u52d5\u7684\u306b\u30c6\u30ad\u30b9\u30c8\u30a8\u30ea\u30a2\u3092\u7de8\u96c6\u53ef\u80fd\u306b\u3057\u307e\u3059
config.name.autoSaveTagModifications = \u30bf\u30b0\u5207\u308a\u66ff\u3048\u6642\u306b\u81ea\u52d5\u4fdd\u5b58
config.description.autoSaveTagModifications = \u30c4\u30ea\u30fc\u3067\u65b0\u3057\u3044\u30bf\u30b0\u3092\u9078\u629e\u3057\u305f\u3068\u304d\u306b\u3001\u5909\u66f4\u3092\u4fdd\u5b58\u3057\u307e\u3059
config.name.saveSessionOnExit = \u7d42\u4e86\u6642\u306b\u30bb\u30c3\u30b7\u30e7\u30f3\u3092\u4fdd\u5b58\u3059\u308b
config.description.saveSessionOnExit = \u73fe\u5728\u306e\u30bb\u30c3\u30b7\u30e7\u30f3\u3092\u4fdd\u5b58\u3057\u3001FFDec \u518d\u8d77\u52d5\u5f8c\u306b\u518d\u3073\u958b\u304d\u307e\u3059 (\u5b9f\u969b\u306e\u30d5\u30a1\u30a4\u30eb\u3067\u306e\u307f\u52d5\u4f5c\u3057\u307e\u3059)
config.name._showDebugMenu=FFDec \u306e\u30c7\u30d0\u30c3\u30b0\u30e1\u30cb\u30e5\u30fc\u3092\u8868\u793a\u3059\u308b
config.description._showDebugMenu=\u30ea\u30dc\u30f3\u306b\u3001\u9006\u30b3\u30f3\u30d1\u30a4\u30e9\u306e\u30c7\u30d0\u30c3\u30b0\u7528\u30e1\u30cb\u30e5\u30fc\u3092\u8868\u793a\u3057\u307e\u3059
config.name.allowOnlyOneInstance = FFDec \u306e\u30a4\u30f3\u30b9\u30bf\u30f3\u30b9\u30921\u3064\u3060\u3051\u8a31\u53ef\u3059\u308b (Windows \u306e\u307f)
config.description.allowOnlyOneInstance = FFDec \u306f1\u3064\u3060\u3051\u5b9f\u884c\u3067\u304d\u3001\u958b\u3044\u305f\u30d5\u30a1\u30a4\u30eb\u306f\u3059\u3079\u30661\u3064\u306e\u30a6\u30a3\u30f3\u30c9\u30a6\u306b\u8ffd\u52a0\u3055\u308c\u307e\u3059\u3002\u3053\u308c\u306f Windows OS \u3067\u306e\u307f\u52d5\u4f5c\u3057\u307e\u3059
config.name.scriptExportSingleFile = \u30b9\u30af\u30ea\u30d7\u30c8\u3092\u5358\u4e00\u30d5\u30a1\u30a4\u30eb\u306b\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3059\u308b
config.description.scriptExportSingleFile = \u30b9\u30af\u30ea\u30d7\u30c8\u3092\u3001\u8907\u6570\u3067\u306f\u306a\u304f1\u3064\u306e\u30d5\u30a1\u30a4\u30eb\u306b\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3057\u307e\u3059
config.name.setFFDecVersionInExportedFont = \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3057\u305f\u30d5\u30a9\u30f3\u30c8\u306b\u3001FFDec \u306e\u30d0\u30fc\u30b8\u30e7\u30f3\u756a\u53f7\u3092\u8a2d\u5b9a\u3059\u308b
config.description.setFFDecVersionInExportedFont = \u3053\u306e\u8a2d\u5b9a\u3092\u7121\u52b9\u306b\u3059\u308b\u3068\u3001FFDec \u306f\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3057\u305f\u30d5\u30a9\u30f3\u30c8\u306b\u3001\u73fe\u5728\u306e FFDec \u306e\u30d0\u30fc\u30b8\u30e7\u30f3\u756a\u53f7\u3092\u8ffd\u52a0\u3057\u307e\u305b\u3093
config.name.gui.skin = \u30e6\u30fc\u30b6\u30fc\u30a4\u30f3\u30bf\u30fc\u30d5\u30a7\u30fc\u30b9\u306e\u30b9\u30ad\u30f3
config.description.gui.skin = \u30b9\u30ad\u30f3\u3092\u898b\u3066\u611f\u3058\u3066\u304f\u3060\u3055\u3044
config.name.lastSessionFiles = \u524d\u56de\u306e\u30bb\u30c3\u30b7\u30e7\u30f3\u30d5\u30a1\u30a4\u30eb
config.description.lastSessionFiles = \u524d\u56de\u306e\u30bb\u30c3\u30b7\u30e7\u30f3\u3067\u958b\u3044\u3066\u3044\u305f\u30d5\u30a1\u30a4\u30eb\u3089
config.name.lastSessionSelection = \u524d\u56de\u306e\u30bb\u30c3\u30b7\u30e7\u30f3\u306e\u9078\u629e\u72b6\u614b
config.description.lastSessionSelection = \u524d\u56de\u306e\u30bb\u30c3\u30b7\u30e7\u30f3\u3067\u9078\u629e\u3057\u3066\u3044\u305f\u30bf\u30b0
config.name.loopMedia = \u30b5\u30a6\u30f3\u30c9\u3068\u30b9\u30d7\u30e9\u30a4\u30c8\u3092\u30eb\u30fc\u30d7\u3055\u305b\u308b
config.description.loopMedia = \u30b5\u30a6\u30f3\u30c9\u3068\u30b9\u30d7\u30e9\u30a4\u30c8\u306e\u518d\u751f\u3092\u81ea\u52d5\u7684\u306b\u518d\u958b\u3057\u307e\u3059
config.name.gui.timeLineSplitPane.dividerLocationPercent = \uff08\u5185\u90e8\uff09\u30bf\u30a4\u30e0\u30e9\u30a4\u30f3 Splitter \u306e\u5834\u6240
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = \u753b\u50cf\u306e\u30ad\u30e3\u30c3\u30b7\u30e5
config.description.cacheImages = \u30c7\u30b3\u30fc\u30c9\u3055\u308c\u305f\u753b\u50cf\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u3092\u30ad\u30e3\u30c3\u30b7\u30e5\u3057\u307e\u3059
config.name.swfSpecificConfigs = SWF \u56fa\u6709\u306e\u8a2d\u5b9a
config.description.swfSpecificConfigs = SWF \u56fa\u6709\u306e\u8a2d\u5b9a\u304c\u542b\u307e\u308c\u307e\u3059
config.name.exeExportMode = EXE \u306e\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u30e2\u30fc\u30c9
config.description.exeExportMode = EXE \u306e\u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u30e2\u30fc\u30c9
config.name.ignoreCLikePackages = FlashCC / Alchemy\u3001\u307e\u305f\u306f\u985e\u4f3c\u306e\u30d1\u30c3\u30b1\u30fc\u30b8\u3092\u7121\u8996\u3059\u308b
config.description.ignoreCLikePackages = FlashCC / Alchemy \u30d1\u30c3\u30b1\u30fc\u30b8\u306f\u901a\u5e38\u6b63\u3057\u304f\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3067\u304d\u307e\u305b\u3093\u3002\u3053\u308c\u3089\u3092\u7121\u52b9\u306b\u3059\u308b\u3053\u3068\u3067\u3001\u4ed6\u306e\u30d1\u30c3\u30b1\u30fc\u30b8\u306e\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3092\u9ad8\u901f\u5316\u3059\u308b\u3053\u3068\u304c\u3067\u304d\u307e\u3059
config.name.overwriteExistingFiles = \u65e2\u5b58\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u4e0a\u66f8\u304d\u3059\u308b
config.description.overwriteExistingFiles = \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u6642\u306b\u65e2\u5b58\u306e\u30d5\u30a1\u30a4\u30eb\u3092\u4e0a\u66f8\u304d\u3057\u307e\u3059\u3002\u73fe\u5728\u306f AS2/3 \u306e\u30b9\u30af\u30ea\u30d7\u30c8\u3067\u306e\u307f
config.name.smartNumberFormatting = \u30b9\u30de\u30fc\u30c8\u306a\u6570\u5b57\u30d5\u30a9\u30fc\u30de\u30c3\u30c8\u3092\u4f7f\u7528\u3059\u308b
config.description.smartNumberFormatting = \u7279\u6b8a\u306a\u6570\u5b57\u3092\u30d5\u30a9\u30fc\u30de\u30c3\u30c8\u3059\u308b (\u8272\u3084\u6642\u9593\u306a\u3069)
config.name.enableScriptInitializerDisplay = (REMOVED) \u30b9\u30af\u30ea\u30d7\u30c8\u306e\u30a4\u30cb\u30b7\u30e3\u30e9\u30a4\u30b6\u3092\u8868\u793a\u3059\u308b
config.description.enableScriptInitializerDisplay = \u30b9\u30af\u30ea\u30d7\u30c8\u30a4\u30cb\u30b7\u30e3\u30e9\u30a4\u30b6\u306e\u8868\u793a\u3068\u7de8\u96c6\u3092\u6709\u52b9\u306b\u3057\u307e\u3059\u3002\u3053\u306e\u8a2d\u5b9a\u306b\u3088\u308a\u3001\u5f37\u8abf\u8868\u793a\u306e\u305f\u3081\u306b\u5404\u30af\u30e9\u30b9\u30d5\u30a1\u30a4\u30eb\u306b\u6539\u884c\u30921\u3064\u8ffd\u52a0\u3059\u308b\u3053\u3068\u304c\u3042\u308a\u307e\u3059
config.name.autoOpenLoadedSWFs = \u8aad\u307f\u8fbc\u3093\u3060 SWF \u3092\u5b9f\u884c\u4e2d\u306b\u958b\u304f (\u5916\u90e8\u30d3\u30e5\u30fc\u30a2 = WIN \u306e\u307f)
config.description.autoOpenLoadedSWFs = FFDec \u5916\u90e8\u30d7\u30ec\u30fc\u30e4\u30fc\u3067 SWF \u3092\u518d\u751f\u3057\u305f\u969b\u306b\u3001AS3 class Loader \u3067\u8aad\u307f\u8fbc\u307e\u308c\u305f\u3059\u3079\u3066\u306e SWF \u3092\u81ea\u52d5\u7684\u306b\u958b\u304d\u307e\u3059\u3002\u3053\u306e\u6a5f\u80fd\u306f Windows \u306e\u307f\u3067\u3059
config.name.lastSessionFileTitles = \u524d\u56de\u306e\u30bb\u30c3\u30b7\u30e7\u30f3\u30d5\u30a1\u30a4\u30eb\u306e\u30bf\u30a4\u30c8\u30eb
config.description.lastSessionFileTitles = \u524d\u56de\u306e\u30bb\u30c3\u30b7\u30e7\u30f3\u3067\u958b\u3044\u305f\u30d5\u30a1\u30a4\u30eb\u306e\u30bf\u30a4\u30c8\u30eb\u3089 (URL \u304b\u3089\u8aad\u307f\u8fbc\u3093\u3060\u5834\u5408\u306a\u3069)
config.group.name.paths = \u30d1\u30b9
config.group.description.paths = \u5fc5\u8981\u306a\u30d5\u30a1\u30a4\u30eb\u306e\u5834\u6240
config.group.tip.paths = projector \u3068 PlayerGlobal \u306f\u3001<a href="%link1%">Adobe \u306e\u30a6\u30a7\u30d6\u30da\u30fc\u30b8</a> \u304b\u3089\u30c0\u30a6\u30f3\u30ed\u30fc\u30c9\u3067\u304d\u307e\u3059\u3002Flex SDK \u306f <a href="%link2%">apache web</a> \u3067\u30c0\u30a6\u30f3\u30ed\u30fc\u30c9\u3067\u304d\u307e\u3059\u3002
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Flash Player projector \u306e\u30d1\u30b9
config.description.playerLocation = \u30b9\u30bf\u30f3\u30c9\u30a2\u30ed\u30f3\u306e Flash Player \u5b9f\u884c\u30d5\u30a1\u30a4\u30eb\u306e\u5834\u6240\u3002\u300c\u5b9f\u884c\u300d\u306b\u4f7f\u7528\u3055\u308c\u307e\u3059
config.name.playerDebugLocation = 2) Flash Player projector content debugger \u306e\u30d1\u30b9
config.description.playerDebugLocation = \u30b9\u30bf\u30f3\u30c9\u30a2\u30ed\u30f3\u306e\u30c7\u30d0\u30c3\u30b0\u7248 Flash Player \u5b9f\u884c\u30d5\u30a1\u30a4\u30eb\u306e\u5834\u6240\u3002\u300c\u30c7\u30d0\u30c3\u30b0\u300d\u306b\u4f7f\u7528\u3055\u308c\u307e\u3059
config.name.playerLibLocation = 3) PlayerGlobal (.swc) \u306e\u30d1\u30b9
config.description.playerLibLocation = playerglobal.swc Flash Player \u30e9\u30a4\u30d6\u30e9\u30ea\u306e\u5834\u6240\u3002\u4e3b\u306b AS3 \u306e\u30b3\u30f3\u30d1\u30a4\u30eb\u306b\u4f7f\u7528\u3055\u308c\u307e\u3059
config.name.debugHalt = \u30c7\u30d0\u30c3\u30b0\u958b\u59cb\u6642\u306b\u5b9f\u884c\u3092\u505c\u6b62\u3059\u308b
config.description.debugHalt = \u30c7\u30d0\u30c3\u30b0\u306e\u958b\u59cb\u6642\u306b\u3001SWF \u3092\u4e00\u6642\u505c\u6b62\u3057\u307e\u3059
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=\uff08\u5185\u90e8\uff09\u30c7\u30d0\u30c3\u30b0\u30e1\u30cb\u30e5\u30fc\u306e Splitter \u306e\u5834\u6240
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Tip: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = \uff08\u5185\u90e8\uff09AS1/2 \u306e\u30c7\u30d0\u30c3\u30b0\u30e1\u30cb\u30e5\u30fc\u306e Splitter \u306e\u5834\u6240
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = \u5916\u90e8\u30d7\u30ec\u30fc\u30e4\u30fc\u3067 SWF \u3092\u5909\u66f4\u3059\u308b\u524d\u306e\u9045\u5ef6\u6642\u9593 (ms)
config.description.setMovieDelay = \u3053\u306e\u5024\u3092 1000ms \u4ee5\u4e0b\u306b\u5909\u66f4\u3059\u308b\u3053\u3068\u306f\u63a8\u5968\u3055\u308c\u307e\u305b\u3093
config.name.warning.svgImport = SVG \u3092\u30a4\u30f3\u30dd\u30fc\u30c8\u6642\u306b\u8b66\u544a\u3059\u308b
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = \u56f3\u5f62\u3092\u753b\u50cf\u306b\u7f6e\u304d\u63db\u3048\u308b\u5834\u5408\u306b\u3001\u6ed1\u3089\u304b\u3067\u306a\u3044\u5857\u308a\u3064\u3076\u3057\u3092\u4f7f\u7528\u3059\u308b
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=AS1/2 \u3092\u72ec\u81ea\u306e flash viewer \u3067 (\u5b9f\u9a13\u7684)
config.description.internalFlashViewer.execute.as12=FFDec flash viewer \u3067\u306e SWF \u518d\u751f\u6642\u306b\u3001ActionScript 1/2 \u306e\u5b9f\u884c\u3092\u8a66\u307f\u307e\u3059
config.name.warning.hexViewNotUpToDate = 16\u9032\u6570\u8868\u793a\u304c\u6700\u65b0\u3067\u306a\u3044\u5834\u5408\u306b\u8b66\u544a\u3092\u8868\u793a\u3059\u308b
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = \u00a7\u00a7dup \u547d\u4ee4\u3092\u8868\u793a\u3059\u308b
# config.description.displayDupInstructions = \u30b3\u30fc\u30c9\u306b \u00a7\u00a7dup \u547d\u4ee4\u3092\u8868\u793a\u3057\u307e\u3059\u3002\u3053\u308c\u304c\u306a\u3044\u3068\u30b3\u30f3\u30d1\u30a4\u30eb\u306f\u5bb9\u6613\u3067\u3059\u304c\u3001\u526f\u4f5c\u7528\u306e\u3042\u308bdupped\u30b3\u30fc\u30c9\u304c2\u56de\u5b9f\u884c\u3055\u308c\u308b\u53ef\u80fd\u6027\u304c\u3042\u308a\u307e\u3059
config.name.useRegExprLiteral = \u6b63\u898f\u8868\u73fe\u3092 /pattern/mod \u69cb\u6587\u3067\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3059\u308b
config.description.useRegExprLiteral = \u6b63\u898f\u8868\u73fe\u3092\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3059\u308b\u3068\u304d\u306b\u3001/pattern/mod \u69cb\u6587\u3092\u4f7f\u7528\u3057\u307e\u3059\u3002\u30aa\u30d5\u306e\u5834\u5408\u306f\u3001\u65b0\u3057\u3044 RegExp("pat","mod") \u304c\u4f7f\u7528\u3055\u308c\u307e\u3059
config.name.handleSkinPartsAutomatically = [SkinPart] \u30e1\u30bf\u30c7\u30fc\u30bf\u3092\u81ea\u52d5\u7684\u306b\u51e6\u7406\u3059\u308b
config.description.handleSkinPartsAutomatically = [SkinPart] \u30e1\u30bf\u30c7\u30fc\u30bf\u3092\u81ea\u52d5\u7684\u306b\u9006\u30b3\u30f3\u30d1\u30a4\u30eb\u3057\u3066\u76f4\u63a5\u7de8\u96c6\u3057\u307e\u3059\u3002\u30aa\u30d5\u306b\u3059\u308b\u3068\u3001_skinParts \u5c5e\u6027\u3068\u305d\u306e getter \u30e1\u30bd\u30c3\u30c9\u304c\u8868\u793a\u3055\u308c\u3001\u624b\u52d5\u3067\u7de8\u96c6\u3067\u304d\u308b\u3088\u3046\u306b\u307e\u3059
config.name.simplifyExpressions = \u5f0f\u3092\u7c21\u7565\u5316\u3059\u308b
config.description.simplifyExpressions = \u30b3\u30fc\u30c9\u3092\u8aad\u307f\u3084\u3059\u304f\u3059\u308b\u305f\u3081\u306b\u3001\u5f0f\u3092\u8a55\u4fa1\u304a\u3088\u3073\u7c21\u7565\u5316\u3057\u307e\u3059
config.name.resetLetterSpacingOnTextImport = \u30c6\u30ad\u30b9\u30c8\u306e\u30a4\u30f3\u30dd\u30fc\u30c8\u6642\u306b\u6587\u5b57\u306e\u9593\u9694\u3092\u30ea\u30bb\u30c3\u30c8\u3059\u308b
config.description.resetLetterSpacingOnTextImport = \u30ad\u30ea\u30eb\u6587\u5b57\u306e\u30d5\u30a9\u30f3\u30c8\u306f\u5e45\u304c\u5e83\u3044\u305f\u3081\u3001\u4fbf\u5229\u3067\u3059
config.name.flexSdkLocation = 4) Flex SDK \u306e\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u30d1\u30b9
config.description.flexSdkLocation = Adobe Flex SDK \u306e\u5834\u6240\u3002\u4e3b\u306b AS3 \u306e\u30b3\u30f3\u30d1\u30a4\u30eb\u306b\u4f7f\u7528\u3057\u307e\u3059
config.name.useFlexAs3Compiler=Flex SDK AS3 \u30b3\u30f3\u30d1\u30a4\u30e9\u3092\u4f7f\u7528\u3059\u308b
config.description.useFlexAs3Compiler=ActionScript \u306e\u76f4\u63a5\u7de8\u96c6\u6642\u306b\u3001Flex SDK \u306e AS3 \u30b3\u30f3\u30d1\u30a4\u30e9\u3092\u4f7f\u7528\u3057\u307e\u3059 (Flex SDK \u306e\u30c7\u30a3\u30ec\u30af\u30c8\u30ea\u30d1\u30b9\u3092\u8a2d\u5b9a\u3059\u308b\u5fc5\u8981\u304c\u3042\u308a\u307e\u3059)
config.name.showSetAdvanceValuesMessage = \u30a2\u30c9\u30d0\u30f3\u30b9\u5024\u306e\u8a2d\u5b9a\u306b\u95a2\u3059\u308b\u60c5\u5831\u3092\u8868\u793a\u3059\u308b
config.description.showSetAdvanceValuesMessage = \u300c\u30a2\u30c9\u30d0\u30f3\u30b9\u5024\u306e\u8a2d\u5b9a\u300d\u306b\u95a2\u3059\u308b\u60c5\u5831\u3092\u8868\u793a\u3057\u307e\u3059
config.name.gui.fontSizeMultiplier = \u30d5\u30a9\u30f3\u30c8\u30b5\u30a4\u30ba\u306e\u500d\u7387
config.description.gui.fontSizeMultiplier = \u30d5\u30a9\u30f3\u30c8\u30b5\u30a4\u30ba\u306e\u500d\u7387
config.name.graphVizDotLocation = 5) GraphViz dot.exe \u306e\u30d1\u30b9
config.description.graphVizDotLocation = \u30b0\u30e9\u30d5\u3092\u8868\u793a\u3059\u308b\u305f\u3081\u306e GraphViz \u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u306e\u3001dot.exe (linux\u306e\u5834\u5408\u306f\u305d\u308c\u306b\u8fd1\u3044\u3082\u306e) \u3078\u306e\u30d1\u30b9
#Do not translate the Font Styles which is in the parenthesis:(Plain,Bold,Italic,BoldItalic)
config.name.gui.sourceFont = \u30bd\u30fc\u30b9\u30b3\u30fc\u30c9\u306e\u30d5\u30a9\u30f3\u30c8
config.description.gui.sourceFont = \u30d5\u30a9\u30f3\u30c8\u540d-\u30d5\u30a9\u30f3\u30c8\u30b9\u30bf\u30a4\u30eb(Plain,Bold,Italic,BoldItalic)-\u30d5\u30a9\u30f3\u30c8\u30b5\u30a4\u30ba
#after 11.1.0
config.name.as12DeobfuscatorExecutionLimit=AS1/2 \u306e\u96e3\u8aad\u5316\u89e3\u9664\u306e\u5b9f\u884c\u5236\u9650
config.description.as12DeobfuscatorExecutionLimit=AS1/2 \u5b9f\u884c\u306e\u96e3\u8aad\u5316\u89e3\u9664\u4e2d\u306b\u51e6\u7406\u3055\u308c\u308b\u547d\u4ee4\u306e\u6700\u5927\u6570
#option that ignore in 8.0.1 and other versions
config.name.showOriginalBytesInPcodeHex = \uff08\u5185\u90e8\uff09\u30aa\u30ea\u30b8\u30ca\u30eb\u306e\u30d0\u30a4\u30c8\u3092\u8868\u793a\u3059\u308b
config.description.showOriginalBytesInPcodeHex = P-code 16\u9032\u6570\u3067\u3001\u30aa\u30ea\u30b8\u30ca\u30eb\u306e\u30d0\u30a4\u30c8\u3092\u8868\u793a\u3057\u307e\u3059
config.name.showFileOffsetInPcodeHex = \uff08\u5185\u90e8\uff09\u30d5\u30a1\u30a4\u30eb\u306e\u30aa\u30d5\u30bb\u30c3\u30c8\u3092\u8868\u793a\u3059\u308b
config.description.showFileOffsetInPcodeHex = P-code 16\u9032\u6570\u3067\u3001\u30d5\u30a1\u30a4\u30eb\u306e\u30aa\u30d5\u30bb\u30c3\u30c8\u3092\u8868\u793a\u3057\u307e\u3059
config.name._enableFlexExport=\uff08\u5185\u90e8\uff09Flex \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3092\u6709\u52b9\u306b\u3059\u308b
config.description.enableFlexExport = Flex \u30a8\u30af\u30b9\u30dd\u30fc\u30c8\u3092\u6709\u52b9\u306b\u3057\u307e\u3059
config.name._ignoreAdditionalFlexClasses=\uff08\u5185\u90e8\uff09\u8ffd\u52a0\u306e Flex \u30af\u30e9\u30b9\u3092\u7121\u8996\u3059\u308b
config.description.ignoreAdditionalFlexClasses = \u8ffd\u52a0\u306e Flex \u30af\u30e9\u30b9\u3092\u7121\u8996\u3057\u307e\u3059
config.name.hwAcceleratedGraphics = 
config.description.hwAcceleratedGraphics = 
config.name.gui.avm2.splitPane.docs.dividerLocationPercent=\uff08\u5185\u90e8\uff09splitPanedocsdividerLocationPercent
config.description.gui.avm2.splitPane.docs.dividerLocationPercent=splitPane docs divider Location Percent
config.name.gui.dump.splitPane.dividerLocationPercent = \uff08\u5185\u90e8\uff09dumpsplitPanedividerLocationPercent
config.description.gui.dump.splitPane.dividerLocationPercent = dump splitPane divider Location Percent
#after 11.3.0
config.name.useAdobeFlashPlayerForPreviews = \uff08\u975e\u63a8\u5968\uff09\u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u306e\u30d7\u30ec\u30d3\u30e5\u30fc\u306b Adobe Flash Player \u3092\u4f7f\u7528\u3059\u308b
config.description.useAdobeFlashPlayerForPreviews = \u30aa\u30d6\u30b8\u30a7\u30af\u30c8\u306e\u30d7\u30ec\u30d3\u30e5\u30fc\u306b Adobe Flash Player \u3092\u4f7f\u7528\u3057\u307e\u3059\u3002\u8b66\u544a: Flash Player \u306f 2021/01/12 \u306b\u5ec3\u6b62\u3055\u308c\u307e\u3057\u305f
#after 12.0.1
config.name.showLineNumbersInPCodeGraphvizGraph = Graphviz \u30b0\u30e9\u30d5\u306b\u884c\u756a\u53f7\u3092\u8868\u793a\u3059\u308b
config.description.showLineNumbersInPCodeGraphvizGraph = P-code Graphviz \u30b0\u30e9\u30d5\u306b\u884c\u756a\u53f7\u3092\u8868\u793a\u3057\u307e\u3059
config.name.padAs3PCodeInstructionName=AS3 P-code \u306e\u547d\u4ee4\u540d\u3092\u30b9\u30da\u30fc\u30b9\u3067\u57cb\u3081\u308b
config.description.padAs3PCodeInstructionName=AS3 P-code \u306e\u547d\u4ee4\u540d\u3092\u30b9\u30da\u30fc\u30b9\u3067\u57cb\u3081\u307e\u3059
#after 13.0.2
config.name.indentAs3PCode=AS3 P-code \u3092\u30a4\u30f3\u30c7\u30f3\u30c8\u3059\u308b
config.description.indentAs3PCode=trait / body / code \u306a\u3069\u306e AS3 P-code \u30d6\u30ed\u30c3\u30af\u3092\u30a4\u30f3\u30c7\u30f3\u30c8\u3057\u307e\u3059
config.name.labelOnSeparateLineAs3PCode=AS3 P-code \u306e\u30e9\u30d9\u30eb\u3092\u5225\u306e\u884c\u306b\u8868\u793a\u3059\u308b
config.description.labelOnSeparateLineAs3PCode=AS3 P-code \u306e\u30e9\u30d9\u30eb\u3092\u3001\u5225\u306e\u884c\u306b\u8868\u793a\u3057\u307e\u3059
config.name.useOldStyleGetSetLocalsAs3PCode=AS3 P-code\u3067\u3001getlocalx \u306e\u4ee3\u308f\u308a\u306b\u3001\u53e4\u3044\u30b9\u30bf\u30a4\u30eb\u306e getlocal_x \u3092\u4f7f\u7528\u3059\u308b
config.description.useOldStyleGetSetLocalsAs3PCode=FFDec 12.x \u4ee5\u524d\u306e\u53e4\u3044\u30b9\u30bf\u30a4\u30eb\u3067\u3042\u308b\u3001getlocal_x, setlocal_x \u3092\u4f7f\u7528\u3057\u307e\u3059
config.name.useOldStyleLookupSwitchAs3PCode=AS3 P-code\u3067\u3001\u62ec\u5f27\u306a\u3057\u306e\u53e4\u3044\u30b9\u30bf\u30a4\u30eb\u3067\u3042\u308b lookupswitch \u3092\u4f7f\u7528\u3059\u308b
config.description.useOldStyleLookupSwitchAs3PCode=FFDec 12.x \u4ee5\u524d\u306e\u53e4\u3044\u30b9\u30bf\u30a4\u30eb\u3067\u3042\u308b\u3001lookupswitch \u3092\u4f7f\u7528\u3057\u307e\u3059
#after 13.0.3
config.name.checkForModifications = FFDec \u5916\u306b\u3088\u308b\u30d5\u30a1\u30a4\u30eb\u306e\u5909\u66f4\u3092\u30c1\u30a7\u30c3\u30af\u3059\u308b
config.description.checkForModifications = \u4ed6\u306e\u30a2\u30d7\u30ea\u30b1\u30fc\u30b7\u30e7\u30f3\u306b\u3088\u308b\u30d5\u30a1\u30a4\u30eb\u306e\u5909\u66f4\u3092\u30c1\u30a7\u30c3\u30af\u3057\u3001\u518d\u8aad\u307f\u8fbc\u307f\u3092\u6c42\u3081\u307e\u3059
config.name.warning.initializers = \u30a4\u30cb\u30b7\u30e3\u30e9\u30a4\u30b6\u306b\u95a2\u3059\u308b AS3 \u306e slot / const \u306e\u7de8\u96c6\u306b\u3064\u3044\u3066\u8b66\u544a\u3059\u308b
config.description.warning.initializers = \u30a4\u30cb\u30b7\u30e3\u30e9\u30a4\u30b6\u306b\u95a2\u3059\u308b\u3001AS3 \u306e slot / const \u306e\u7de8\u96c6\u306b\u3064\u3044\u3066\u3001\u8b66\u544a\u3092\u8868\u793a\u3057\u307e\u3059
config.name.parametersPanelInSearchResults = \u691c\u7d22\u7d50\u679c\u306b\u30d1\u30e9\u30e1\u30fc\u30bf\u30d1\u30cd\u30eb\u3092\u8868\u793a\u3059\u308b
config.description.parametersPanelInSearchResults = \u691c\u7d22\u7d50\u679c\u30a6\u30a3\u30f3\u30c9\u30a6\u306b\u3001\u691c\u7d22\u30c6\u30ad\u30b9\u30c8 / \u5927\u6587\u5b57\u3068\u5c0f\u6587\u5b57\u3092\u533a\u5225\u3057\u306a\u3044 / \u6b63\u898f\u8868\u73fe \u306a\u3069\u306e\u30d1\u30e9\u30e1\u30fc\u30bf\u3092\u8868\u793a\u3057\u307e\u3059
config.name.displayAs3PCodeDocsPanel=AS3 P-code \u306b\u3001\u30c9\u30ad\u30e5\u30e1\u30f3\u30c8\u30d1\u30cd\u30eb\u3092\u8868\u793a\u3059\u308b
config.description.displayAs3PCodeDocsPanel=AS3 P-code \u306e\u7de8\u96c6\u3068\u8868\u793a\u306b\u304a\u3051\u308b\u3001\u547d\u4ee4\u3068\u30b3\u30fc\u30c9\u69cb\u9020\u306e\u30c9\u30ad\u30e5\u30e1\u30f3\u30c8\u3092\u8868\u793a\u3057\u307e\u3059
config.name.displayAs3TraitsListAndConstantsPanel=AS3 \u306e\u3001trait \u4e00\u89a7\u3068\u5b9a\u6570\u306e\u30d1\u30cd\u30eb\u3092\u8868\u793a\u3059\u308b
config.description.displayAs3TraitsListAndConstantsPanel=AS3 \u306e\u30bf\u30b0\u30c4\u30ea\u30fc\u306e\u4e0b\u306b\u3001trait \u3068\u5b9a\u6570\u306e\u4e00\u89a7\u3092\u8868\u793a\u3057\u307e\u3059
config.name.useAsTypeIcons = \u9805\u76ee\u306e\u7a2e\u985e\u306b\u5fdc\u3058\u305f\u30b9\u30af\u30ea\u30d7\u30c8\u306e\u30a2\u30a4\u30b3\u30f3\u3092\u4f7f\u7528\u3059\u308b
config.description.useAsTypeIcons = \u30b9\u30af\u30ea\u30d7\u30c8\u306e\u7a2e\u985e (class/interface/frame/...) \u306b\u5fdc\u3058\u3066\u3001\u7570\u306a\u308b\u30a2\u30a4\u30b3\u30f3\u3092\u4f7f\u7528\u3057\u307e\u3059
config.name.limitAs3PCodeOffsetMatching=AS3 P-code \u306e\u30aa\u30d5\u30bb\u30c3\u30c8 \u30de\u30c3\u30c1\u30f3\u30b0\u306e\u5236\u9650
config.description.limitAs3PCodeOffsetMatching=AS3 \u30b9\u30af\u30ea\u30d7\u30c8\u306b\u30aa\u30d5\u30bb\u30c3\u30c8\u4e00\u81f4\u3059\u308b\u3001AS3 P-code \u306e\u547d\u4ee4\u6570\u306e\u4e0a\u9650
config.name.showSlowRenderingWarning = \u30ec\u30f3\u30c0\u30ea\u30f3\u30b0\u304c\u9045\u3044\u5834\u5408\u306b\u8b66\u544a\u3092\u8a18\u9332\u3059\u308b
config.description.showSlowRenderingWarning = \u5185\u8535\u30d5\u30e9\u30c3\u30b7\u30e5\u30d3\u30e5\u30fc\u30a2\u306e\u8868\u793a\u901f\u5ea6\u304c\u9045\u3044\u5834\u5408\u306b\u3001\u8b66\u544a\u3092\u30ed\u30b0\u306b\u8a18\u9332\u3057\u307e\u3059
config.name.autoCloseQuotes = \u30b9\u30af\u30ea\u30d7\u30c8\u7de8\u96c6\u6642\u306b\u3001\u5358\u4e00\u5f15\u7528\u7b26\u3092\u81ea\u52d5\u7684\u306b\u9589\u3058\u308b
config.description.autoCloseQuotes = \u5358\u4e00\u5f15\u7528\u7b26 ' \u3092\u5165\u529b\u3059\u308b\u3068\u3001\u81ea\u52d5\u7684\u306b\u3001\u5358\u4e00\u5f15\u7528\u7b26\u3092\u3082\u3046\u4e00\u3064\u633f\u5165\u3057\u307e\u3059
config.name.autoCloseDoubleQuotes = \u30b9\u30af\u30ea\u30d7\u30c8\u7de8\u96c6\u6642\u306b\u3001\u4e8c\u91cd\u5f15\u7528\u7b26\u3092\u81ea\u52d5\u7684\u306b\u9589\u3058\u308b
config.description.autoCloseDoubleQuotes = \u4e8c\u91cd\u5f15\u7528\u7b26 " \u3092\u5165\u529b\u3059\u308b\u3068\u3001\u81ea\u52d5\u7684\u306b\u3001\u4e8c\u91cd\u5f15\u7528\u7b26\u3092\u3082\u3046\u4e00\u3064\u633f\u5165\u3057\u307e\u3059
config.name.autoCloseBrackets = \u30b9\u30af\u30ea\u30d7\u30c8\u7de8\u96c6\u6642\u306b\u3001\u89d2\u62ec\u5f27\u3092\u81ea\u52d5\u7684\u306b\u9589\u3058\u308b
config.description.autoCloseBrackets = \u89d2\u62ec\u5f27 \u958b\u304d [ \u3092\u5165\u529b\u3059\u308b\u3068\u3001\u81ea\u52d5\u7684\u306b \u89d2\u62ec\u5f27 \u9589\u3058 ] \u3092\u633f\u5165\u3057\u307e\u3059
config.name.autoCloseParenthesis = \u30b9\u30af\u30ea\u30d7\u30c8\u7de8\u96c6\u6642\u306b\u3001\u4e38\u62ec\u5f27\u3092\u81ea\u52d5\u7684\u306b\u9589\u3058\u308b
config.description.autoCloseParenthesis = \u4e38\u62ec\u5f27 \u958b\u304d ( \u3092\u5165\u529b\u3059\u308b\u3068\u3001\u81ea\u52d5\u7684\u306b \u4e38\u62ec\u5f27 \u9589\u3058 ) \u3092\u633f\u5165\u3057\u307e\u3059
config.name.showDialogOnError = \u3059\u3079\u3066\u306e\u30a8\u30e9\u30fc\u3067\u3001\u30a8\u30e9\u30fc\u30c0\u30a4\u30a2\u30ed\u30b0\u3092\u8868\u793a\u3059\u308b
config.description.showDialogOnError = \u30a8\u30e9\u30fc\u304c\u767a\u751f\u3059\u308b\u305f\u3073\u306b\u3001\u81ea\u52d5\u7684\u306b\u30a8\u30e9\u30fc\u30c0\u30a4\u30a2\u30ed\u30b0\u3092\u8868\u793a\u3057\u307e\u3059
