# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%available% of %total% characters)
dialog.title = Font embedding
label.individual = Individual characters:
button.loadfont = Load font from disk...
filter.ttf = True Type Font files (*.ttf)
error.invalidfontfile = Invalid font file
error.cannotreadfontfile = Cannot read font file
installed = Installed: 
ttffile.noselection = TTF file: <select>
ttffile.selection = TTF file: %fontname% (%filename%)
allcharacters = All characters (%available% characters)
#after 14.0.0
ascentdescentleading = Set ascent, descent and leading
#after 19.1.2
font.name = Font name:
font.name.default = My font
font.source = Source: