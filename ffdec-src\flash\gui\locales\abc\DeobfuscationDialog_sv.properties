# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Bearbeta alla klasser
dialog.title = Avl\u00e4gsna obfuskerad Bytekod
deobfuscation.level = Avl\u00e4gsnad obfuskering, kodniv\u00e5:
deobfuscation.removedeadcode = Ta bort d\u00f6d kod
deobfuscation.removetraps = Ta bort f\u00e4llor
deobfuscation.restorecontrolflow = \u00c5terst\u00e4ll styrfl\u00f6de
button.ok = Godk\u00e4nn
button.cancel = Avbryt
