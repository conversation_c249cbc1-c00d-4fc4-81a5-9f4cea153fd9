# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%available% de %total% caracteres)
dialog.title = Fonte embarcada
label.individual = Caractere individual:
button.loadfont = Carregar fonte do disco...
filter.ttf = Arquivos True Type Font (*.ttf)
error.invalidfontfile = Arquivo de fonte inv\u00e1lido
error.cannotreadfontfile = N\u00e3o foi poss\u00edvel ler o arquivo de fonte
installed = Instalada: 
ttffile.noselection = Arquivo TTF: <selecione>
ttffile.selection = Arquivo TTF: %fontname% (%filename%)
allcharacters = Todos os caracteres (%available% caracteres)
#after 14.0.0
ascentdescentleading = Definir ascens\u00e3o, descida e espa\u00e7amento
#after 19.1.2
font.name = Nome da fonte:
font.name.default = Minha fonte
font.source = Fonte:
