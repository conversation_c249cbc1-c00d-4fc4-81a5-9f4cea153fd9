# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
version = verzi\u00f3
releasedate = Kiad\u00e1si d\u00e1tum:
newversionavailable = \u00daj verzi\u00f3 el\u00e9rhet\u0151:
changeslog = V\u00e1ltoz\u00e1snapl\u00f3:
downloadnow = Let\u00f6lti most?
button.ok = OK
button.cancel = M\u00e9gse
dialog.title = \u00daj verzi\u00f3 el\u00e9rhet\u0151
newversion = \u00daj verzi\u00f3
newvermessage = Az %oldAppName% \u00faj verzi\u00f3ja el\u00e9rhet\u0151: %newAppName%.\r\nK\u00e9rem l\u00e1togassa meg a %projectPage% oldalt a let\u00f6lt\u00e9shez.
#change this only when the date format is wrong in the changelog
#you can use any java date format string, e.g: yyyy.MM.dd
customDateFormat = default
