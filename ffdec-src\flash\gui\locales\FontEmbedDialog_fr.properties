# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%available% sur %total% caract\u00e8res)
dialog.title = Police incorpor\u00e9e
label.individual = Caract\u00e8re personnalis\u00e9:
button.loadfont = Charger les polices de caract\u00e8res \u00e0 partir du disque...
filter.ttf = Fichier de police True Type (*.ttf)
error.invalidfontfile = Fichier de police invalide
error.cannotreadfontfile = Impossible de lire le fichier de police
installed = Install\u00e8: 
ttffile.noselection = fichier TTF : <select>
ttffile.selection = fichier TTF : %fontname% (%filename%)
allcharacters = Tous les caract\u00e8res (caract\u00e8res %available%)
