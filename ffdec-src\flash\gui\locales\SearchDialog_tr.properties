# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
checkbox.ignorecase = Yoksayma durumu
checkbox.regexp = D\u00fczenli ifade
button.ok = TAMAM
button.cancel = \u0130ptal
label.searchtext = <PERSON><PERSON> metni:
label.replacementtext = De\u011fi\u015ftirme metni:
#dialog.title = ActionScript search
dialog.title = Metin ara
dialog.title.replace = Metin de\u011fi\u015ftir
error = Hata
error.invalidregexp = Ge\u00e7ersiz kal\u0131p
checkbox.searchText = Metinlerde ara
checkbox.searchAS = AS i\u00e7inde ara
checkbox.replaceInParameters = Parametrelerde de\u011fi\u015ftir
checkbox.searchPCode = P Kodunda ara
#after 13.0.3
label.scope = Kapsam:
scope.currentFile = Ge\u00e7erli SWF
scope.selection = Se\u00e7im (%selection%)
scope.allFiles = A\u00e7\u0131lan t\u00fcm SWF'ler
scope.selection.items = %count% \u00f6\u011fe
#after 16.3.1
scope.currentFile.abc = Ge\u00e7erli ABC
