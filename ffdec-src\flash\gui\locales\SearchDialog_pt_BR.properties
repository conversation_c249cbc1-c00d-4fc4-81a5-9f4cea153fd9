# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
checkbox.ignorecase = Ignorar mai\u00fasculas e min\u00fasculas
checkbox.regexp = Express\u00e3o regular
button.ok = OK
button.cancel = Cancelar
label.searchtext = Texto de pesquisa:
label.replacementtext = Texto de substitui\u00e7\u00e3o:
#dialog.title = ActionScript search
dialog.title = Pesquisa de texto
dialog.title.replace = Substitui\u00e7\u00e3o de texto
error = Erro
error.invalidregexp = Padr\u00e3o inv\u00e1lido
checkbox.searchText = Pesquisar nos textos
checkbox.searchAS = Pesquisar no AS
checkbox.replaceInParameters = Substituir nos par\u00e2metros
checkbox.searchPCode = Pesquisar no P-Code
#after 13.0.3
label.scope = Escopo:
scope.currentFile = SWF atual
scope.selection = Sele\u00e7\u00e3o (%selection%)
scope.allFiles = Todos os SWFs abertos
scope.selection.items = %count% itens
#after 16.3.1
scope.currentFile.abc = ABC atual
