# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Vormen
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5-canvas
shapes.swf = SWF
texts = Teksten
texts.plain = Platte tekst
texts.formatted = Geformatteerde tekst
texts.svg = SVG
images = Images
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Films
movies.flv = FLV (Geen audio)
sounds = Geluiden
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (Alleen audio)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Scripts
scripts.as = ActionScript
scripts.pcode = P-code
scripts.pcode_hex=P-code met Hex
scripts.hex = Hex
scripts.constants = Constanten
scripts.as_method_stubs=ActionScript-methode stubs
scripts.pcode_graphviz=P-code GraphViz
binaryData = Binaire data
binaryData.raw = Raw
dialog.title = Exporteren...
button.ok = OK
button.cancel = Annuleren
morphshapes = Morphvormen
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5-canvas
morphshapes.swf = SWF
morphshapes.bmp_start_end=BMP (begin, einde)
morphshapes.png_start_end=PNG (begin, einde)
morphshapes.svg_start_end=SVG (begin, einde)
frames = Frames
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5-canvas
frames.pdf = PDF
frames.bmp = BMP
frames.swf = SWF
sprites = Sprites
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = HTML5-canvas
sprites.pdf = PDF
sprites.bmp = BMP
sprites.swf = SWF
buttons = Knoppen
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
buttons.swf = SWF
fonts = Lettertypen
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Zoom
zoom.percent = %
zoom.invalid = Ongeldige zoomwaarde.
symbolclass = Toewijzing van symboolklassen
symbolclass.csv = CSV
#after 18.0.0
images.png_gif_jpeg_alpha=PNG/GIF/JPEG+alpha
#after 18.5.0
fonts4=DefineFont4
fonts4.cff=CFF
embed = Ingesloten objectenen exporteren via [Embed]
