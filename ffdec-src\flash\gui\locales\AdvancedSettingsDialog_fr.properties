# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Param\u00e8tres avanc\u00e9s
advancedSettings.restartConfirmation = Vous devez red\u00e9marrer le programme pour que les changements prennent effet. Red\u00e9marrer maintenant ?
advancedSettings.columns.name = Nom
advancedSettings.columns.value = Valeur
advancedSettings.columns.description = Description
default = D\u00e9faut
config.group.name.export = Export
config.group.description.export = Configuration de l'export
config.group.name.script = Scripts
config.group.description.script = D\u00e9compilation ActionScript connexes
config.group.name.update = Mises \u00e0 jour
config.group.description.update = Recherche de mises \u00e0 jour
config.group.name.format = Mise en forme
config.group.description.format = Mise en forme du code ActionScript
config.group.name.limit = Limites
config.group.description.limit = Limites de la d\u00e9compilation du code crypt\u00e9, etc.
config.group.name.ui = Interface
config.group.description.ui = Configuration de l'interface utilisateur
config.group.name.debug = D\u00e9boguage
config.group.description.debug = Param\u00e8tres de d\u00e9boguage
config.group.name.display = Affichage
config.group.description.display = Affichage des objets Flash, etc.
config.group.name.decompilation = D\u00e9compilation
config.group.description.decompilation = Fonctions relatives \u00e0 la d\u00e9compilation
config.group.name.other = Autres
config.group.description.other = Autres configurations
config.name.openMultipleFiles = Ouvrir plusieurs fichiers
config.description.openMultipleFiles = Autoriser l'ouverture de plusieurs fichiers dans une seule fen\u00eatre
config.name.decompile = Afficher les sources ActionScript
config.description.decompile = Vous pouvez d\u00e9sactiver la d\u00e9compilation ActionScript, seul le code Assembleur sera affich\u00e9
config.name.dumpView = Donn\u00e9es brutes
config.description.dumpView = Voir les donn\u00e9es brutes
config.name.useHexColorFormat = Format couleur hexad\u00e9cimal
config.description.useHexColorFormat = Afficher les couleurs au format hexad\u00e9cimal
config.name.parallelSpeedUp = Acc\u00e9l\u00e9ration parall\u00e8le
config.description.parallelSpeedUp = Le parall\u00e9lisme augmente la vitesse de d\u00e9compilation
config.name.parallelSpeedUpThreadCount = Nombre de canaux (0 = auto)
config.description.parallelSpeedUpThreadCount = Nombre de canaux \u00e0 utiliser pour l'acc\u00e9l\u00e9ration parall\u00e8le. 0 = processor count - 1.
config.name.autoDeobfuscate = D\u00e9sobfuscation automatique
config.description.autoDeobfuscate = D\u00e9marer la d\u00e9sobfuscation sur l'ensemble des fichiers avant la d\u00e9compilation ActionScript
config.name.cacheOnDisk = Utilisation de la m\u00e9moire cache du disque
config.description.cacheOnDisk = Des parties sont d\u00e9j\u00e0 d\u00e9compil\u00e9s sur le disque dur \u00e0 la place de la m\u00e9moire
config.name.internalFlashViewer = Utiliser la visionneuse Flash int\u00e9gr\u00e9e
config.description.internalFlashViewer = Utililser la visionneuse Flash JPEXS au lieu de la visionneuse Flash standard pour afficher les \u00e9l\u00e9ments
config.name.gotoMainClassOnStartup = Aller \u00e0 la classe d'initialisation (AS3)
config.description.gotoMainClassOnStartup = Aller dans la classe de document \u00e0 l'ouverture du fichier AS3
config.name.autoRenameIdentifiers = Renommage automatique des identifiants
config.description.autoRenameIdentifiers = Renomme automatiquement les identifiants invalides en chargeant le fichier SWF
config.name.offeredAssociation = (Interne) Association de fichiers SWF d\u00e9j\u00e0 ouvert
config.description.offeredAssociation = La boite de dialogue d'association de fichiers est d\u00e9j\u00e0 ouverte
config.name.decimalAddress = Utiliser l'adressage d\u00e9cimale
config.description.decimalAddress = Utiliser l'adressage d\u00e9cimale au lieu de l'adressage hexad\u00e9cimal
config.name.showAllAddresses = Afficher toutes les adresses
config.description.showAllAddresses = Afficher toutes les instructions ActionScript \u00e0 toutes les adresses
config.name.useFrameCache = Utiliser la m\u00e9moire cache des images
config.description.useFrameCache = Mettre en cache les images avant le nouveau rendu
config.name.useRibbonInterface = Interface Ruban
config.description.useRibbonInterface = D\u00e9cocher pour utiliser l'interface classique sans le menu-ruban
config.name.openFolderAfterFlaExport = Ouvrir le dossier apr\u00e8s l'export FLA
config.description.openFolderAfterFlaExport = Afficher le dossier apr\u00e8s l'export de fichiers FLA
config.name.useDetailedLogging = Journal d\u00e9taill\u00e9
config.description.useDetailedLogging = Les messages d'erreurs et les informations de d\u00e9bogages sont renseign\u00e9s dans le journal d\u00e9taill\u00e9
config.name.resolveConstants = R\u00e9soudre les constantes dans le code assembleur en AS1/2
config.description.resolveConstants = Arr\u00eate d'afficher 'constantxx' au lieu des vraies valeurs dans la fen\u00eatre assembleur
config.name.sublimiter = Limites du sous-programme
config.description.sublimiter = Limites du sous-programme pour du code crypt\u00e9
config.name.exportTimeout = Limite de temps \u00e9coul\u00e9e \u00e0 l'exportation (secondes)
config.description.exportTimeout = Le d\u00e9compileur arr\u00eatera l'exportation d\u00e8s que cette valeur sera atteinte
config.name.decompilationTimeoutFile = Limite de temps pour d\u00e9compiler un fichier (secondes)
config.description.decompilationTimeoutFile = Le d\u00e9compileur arr\u00eatera la d\u00e9compilation ActionScript d\u00e8s que cette valeur sera atteinte pour un fichier
config.name.paramNamesEnable = Activer les noms de param\u00e8tres en AS3
config.description.paramNamesEnable = L'utilisation des noms de param\u00e8tres en d\u00e9compilant peut causer des probl\u00e8mes parce que le programme officiel CS 5.5 pr\u00e9f\u00e8re Flash ins\u00e8rer des mauvais indices de noms de param\u00e9tres
config.name.displayFileName = Afficher le nom SWF dans la barre de titre
config.description.displayFileName = Afficher l'URL et le nom de fichier dans la barre de titre (Vous pouvez faire des captures d'\u00e9cran)
config.name.dumpTags = Afficher les \u00e9tiquettes dans la console
config.description.dumpTags = Afficher les \u00e9tiquettes dans la console en jouant le fichier SWF
config.name.decompilationTimeoutSingleMethod = AS3: Single method decompilation timeout (secondes)
config.description.decompilationTimeoutSingleMethod = Le d\u00e9compilateur arr\u00eatera la d\u00e9compilation du code ActionScript d\u00e8 que ce temps sera atteint dans la m\u00e9thode
config.name.lastRenameType = (Interne) Dernier type renomm\u00e9
config.description.lastRenameType = Dernier type d'identificateur renomm\u00e9 utilis\u00e9
config.name.lastSaveDir = (Interne) Dernier dossier de sauvegarde
config.description.lastSaveDir = Dernier dossier de sauvegarde utilis\u00e9
config.name.lastOpenDir = (Interne) Dernier dossier ouvert
config.description.lastOpenDir = Dernier dossier ouvert utilis\u00e9
config.name.lastExportDir = (Interne) Dernier dossier d'export
config.description.lastExportDir = Dernier dossier d'export utilis\u00e9
config.name.locale = Langues
config.description.locale = Param\u00e8tres r\u00e9gionaux
config.name.registerNameFormat = Format du registre des variables
config.description.registerNameFormat = Format du registre local des noms de variables. Utilise %d pour le registre num\u00e9rique.
config.name.maxRecentFileCount = Nb. max. de fichiers r\u00e9cents
config.description.maxRecentFileCount = Nombre maximal de fichiers r\u00e9cents
config.name.recentFiles = (Interne) Fichiers r\u00e9cents
config.description.recentFiles = Fichiers r\u00e9cemment ouverts
config.name.fontPairingMap = (Interne) Couplage des polices pour l'importation
config.description.fontPairingMap = Couplage des polices pour l'importation des nouveaux caract\u00e8res
config.name.lastUpdatesCheckDate = (Interne) Date de derni\u00e8re mises \u00e0 jour
config.description.lastUpdatesCheckDate = Date de la derni\u00e8re v\u00e9rification des mises \u00e0 jour sur le serveur
config.name.gui.window.width = (Interne) Derni\u00e8re largeur de fen\u00eatre
config.description.gui.window.width = Derni\u00e8re valeur enregistr\u00e9e de la largeur de la fen\u00eatre
config.name.gui.window.height = (Interne) Derni\u00e8re hauteur de fen\u00eatre
config.description.gui.window.height = Derni\u00e8re valeur enregistr\u00e9e de la hauteur de la fen\u00eatre
config.name.gui.window.maximized.horizontal = (Interne) Agrandissement horizontal de la fen\u00eatre
config.description.gui.window.maximized.horizontal = Dernier \u00e9tat de la fen\u00eatre - Agrandissement horizontal
config.name.gui.window.maximized.vertical = (Interne) Agrandissement vertical de la fen\u00eatre
config.description.gui.window.maximized.vertical = Derni\u00e8r \u00e9tat de la fen\u00eatre - Agrandissement vertical
config.name.gui.avm2.splitPane.dividerLocationPercent=(Interne) Emplacement du s\u00e9parateur AS3
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Interne) Emplacement du s\u00e9parateur AS1/2
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Interne) Aper\u00e7u de l'emplacement du s\u00e9parateur
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Interne) Emplacement du s\u00e9parateur 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Interne) Emplacement du s\u00e9parateur 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Enregistrer en tant qu'\u00e9chelle de mode EXE
config.description.saveAsExeScaleMode = Mode de mise \u00e0 l'\u00e9chelle pour un export EXE
config.name.syntaxHighlightLimit = Nb max de caract\u00e8res surlign\u00e9s
config.description.syntaxHighlightLimit = Nombre maximal de caract\u00e8res pour surligner
config.name.guiFontPreviewSampleText = (Internal) Dernier aper\u00e7u texte de la police de caract\u00e8res
config.description.guiFontPreviewSampleText = Dernier aper\u00e7u texte de la police de caract\u00e8res
config.name.gui.fontPreviewWindow.width = (Interne) Derni\u00e8re largeur de fen\u00eatre d'aper\u00e7u des polices de caract\u00e8res
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Interne) Derni\u00e8re hauteur de fen\u00eatre d'aper\u00e7u des polices de caract\u00e8res
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Interne) Derni\u00e8re position X de la fen\u00eatre d'aper\u00e7u des polices de caract\u00e8res
config.description.gui.fontPreviewWindow.posX = Il s'agit de la position de la fen\u00eatre sur l'axe des abscisses
config.name.gui.fontPreviewWindow.posY = (Interne) Derni\u00e8re position Y de la fen\u00eatre d'aper\u00e7u des polices de caract\u00e8res
config.description.gui.fontPreviewWindow.posY = Il s'agit de la position de la fen\u00eatre sur l'axe des ordonn\u00e9es
config.name.formatting.indent.size = Nb de caract\u00e8res par indentation
config.description.formatting.indent.size = Nombre d'espaces (ou tabulations) pour une indentation
config.name.formatting.indent.useTabs = Indentation
config.description.formatting.indent.useTabs = Utiliser les tabulations au lieu d'espaces pour l'indentation
config.name.beginBlockOnNewLine = Accolades pour les nouvelle lignes
config.description.beginBlockOnNewLine = Commencer les blocs avec des accolades sur les nouvelles lignes
config.name.check.updates.delay = Intervale de v\u00e9rification des mises \u00e0 jour
config.description.check.updates.delay = Intervale minimum entre chaque v\u00e9rification automatique des mises \u00e0 jour au d\u00e8marrage de l'application
config.name.check.updates.stable = V\u00e9rification des versions stables
config.description.check.updates.stable = V\u00e9rification des mises \u00e0 jour des versions stables
config.name.check.updates.nightly = V\u00e9rification des versions de tests
config.description.check.updates.nightly = V\u00e9rification des mises \u00e0 jour des versions de tests
config.name.check.updates.enabled = V\u00e9rification automatique activ\u00e9e
config.description.check.updates.enabled = V\u00e9rification automatique des mises \u00e0 jour lorsque au d\u00e9marrage de l'application
config.name.export.formats = (Interne) Formats d'export
config.description.export.formats = Derniers formats d'export utilis\u00e8
config.name.textExportSingleFile = Export de textes dans un seul fichier
config.description.textExportSingleFile = Exportation de textes dans un seul fichier plut\u00f4t que dans plusieurs fichiers
config.name.textExportSingleFileSeparator = S\u00e9parateur de textes dans un fichier d'export de textes
config.description.textExportSingleFileSeparator = Texte \u00e0 ins\u00e9rer entre chaque texte dans un fichier d'export de textes
config.name.textExportSingleFileRecordSeparator = S\u00e9parateur d'enregistrement dans un fichier d'export de textes
config.description.textExportSingleFileRecordSeparator = Texte \u00e0 ins\u00e9rer entre chaque enregistrement dans un fichier d'export de textes
config.name.warning.experimental.as12edit=Alerte lors d'\u00e9dition en AS1/2
config.description.warning.experimental.as12edit=Afficher l'avertissement lors de l'\u00e9dition exp\u00e9rimentale des scripts AS1/2
config.name.warning.experimental.as3edit=Alerte lors d'\u00e9dition en AS3
config.description.warning.experimental.as3edit=Afficher l'avertissement lors de l'\u00e9dition exp\u00e9rimentale des scripts AS3
config.name.packJavaScripts = Paquet JavaScripts
config.description.packJavaScripts = D\u00e9marrer le paquet JavaScript sur des scripts cr\u00e9\u00e9s lors de l'exportation de dessins
config.name.textExportExportFontFace = Utiliser le style de police de caract\u00e8res dans l'export SVG
config.description.textExportExportFontFace = Embarquer le style de police de caract\u00e8res dans l'export SVG au lieu des formes
config.name.lzmaFastBytes = LZMA fast bytes (valeurs permises : 5-255)
config.description.lzmaFastBytes = Param\u00e8tre vitesse des octets de l'encodeur LZMA
config.name.pluginPath = Chemin du Plugin
config.description.pluginPath = -   
config.name.showMethodBodyId = Afficher l'identifiant dans le corps de texte
config.description.showMethodBodyId = Afficher l'identifiant dans le corps de la m\u00e9thode lors de l'importation en ligne de commande
config.name.export.zoom = (Interne) Export du zoom
config.description.export.zoom = Dernier export de zoom utilis\u00e9
config.name.debuggerPort = Port de d\u00e9boggage
config.description.debuggerPort = Port utilis\u00e9 pour le d\u00e9boggage du socket
config.name.displayDebuggerInfo = (Interne) Afficher les informations du d\u00e9boggueur
config.description.displayDebuggerInfo = Afficher les informations concernant le d\u00e9boggueur avant sa mise en route
config.name.randomDebuggerPackage = Utiliser un nom de paquet al\u00e9atoire pour le d\u00e9boggueur
config.description.randomDebuggerPackage = Renomme le paquet de d\u00e9boggueur en une chaine de texte al\u00e9atoire lequel sera plus difficile \u00e0 d\u00e9tecter par ActionScript
config.name.lastDebuggerReplaceFunction = (Interne) Dernier remplacement de traces s\u00e9lectionn\u00e9
config.description.lastDebuggerReplaceFunction = Function name which was last selected in replace trace function with debugger
config.name.getLocalNamesFromDebugInfo = AS3: R\u00e9cup\u00e9rer les noms de variables \u00e0 partir des informations de d\u00e9boggage
config.description.getLocalNamesFromDebugInfo = Si les informations de d\u00e9boggage sont pr\u00e9sents, alors renommer les variables locales de types _loc_x_ en utilisant leurs noms r\u00e9els. Peut \u00eatre d\u00e9sactiv\u00e9 car certains obfuscateurs utilisent ici des noms de variables invalides
config.name.tagTreeShowEmptyFolders = Afficher les dossiers vides
config.description.tagTreeShowEmptyFolders = Afficher les dossiers vides dans l'arborescence des balises
config.name.autoLoadEmbeddedSwfs = Chargement automatique des SWFs incorpor\u00e9s
config.description.autoLoadEmbeddedSwfs = Charger automatiquement les SWF embarqu\u00e9s \u00e0 partir des balises DefineBinaryData
config.name.overrideTextExportFileName = Remplacer le nom de fichier lors de l'export de texte
config.description.overrideTextExportFileName = Vous pouvez personaliser le nom de fichier du texte export\u00e9. Utilisez {filename} dans l'emplacement d\u00e9di\u00e9 pour utiliser le nom de fichier du SWF courant.
config.name.showOldTextDuringTextEditing = Afficher le texte d'origine lors de l'\u00e9dition
config.description.showOldTextDuringTextEditing = Afficher l'original des textes des balises en couleur grise dans l'aper\u00e7u.
config.group.name.import = Import
config.group.description.import = Configuration des imports
config.name.textImportResizeTextBoundsMode = Limites du texte en mode redimensionnement
config.description.textImportResizeTextBoundsMode = Limites du texte en mode redimensionnement apr\u00e8s son \u00e9dition.
config.name.showCloseConfirmation = Confimation de fermeture
config.description.showCloseConfirmation = Affiche la confirmation de fermeture du fichier SWF lorsque les fichiers sont modifi\u00e9s
config.name.showCodeSavedMessage = Afficher un message d'avertissement lors de l'enregistrement du code
config.description.showCodeSavedMessage = Afficher un message d'avertissement lors de l'enregistrement du code
config.name.showTraitSavedMessage = Afficher un message lors de l'enregistrement de la caract\u00e9ristique
config.description.showTraitSavedMessage = Afficher un message d'avertissement lors de l'enregistrement d'une caract\u00e9ristique
config.name.updateProxyAddress = Adresse du Proxy http pour v\u00e9rifier les mises \u00e0 jour
config.description.updateProxyAddress = Adresse du Proxy http pour v\u00e9rifier les mises \u00e0 jour. Format : exemple.com:8080
config.name.editorMode = Mode \u00e9diteur
config.description.editorMode = Rend \u00e9ditable les zones de texte automatiquement lorsque vous s\u00e9lectionnez un texte ou un script
config.name.autoSaveTagModifications = Enregistrement auto des \u00e9tiquettes modifi\u00e9es
config.description.autoSaveTagModifications = Enregistre les changements lorsque vous s\u00e9lectionnez une autre \u00e9tiquette dans l'arborescence
config.name.saveSessionOnExit = Enregistrer la session en quittant
config.description.saveSessionOnExit = Enregistre la session courante et la r\u00e9tablira au red\u00e9marrage de FFDec (ne fonctionne qu'avec des fichiers physiquement pr\u00e9sents sur disque)
config.name._showDebugMenu=Affiche le menu de d\u00e9boggage FFDec
config.description._showDebugMenu=Affiche le menu de d\u00e9boggage dans le ruban lorsque le d\u00e9compileur est en train de d\u00e9bogguer.
config.name.allowOnlyOneInstance = Une seule instance FFDec (OS Windows uniquement)
config.description.allowOnlyOneInstance = FFDec peut d\u00e9marrer en une seule fois, tous les fichiers ouverts seront ajout\u00e9s dans une unique fen\u00eatre. Ne fonctionne que sous Windows uniquement.
config.name.scriptExportSingleFile = Export des scripts dans un seul fichier
config.description.scriptExportSingleFile = Exportation des scripts dans un seul fichier au lieu de cr\u00e9er plusieurs fichiers
config.name.setFFDecVersionInExportedFont = Indiquer la version de FFDec dans la police de caract\u00e8res export\u00e9es
config.description.setFFDecVersionInExportedFont = Lorsque cette option est decoch\u00e9es, FFDec n'ajoutera son num\u00e9ro de version dans la police de caract\u00e8res export\u00e9es.
config.name.gui.skin = Personnalisation de l'interface utilisateur
config.description.gui.skin = Personnalisation de l'interface utilisateur
config.name.lastSessionFiles = Fichiers de la derni\u00e8re session
config.description.lastSessionFiles = Contient les fichiers ouverts lors de la derni\u00e8re session
config.name.lastSessionSelection = S\u00e9lection de la derni\u00e8re session
config.description.lastSessionSelection = Contient la s\u00e9lection lors de la derni\u00e8re session
config.name.loopMedia = Rejouer les sons et les sprites
config.description.loopMedia = Rejoue automatiquement les sons et les sprites
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Internal) Position du s\u00e9parateur de la fen\u00eatre du chronogramme
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Images en m\u00e9moire cache
config.description.cacheImages = Met en m\u00e9moire cache les images-objets d\u00e9cod\u00e9es
config.name.swfSpecificConfigs = Configuration avanc\u00e9es SWF
config.description.swfSpecificConfigs = Contient les configurations avanc\u00e9es SWF
config.name.exeExportMode = Mode export EXE
config.description.exeExportMode = Mode export EXE
config.name.ignoreCLikePackages = Ignorer les paquets FlashCC / Alchemy ou similaires
config.description.ignoreCLikePackages = Les paquets FlashCC/Alchemy ne sont pas d\u00e9compilables correctement. Vous pouvez les d\u00e9sactiver pour gagner en vitesse de d\u00e9compilation avec les autres paquets.
config.name.overwriteExistingFiles = \u00c9craser les fichiers existants
config.description.overwriteExistingFiles = \u00c9craser les fichiers existants lors de l'export. Ne fonctionne que pour les scripts AS2/3.
config.name.smartNumberFormatting = Utiliser le formattage num\u00e9rique intelligent
config.description.smartNumberFormatting = Formattage num\u00e9riques sp\u00e9ciaux (par exemple : les couleurs et les temps)
config.name.enableScriptInitializerDisplay = (REMOVED) Affiche l'initialisateur de script
config.description.enableScriptInitializerDisplay = Active l'affichage et l'\u00e9ditiion de l'initialisateur de script. Ce param\u00e8tre ajoute une nouvelle ligne en \u00e9vidence dans chaque classe.
config.name.autoOpenLoadedSWFs = Ouvre les SWF charg\u00e9s lors de l'ex\u00e9cution (Lecteur externe = Windows seulement)
config.description.autoOpenLoadedSWFs = Ouvre automatiquement tous les SWF charg\u00e9s par la classe de chargement AS3 en lisant les SWF jou\u00e9s dans le lecteur externe FFDec. Cette option ne fonctionne que sous Windows.
config.name.lastSessionFileTitles = Les titres de la derni\u00c8re session
config.description.lastSessionFileTitles = Comprend les titres des fichiers ouverts lors de la derni\u00c8re session (par exemple depuis une URL, etc.)
config.group.name.paths = Chemins
config.group.description.paths = Location des fichiers demand\u00e9s
#config.group.tip.paths = Vous pouvez t\u00e9l\u00e9charger ces fichiers depuis le site web d'Adobe
#TODO: translate again:
config.group.tip.paths = Download projector and Playerglobal on <a href="%link1%">adobe webpage</a>. Flex SDK can be downloaded on <a href="%link2%">apache web</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Chemin du lecteur Flash
config.description.playerLocation = Location du lecteur autonome de fichier ex\u00e9cutable Flash. Used for Run action.
config.name.playerDebugLocation = 2) Chemin du d\u00e9boggeur du lecteur Flash
config.description.playerDebugLocation = Location du d\u00e9boggueur autonome du lecteur Flash. Utilis\u00e9 \u00e0 des fins de d\u00e9bogguage.
config.name.playerLibLocation = 3) Chemin du fichier PlayerGlobal (.swc)
config.description.playerLibLocation = Location de la librairie du lecteur Flash playerglobal.swc. Utilis\u00e9 principalement pour la d\u00e9compilation du code AS3.
config.name.debugHalt = Stoppe l'ex\u00e9cution lorsque le d\u00e9bogguage d\u00e9marre
config.description.debugHalt = G\u00e9n\u00e8re une pause du SWF lorsque le d\u00e9bogguage commence.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Interne) Localisation du menu de debug
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Tip: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Interne) Location du menu de debug AS1/2
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = D\u00e9lais en millisecondes avant de changer le SWF dans le lecteur externe
config.description.setMovieDelay = Ce n'est pas recommand\u00e9 de mettre une valeur en dessous de 1000 ms
config.name.warning.svgImport = Alerte lors d'un import SVG
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Utiliser un remplissage non liss\u00e9 lorsque forme est remplac\u00e9e par une image
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=Utiliser le lecteur flash propre \u00e0 AS1/2 (Exp\u00e9rimental)
config.description.internalFlashViewer.execute.as12=Tente d'ex\u00e9cuter le code ActionScript 1/2 dans le lecteur externe FFDec
config.name.warning.hexViewNotUpToDate = Afficher une vue, pas en temps r\u00e9el, des alertes en hexad\u00e9cimal
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = Afficher les instructions \u00a7\u00a7dup
config.description.displayDupInstructions = Affiche les instructions \u00a7\u00a7dup dans le code. Sans eux, le code peut \u00eatre compil\u00e9 facilement mais pourrait entra\u00eener un effet de doublon.
config.name.useRegExprLiteral = D\u00e9compiler litt\u00e9ralement RegExp en tant que /pattern/mod.
config.description.useRegExprLiteral = Utilise la syntaxe /pattern/mod lors de la d\u00e9compilations d'expressions r\u00e9guli\u00c8res. Les nouveaux RegExp("pat","mod") sont utilis\u00e9s diff\u00e9remment
config.name.handleSkinPartsAutomatically = Manipuler automatiquement les m\u00e9tadonn\u00e9es [SkinPart]
config.description.handleSkinPartsAutomatically = D\u00e9compile et \u00e9dite directement les m\u00e9tadonn\u00e9es [SkinPart]. Lorsque cette option est d\u00e9sactiv\u00e9e, les attributs et la m\u00e9thode de lecture des _skinParts sont modifiables manuellement.
config.name.simplifyExpressions = Simplifier les expressions
config.description.simplifyExpressions = \u00c9value et simplifie les expressions pour rendre le code plus lisible
config.name.resetLetterSpacingOnTextImport = R\u00e9nitialiser l'espace entre les lettres lors de l'import de texte
config.description.resetLetterSpacingOnTextImport = Adapte les caract\u00e9res de polices cyrilliques, parce qu'elles sont plus larges
