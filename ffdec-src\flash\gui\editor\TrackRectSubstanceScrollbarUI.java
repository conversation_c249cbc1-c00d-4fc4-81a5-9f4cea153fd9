/*
 * Copyright (C) 2025 JPEXS
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.jpexs.decompiler.flash.gui.editor;

import java.awt.Rectangle;
import javax.swing.JComponent;
import org.pushingpixels.substance.internal.ui.SubstanceScrollBarUI;

/**
 *
 * <AUTHOR>
 */
public class TrackRectSubstanceScrollbarUI extends SubstanceScrollBarUI {
    public TrackRectSubstanceScrollbarUI(JComponent b) {
        super(b);
    }

    @Override
    public Rectangle getTrackBounds() {
        return super.getTrackBounds();
    }        
}
