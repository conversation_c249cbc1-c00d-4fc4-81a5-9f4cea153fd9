# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
checkbox.ignorecase = \u0418\u0433\u043d\u043e\u0440\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0440\u0435\u0433\u0438\u0441\u0442\u0440
checkbox.regexp = \u0420\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u043e\u0435 \u0432\u044b\u0440\u0430\u0436\u0435\u043d\u0438\u0435
button.ok = OK
button.cancel = \u041e\u0442\u043c\u0435\u043d\u0430
label.searchtext = \u0418\u0441\u043a\u0430\u0442\u044c \u0442\u0435\u043a\u0441\u0442:
label.replacementtext = \u0422\u0435\u043a\u0441\u0442 \u0437\u0430\u043c\u0435\u043d\u044b:
#dialog.title = \u041f\u043e\u0438\u0441\u043a \u043f\u043e ActionScript
dialog.title = \u041f\u043e\u0438\u0441\u043a \u0442\u0435\u043a\u0441\u0442\u0430
dialog.title.replace = \u0417\u0430\u043c\u0435\u043d\u0430 \u0442\u0435\u043a\u0441\u0442\u0430
error = \u041e\u0448\u0438\u0431\u043a\u0430
error.invalidregexp = \u041d\u0435\u0432\u0435\u0440\u043d\u043e\u0435 \u0440\u0435\u0433\u0443\u043b\u044f\u0440\u043d\u043e\u0435 \u0432\u044b\u0440\u0430\u0436\u0435\u043d\u0438\u0435
checkbox.searchText = \u0418\u0441\u043a\u0430\u0442\u044c \u0432 \u0442\u0435\u043a\u0441\u0442\u0435
checkbox.searchAS = \u0418\u0441\u043a\u0430\u0442\u044c \u0432 AS
checkbox.replaceInParameters = \u0417\u0430\u043c\u0435\u043d\u044f\u0442\u044c \u0432 \u043f\u0430\u0440\u0430\u043c\u0435\u0442\u0440\u0430\u0445
