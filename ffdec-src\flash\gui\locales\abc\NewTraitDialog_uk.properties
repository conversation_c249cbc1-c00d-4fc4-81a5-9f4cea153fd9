# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
type.method = Method
type.getter = Getter
type.setter = Setter
type.const = Const
type.slot = Slot (var)
checkbox.static = \u0421\u0442\u0430\u0442\u0438\u0447\u043d\u0438\u0439
dialog.title = \u041d\u043e\u0432\u0430 \u043e\u0441\u043e\u0431\u043b\u0438\u0432\u0456\u0441\u0442\u044c (trait)
error.name = \u041d\u0435\u043e\u0431\u0445\u0456\u0434\u043d\u043e \u0432\u043a\u0430\u0437\u0430\u0442\u0438 \u0456\u043c'\u044f \u043e\u0441\u043e\u0431\u043b\u0438\u0432\u043e\u0441\u0442\u0456 (trait)
