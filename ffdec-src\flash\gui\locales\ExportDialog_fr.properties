# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Formes
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = Canevas HTML5
texts = Textes
texts.plain = Texte brut
texts.formatted = Texte format\u00e9
texts.svg = SVG
images = Images
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Vid\u00e9os
movies.flv = FLV (<PERSON>cu<PERSON> son)
sounds = Sons
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (Audio seulement)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Scripts
scripts.as = ActionScript
scripts.pcode = Assembleur
scripts.pcode_hex=Assembleur hexad\u00e9cimal
scripts.hex = Hexad\u00e9cimal
scripts.constants = Constantes
binaryData = Donn\u00e8es binaires
binaryData.raw = Raw
dialog.title = Exportation...
button.ok = OK
button.cancel = Annuler
morphshapes = Images anim\u00e8es
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = Canevas HTML5
frames = Image
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = Canevas HTML5
frames.pdf = PDF
frames.bmp = BMP
sprites = Sprites
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = Canevas HTML5
sprites.pdf = PDF
sprites.bmp = BMP
buttons = Buttons
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
fonts = Polices de caract\u00e9res
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Zoom
zoom.percent = %
zoom.invalid = Valeur de zoom invalide.
symbolclass = Symbol-Class mapping
symbolclass.csv = CSV
