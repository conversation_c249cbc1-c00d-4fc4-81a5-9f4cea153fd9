# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = \u041e\u0431\u0440\u0430\u0431\u043e\u0442\u0430\u0442\u044c \u0432\u0441\u0435 \u043a\u043b\u0430\u0441\u0441\u044b
dialog.title = \u0414\u0435\u043e\u0431\u0444\u0443\u0441\u043a\u0430\u0446\u0438\u044f P-code
deobfuscation.level = \u0421\u0442\u0435\u043f\u0435\u043d\u044c \u0434\u0435\u043e\u0431\u0444\u0443\u0441\u043a\u0430\u0446\u0438\u0438:
deobfuscation.removedeadcode = \u0423\u0434\u0430\u043b\u0438\u0442\u044c \"\u043c\u0451\u0440\u0442\u0432\u044b\u0439\" \u043a\u043e\u0434
deobfuscation.removetraps = \u0423\u0434\u0430\u043b\u0438\u0442\u044c \u043b\u043e\u0432\u0443\u0448\u043a\u0438 \u0434\u043b\u044f \u0434\u0435\u043a\u043e\u043c\u043f\u0438\u043b\u044f\u0442\u043e\u0440\u043e\u0432
deobfuscation.restorecontrolflow = \u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u0438\u0442\u044c \u043f\u043e\u0440\u044f\u0434\u043e\u043a \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f
button.ok = OK
button.cancel = \u041e\u0442\u043c\u0435\u043d\u0430
