# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

dialog.title = Convert place object type

place1 = colortransform, depth, character id, matrix
place2 = flag move, clip actions, clip depth, colortransform with alpha, name, ratio
place3 = class name, background color, bitmap cache, blend mode, filters, visible
place4 = AMF data

minimum = (Minimum)
unsupported = (Unsupported)

button.ok = OK
button.cancel = Cancel