# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
library = \u5e93
library.folder.images = \u56fe\u50cf
library.folder.graphics = \u56fe\u5f62
library.folder.shapeTweens = \u5f62\u72b6\u8865\u95f4
library.folder.texts = \u6587\u672c
library.folder.fonts = \u5b57\u4f53
library.folder.movieClips = \u5f71\u7247\u526a\u8f91
library.folder.buttons = \u6309\u94ae
library.folder.sounds = \u58f0\u97f3
library.folder.videos = \u89c6\u9891
library.header.name = \u540d\u79f0
library.header.asLinkage = AS\u94fe\u63a5
item.image = \u56fe\u50cf
item.graphic = \u56fe\u5f62
item.shapeTween = \u5f62\u72b6\u8865\u95f4
item.text = \u6587\u672c
item.font = \u5b57\u4f53
item.movieClip = \u5f71\u7247\u526a\u8f91
item.button = \u6309\u94ae
item.sound = \u58f0\u97f3
item.video = \u89c6\u9891
item.unknown = \u672a\u77e5
undo = \u64a4\u9500 %action%
undo.cannot = \u65e0\u6cd5\u64a4\u9500
redo = \u91cd\u505a %action%
redo.cannot = \u65e0\u6cd5\u91cd\u505a
transform = \u8f6c\u6362
action.addFrame = \u6dfb\u52a0\u5e27
action.addKeyFrame = \u6dfb\u52a0\u5173\u952e\u5e27
action.addKeyFrameWithBlankFrameBefore = \u5728\u7a7a\u767d\u5e27\u4e4b\u524d\u6dfb\u52a0\u5173\u952e\u5e27
action.removeFrame = \u79fb\u9664\u5e27
action.transform = \u8f6c\u6362
action.move = \u79fb\u52a8
action.addToStage = \u6dfb\u52a0\u5230\u821e\u53f0
action.change = \u66f4\u6539 %item%
action.change.compression = \u538b\u7f29
action.change.swfVersion = SWF\u7248\u672c
action.change.encrypted = \u52a0\u5bc6
action.change.gfx = GFX
action.change.frameRate = \u5e27\u7387
action.change.width = \u5bbd
action.change.height = \u9ad8
action.change.colorEffect = \u989c\u8272\u6548\u679c
timeline.main = \u4e3b\u65f6\u95f4\u8f74
timeline.item = \u65f6\u95f4\u8f74 %item%
timeline.item.cancel = \u53d6\u6d88-\u5355\u51fb\u4ee5\u8f6c\u5230\u4e3b\u65f6\u95f4\u8f74
properties = \u5c5e\u6027
properties.document = \u6587\u6863
properties.instance.single = \u5b9e\u4f8b %item%
properties.instance.multiple = %count% \u5b9e\u4f8b
properties.instance.none = \u65e0\u5b9e\u4f8b
properties.instance.header.positionSize = \u4f4d\u7f6e\u548c\u5927\u5c0f
properties.instance.header.colorEffect = \u989c\u8272\u6548\u679c
property.label = %item%:
property.instance.item = \u9879\u76ee
property.instance.colorEffect.alpha = Alpha
property.instance.colorEffect.red = \u7ea2
property.instance.colorEffect.green = \u7eff
property.instance.colorEffect.blue = \u84dd
property.instance.positionSize.x = X
property.instance.positionSize.y = Y
property.instance.positionSize.width = \u5bbd
property.instance.positionSize.height = \u9ad8
properties.instance.header.display = \u663e\u793a
property.instance.display.visible = \u53ef\u89c6
property.instance.display.blending = \u6df7\u5408\u6a21\u5f0f
property.instance.display.blending.normal = \u4e00\u822c
property.instance.display.blending.layer = \u56fe\u5c42
property.instance.display.blending.multiply = \u6b63\u7247\u53e0\u5e95
property.instance.display.blending.screen = \u6ee4\u8272
property.instance.display.blending.lighten = \u53d8\u4eae
property.instance.display.blending.darken = \u53d8\u6697
property.instance.display.blending.difference = \u5dee\u503c
property.instance.display.blending.add = \u589e\u52a0
property.instance.display.blending.subtract = \u51cf\u53bb
property.instance.display.blending.invert = \u53cd\u76f8
property.instance.display.blending.alpha = Alpha
property.instance.display.blending.erase = \u64e6\u9664
property.instance.display.blending.overlay = \u53e0\u52a0
property.instance.display.blending.hardlight = \u5f3a\u5149
property.instance.display.cacheAsBitmap = \u7f13\u5b58\u4e3a\u4f4d\u56fe
property.instance.display.cacheAsBitmap.transparent = \u900f\u660e
property.instance.display.cacheAsBitmap.opaque = \u4e0d\u900f\u660e
