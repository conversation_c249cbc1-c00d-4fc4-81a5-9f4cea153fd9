# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
version = version
releasedate = \u53d1\u5e03\u65f6\u95f4:
newversionavailable = \u6709\u65b0\u7248\u672c\u53ef\u7528:
changeslog = \u66f4\u6539\u65e5\u5fd7:
downloadnow = \u7acb\u5373\u4e0b\u8f7d\uff1f
button.ok = \u786e\u5b9a
button.cancel = \u53d6\u6d88
dialog.title = \u6709\u65b0\u7248\u672c\u53ef\u7528
newversion = \u65b0\u7248\u672c
newvermessage = %oldAppName%\u7684\u65b0\u7248\u672c\u5df2\u7ecf\u53d1\u5e03: %newAppName%.\r\n\u8bf7\u8bbf\u95ee%projectPage%\u8fdb\u884c\u4e0b\u8f7d.
#change this only when the date format is wrong in the changelog
#you can use any java date format string, e.g: yyyy.MM.dd
customDateFormat = \u9ed8\u8ba4
