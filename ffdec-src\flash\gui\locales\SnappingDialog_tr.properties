# Copyright (C) 2025 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = Yakalama
button.ok = Tamam
button.cancel = \u0130ptal
snapAlign = Hizalamaya Yakalama
snapToGrid = Izgaraya Yakalama
snapToGuides = K\u0131lavuzlara Yakalama
snapToPixels = Piksellere Yakalama
snapToObjects = Nesnelere Yakalama
snapAlign.settings = Hizalamaya yakalama ayarlar\u0131
snapAlign.stageBorder = Sahne s\u0131n\u0131r\u0131:
snapAlign.objectSpacing = Nesne aral\u0131\u011f\u0131:
snapAlign.objectSpacing.horizontal = Yatay:
snapAlign.objectSpacing.vertical = Dikey:
snapAlign.centerAlignment = Merkez hizalamas\u0131:
snapAlign.centerAlignment.horizontal = Yatay merkez hizalamas\u0131
snapAlign.centerAlignment.vertical = Dikey merkez hizalamas\u0131
error.invalidSpacing = Ge\u00e7ersiz aral\u0131k de\u011feri. Tam say\u0131 bekleniyor.
error.invalidBorder = Ge\u00e7ersiz kenarl\u0131k de\u011feri. Tam say\u0131 bekleniyor.
