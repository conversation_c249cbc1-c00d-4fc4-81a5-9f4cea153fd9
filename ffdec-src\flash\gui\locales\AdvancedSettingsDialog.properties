# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Advanced Settings
advancedSettings.restartConfirmation = You must restart the program for some modifications to take effect. Do you want to restart it now?
advancedSettings.columns.name = Name
advancedSettings.columns.value = Value
advancedSettings.columns.description = Description
default = default
config.group.name.export = Export
config.group.description.export = Configuration of exports
config.group.name.script = Scripts
config.group.description.script = ActionScript decompilation related
config.group.name.update = Updates
config.group.description.update = Checking for updates
config.group.name.format = Formatting
config.group.description.format = ActionScript code formatting
config.group.name.limit = Limits
config.group.description.limit = Decompilation limits for obfuscated code, etc.
config.group.name.ui = Interface
config.group.description.ui = User interface configuration
config.group.name.debug = Debug
config.group.description.debug = Debugging settings
config.group.name.display = Display
config.group.description.display = Flash objects display, etc.
config.group.name.decompilation = Decompilation
config.group.description.decompilation = Global decompilation related functions
config.group.name.other = Other
config.group.description.other = Other uncategorized configs
config.name.openMultipleFiles = Open multiple files
config.description.openMultipleFiles = Allows opening multiple files at once in one window.
config.name.decompile = Show ActionScript source
config.description.decompile = You can disable AS decompilation, then only P-code is shown.
config.name.dumpView = Dump View
config.description.dumpView = View raw data dump.
config.name.useHexColorFormat = Hex color format
config.description.useHexColorFormat = Show the colors in hex format.
config.name.parallelSpeedUp = Parallel SpeedUp
config.description.parallelSpeedUp = Parallelism can speed up decompilation.
config.name.parallelSpeedUpThreadCount = Number of threads (0 = auto)
config.description.parallelSpeedUpThreadCount = Number of threads for parallel speedup. 0 = processor count - 1.
config.name.autoDeobfuscate = Automatic deobfuscation
config.description.autoDeobfuscate = Run deobfuscation on every file before ActionScript decompilation.
config.name.cacheOnDisk = Use caching on disk
config.description.cacheOnDisk = Cache already decompiled parts on hard drive instead of memory.
config.name.internalFlashViewer = Use own Flash viewer
config.description.internalFlashViewer = Use JPEXS Flash Viewer instead of standard Flash Player for flash parts display.
config.name.gotoMainClassOnStartup = Go to main class on startup (AS3)
config.description.gotoMainClassOnStartup = Navigates to document class of AS3 file on SWF opening.
config.name.autoRenameIdentifiers = Automatic rename identifiers
config.description.autoRenameIdentifiers = Automatically rename invalid identifiers on SWF load.
config.name.offeredAssociation = (Internal) Association with SWF files displayed
config.description.offeredAssociation = Dialog about file association was already displayed.
config.name.decimalAddress = Use decimal addresses
config.description.decimalAddress = Use decimal addresses instead of hexadecimal.
config.name.showAllAddresses = Show all addresses
config.description.showAllAddresses = Display all ActionScript instruction addresses.
config.name.useFrameCache = Use frame cache
config.description.useFrameCache = Cache frames before rendering again.
config.name.useRibbonInterface = Ribbon interface
config.description.useRibbonInterface = Uncheck to use classic interface without ribbon menu.
config.name.openFolderAfterFlaExport = Open folder after FLA export
config.description.openFolderAfterFlaExport = Display output directory after exporting FLA file.
config.name.useDetailedLogging = FFDec detailed Logging
config.description.useDetailedLogging = Log detailed error messages and info for debugging of FFDec.
config.name._debugMode=FFDec in debug mode
config.description._debugMode=Mode for debugging FFDec. Turns on debug menu. This has nothing to do with the debugger functionality
config.name.resolveConstants = Resolve constants in AS1/2 p-code
config.description.resolveConstants = Turn this off to show 'constantxx' instead of real values in P-code window.
config.name.sublimiter = Limit of code subs
config.description.sublimiter = Limit of code subs for obfuscated code.
config.name.exportTimeout = Total export timeout (seconds)
config.description.exportTimeout = Decompiler will stop exporting after reaching this time.
config.name.decompilationTimeoutFile = Single file decompilation timeout (seconds)
config.description.decompilationTimeoutFile = Decompiler will stop ActionScript decompilation after reaching this time in one file.
config.name.paramNamesEnable = Enable parameter names in AS3
config.description.paramNamesEnable = Using parameter names in decompiling may cause problems because official programs like Flash CS 5.5 inserts wrong parameter names indices.
config.name.displayFileName = Show SWF name in title
config.description.displayFileName = Display SWF file/url name in the window title (You can make screenshots then).
config.name._debugCopy=FFDec debug recompile
config.description._debugCopy=Tries to compile SWF file again just after opening to ensure it produces same binary code. Use for DEBUGGING FFDec only!
config.name.dumpTags = Dump tags to console
config.description.dumpTags = Dump tags to console on reading SWF file.
config.name.decompilationTimeoutSingleMethod = AS3: Single method decompilation timeout (seconds)
config.description.decompilationTimeoutSingleMethod = Decompiler will stop ActionScript decompilation after reaching this time in one method.
config.name.lastRenameType = (Internal) Last rename type
config.description.lastRenameType = Last used rename identifiers type.
config.name.lastSaveDir = (Internal) Last save directory
config.description.lastSaveDir = Last used save directory.
config.name.lastOpenDir = (Internal) Last open directory
config.description.lastOpenDir = Last used open directory.
config.name.lastExportDir = (Internal) Last export directory
config.description.lastExportDir = Last used export directory.
config.name.locale = Language
config.description.locale = Locales identifier.
config.name.registerNameFormat = Register variable format
config.description.registerNameFormat = Format of local register variable names. Use %d for register number.
config.name.maxRecentFileCount = Max recent count
config.description.maxRecentFileCount = Maximum number of recent files.
config.name.recentFiles = (Internal) Recent files
config.description.recentFiles = Recent opened files.
config.name.fontPairingMap = (Internal) Font pairs for import
config.description.fontPairingMap = Font pairs for importing new characters.
config.name.lastUpdatesCheckDate = (Internal) Last update check date
config.description.lastUpdatesCheckDate = Date of last checking for updates on server.
config.name.gui.window.width = (Internal) Last window width
config.description.gui.window.width = Last saved window width.
config.name.gui.window.height = (Internal) Last window height
config.description.gui.window.height = Last saved window height.
config.name.gui.window.maximized.horizontal = (Internal) Window maximized horizontally
config.description.gui.window.maximized.horizontal = Last window state - maximized horizontally.
config.name.gui.window.maximized.vertical = (Internal) Window maximized vertically
config.description.gui.window.maximized.vertical = Last window state - maximized vertically.
config.name.gui.avm2.splitPane.dividerLocationPercent=(Internal) AS3 Splitter location
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Internal) AS1/2 splitter location
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Internal) Preview splitter location
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Internal) Splitter location 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Internal) Splitter location 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Save as EXE scale mode
config.description.saveAsExeScaleMode = Scaling mode for EXE export.
config.name.syntaxHighlightLimit = Syntax hilight max chars
config.description.syntaxHighlightLimit = Maximum number of characters to run syntax hilight on.
config.name.guiFontPreviewSampleText = (Internal) Last font preview sample text
config.description.guiFontPreviewSampleText = Last font preview sample text list index.
config.name.gui.fontPreviewWindow.width = (Internal) Last font preview window width
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Internal) Last font preview window height
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Internal) Last font preview window X
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Internal) Last font preview window Y
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Characters per indent
config.description.formatting.indent.size = Number or spaces(or tabs) for one indentation.
config.name.formatting.indent.useTabs = Tabs for indent
config.description.formatting.indent.useTabs = Use tabs instead of spaces for indentation.
config.name.beginBlockOnNewLine = Curly brace on new line
config.description.beginBlockOnNewLine = Begin block with curly brace on new line.
config.name.check.updates.delay = Updates check delay
config.description.check.updates.delay = Minimum time between automatic checks for updates on application start.
config.name.check.updates.stable = Check for stable versions
config.description.check.updates.stable = Checking for stable version updates.
config.name.check.updates.nightly = Check for nightly versions
config.description.check.updates.nightly = Checking for nightly version updates.
config.name.check.updates.enabled = Updates check enabled
config.description.check.updates.enabled = Automatic checking for updates on application start.
config.name.export.formats = (Internal) Export formats
config.description.export.formats = Last used export formats.
config.name.textExportSingleFile = Export texts to single file
config.description.textExportSingleFile = Exporting texts to one file instead of multiple.
config.name.textExportSingleFileSeparator = Separator of texts in one file text export
config.description.textExportSingleFileSeparator = Text to insert between texts in single file text export.
config.name.textExportSingleFileRecordSeparator = Separator of records in one file text export
config.description.textExportSingleFileRecordSeparator = Text to insert between text records in single file text export.
config.name.warning.experimental.as12edit=Warn on AS1/2 direct edit
config.description.warning.experimental.as12edit=Show warning on AS1/2 experimental direct editation
config.name.warning.experimental.as3edit=Warn on AS3 direct edit
config.description.warning.experimental.as3edit=Show warning on AS3 experimental direct editation
config.name.packJavaScripts = Pack JavaScripts
config.description.packJavaScripts = Run JavaScript packer on scripts created on Canvas Export.
config.name.textExportExportFontFace = Use font-face in SVG export
config.description.textExportExportFontFace = Embed font files in SVG using font-face instead of shapes.
config.name.lzmaFastBytes = LZMA fast bytes (valid values: 5-255)
config.description.lzmaFastBytes = Fast bytes parameter of the LZMA encoder.
config.name.pluginPath = Plugin Path
config.description.pluginPath = -
config.name.showMethodBodyId = Show method body id
config.description.showMethodBodyId = Shows the id of the methodbody for commandline import.
config.name.export.zoom = (Internal) Export zoom
config.description.export.zoom = Last used export zoom.
config.name.debuggerPort = Debugger port
config.description.debuggerPort = Port used for socket debugging.
config.name.displayDebuggerInfo = (Internal) Display debugger info
config.description.displayDebuggerInfo = Display info about debugger before switching it.
config.name.randomDebuggerPackage = Use random package name for Debugger
config.description.randomDebuggerPackage = This renames Debugger package to random string which makes debugger presence harder to detect by ActionScript.
config.name.lastDebuggerReplaceFunction = (Internal) Last selected trace replacement
config.description.lastDebuggerReplaceFunction = Function name which was last selected in replace trace function with debugger.
config.name.getLocalNamesFromDebugInfo = AS3: Get local register names from debug info
config.description.getLocalNamesFromDebugInfo = If debug info present, renames local registers from _loc_x_ to real names. This can be turned off because some obfuscators use invalid register names there.
config.name.tagTreeShowEmptyFolders = Show empty folders
config.description.tagTreeShowEmptyFolders = Show empty folders in tag tree.
config.name.autoLoadEmbeddedSwfs = Auto load embedded SWFs
config.description.autoLoadEmbeddedSwfs = Automatically load the embedded SWFs from DefineBinaryData tags.
config.name.overrideTextExportFileName = Override text export filename
config.description.overrideTextExportFileName = You can customize the filename of the exported text. Use {filename} placeholder to use the filename of current SWF.
config.name.showOldTextDuringTextEditing = Show old text during text editing
config.description.showOldTextDuringTextEditing = Shows the original text of the text tag with gray color in the preview area.
config.group.name.import = Import
config.group.description.import = Configuration of imports
config.name.textImportResizeTextBoundsMode = Text bounds resize mode
config.description.textImportResizeTextBoundsMode = Text bounds resize mode after text editing.
config.name.showCloseConfirmation = Show again SWF close confirmation
config.description.showCloseConfirmation = Show again SWF close confirmation for modified files.
config.name.showCodeSavedMessage = Show again code saved message
config.description.showCodeSavedMessage = Show again code saved message.
config.name.showTraitSavedMessage = Show again trait saved message
config.description.showTraitSavedMessage = Show again trait saved message.
config.name.updateProxyAddress = Http Proxy address for checking updates
config.description.updateProxyAddress = Http Proxy address for checking updates. Format: example.com:8080.
config.name.editorMode = Editor Mode
config.description.editorMode = Make text areas editable automatically when you select a Text or Script node.
config.name.autoSaveTagModifications = Auto save tag modifications
config.description.autoSaveTagModifications = Save the changes when you select a new tag in the tree.
config.name.saveSessionOnExit = Save session on exit
config.description.saveSessionOnExit = Save the current session and reopens it after FFDec restart (works only with real files).
config.name._showDebugMenu=Show FFDec debug menu
config.description._showDebugMenu=Shows debug menu in the ribbon for debugging of the decompiler.
config.name.allowOnlyOneInstance = Allow only one FFDec instance (Only Windows OS)
config.description.allowOnlyOneInstance = FFDec can be then run only once, all files opened will be added to one window. It works only with Windows operating system.
config.name.scriptExportSingleFile = Export scripts to single file
config.description.scriptExportSingleFile = Exporting scripts to one file instead of multiple.
config.name.setFFDecVersionInExportedFont = Set FFDec version number in exported font
config.description.setFFDecVersionInExportedFont = When this setting is disabled, FFDec won't add the current FFDec version number to the exported font.
config.name.gui.skin = User Interface Skin
config.description.gui.skin = Look and feel skin.
config.name.lastSessionFiles = Last session files
config.description.lastSessionFiles = Contains the opened files from the last session.
config.name.lastSessionSelection = Last session selection
config.description.lastSessionSelection = Contains the selection from the last session.
config.name.loopMedia = Loop sounds and sprites
config.description.loopMedia = Automatically restarts the playing of the sounds and sprites.
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Internal) Timeline Splitter location
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Cache images
config.description.cacheImages = Cache the decoded image objects.
config.name.swfSpecificConfigs = SWF specific configurations
config.description.swfSpecificConfigs = Contains the SWF specific configurations.
config.name.exeExportMode = EXE export mode
config.description.exeExportMode = EXE export mode.
config.name.ignoreCLikePackages = Ignore FlashCC / Alchemy or similar packages
config.description.ignoreCLikePackages = FlashCC/Alchemy packages cannot usually be decompiled correctly. You can disable them to speedup other packages decompilation.
config.name.overwriteExistingFiles = Overwrite the existing files
config.description.overwriteExistingFiles = Overwrite the existing files during export. Currently only for AS2/3 scripts.
config.name.smartNumberFormatting = Use smart number formatting
config.description.smartNumberFormatting = Format special numbers (for example colors and times).
config.name.enableScriptInitializerDisplay = (REMOVED) Display script initializers
config.description.enableScriptInitializerDisplay = Enable script initializers display and editation. This setting may add one newline to each class file for highlighting.
config.name.autoOpenLoadedSWFs = Open loaded SWFs during run (External viewer = WIN only)
config.description.autoOpenLoadedSWFs = Opens automatically all SWFs loaded by AS3 class Loader by running SWF when played in FFDec external player. This feature is Windows only.
config.name.lastSessionFileTitles = Last session file titles
config.description.lastSessionFileTitles = Contains the opened file titles from the last session (for example when loaded from URL etc.).
config.group.name.paths = Paths
config.group.description.paths = Location of needed files
config.group.tip.paths = Download projector and Playerglobal on <a href="%link1%">adobe webpage</a>. Flex SDK can be downloaded on <a href="%link2%">apache web</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Flash Player projector path
config.description.playerLocation = Location of standalone flash player executable. Used for Run action.
config.name.playerDebugLocation = 2) Flash Player projector content debugger path
config.description.playerDebugLocation = Location of standalone debug flash player executable. Used for Debug action.
config.name.playerLibLocation = 3) PlayerGlobal (.swc) path
config.description.playerLibLocation = Location of playerglobal.swc flash player library. It is used mostly for AS3 compilation.
config.name.debugHalt = Halt execution on debug start
config.description.debugHalt = Pause SWF on start of debugging.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Internal) Debug menu splitter location
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Tip: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Internal) AS1/2 Debug menu splitter location
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = Delay before changing the SWF in external player in ms
config.description.setMovieDelay = Not recommended to change this value below 1000ms.
config.name.warning.svgImport = Warn on SVG import
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Use non-smoothed fill when a shape is replaced with an image
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=AS1/2 in own flash viewer (Experimental)
config.description.internalFlashViewer.execute.as12=Try to execute ActionScript 1/2 during SWF playback using FFDec flash viewer
config.name.warning.hexViewNotUpToDate = Show Hex View not up-to-date warning
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = Show \u00a7\u00a7dup instructions
config.description.displayDupInstructions = Display \u00a7\u00a7dup instructions in the code. Without them, the code can be easily compiled but some dupped code with sideeffects could be executed twice.
config.name.useRegExprLiteral = Decompile RegExp as /pattern/mod literal.
config.description.useRegExprLiteral = Use /pattern/mod syntax when decompiling regular expressions. new RegExp("pat","mod") is used otherwise.
config.name.handleSkinPartsAutomatically = Handle [SkinPart] metadata automatically
config.description.handleSkinPartsAutomatically = Decompiles and direct edits [SkinPart] metadata automatically. When turned off, _skinParts attribute and its getter method is visible and manually editable.
config.name.simplifyExpressions = Simplify expressions
config.description.simplifyExpressions = Evaluate and simplify expressions to make code more readable.
config.name.resetLetterSpacingOnTextImport = Reset Letter Spacing on text import
config.description.resetLetterSpacingOnTextImport = Useful for cyrillic fonts, because they are wider.
config.name.flexSdkLocation = 4) Flex SDK directory path
config.description.flexSdkLocation = Location of Adobe Flex SDK. It is used mostly for AS3 compilation.
config.name.useFlexAs3Compiler=Use Flex SDK AS3 compiler
config.description.useFlexAs3Compiler=Use AS3 compiler from Flex SDK while ActionScript direct editation (Flex SDK directory needs to be set)
config.name.showSetAdvanceValuesMessage = Show again information about setting advance values
config.description.showSetAdvanceValuesMessage = Show again information about setting advance values.
config.name.gui.fontSizeMultiplier = Font size multiplier
config.description.gui.fontSizeMultiplier = Font size multiplier.
config.name.graphVizDotLocation = 5) GraphViz Dot executable path
config.description.graphVizDotLocation = Path to dot.exe (or similar for linux) of GraphViz application for displaying Graphs.
#Do not translate the Font Styles which is in the parenthesis:(Plain,Bold,Italic,BoldItalic)
config.name.gui.sourceFont = Source font style
config.description.gui.sourceFont = FontName-FontStyle(Plain,Bold,Italic,BoldItalic)-FontSize.
#after 11.1.0
config.name.as12DeobfuscatorExecutionLimit=AS1/2 deobfuscator execution limit
config.description.as12DeobfuscatorExecutionLimit=Maximum number of instructions processed during AS1/2 execution deobfuscation
#option that ignore in 8.0.1 and other versions
config.name.showOriginalBytesInPcodeHex = (Internal) Show original bytes
config.description.showOriginalBytesInPcodeHex = show Original Bytes In Pcode Hex.
config.name.showFileOffsetInPcodeHex = (Internal) Show file offset
config.description.showFileOffsetInPcodeHex = show File Offset In Pcode Hex.
config.name._enableFlexExport=(Internal) enableFlexExport
config.description.enableFlexExport = enable Flex Export.
config.name._ignoreAdditionalFlexClasses=(Internal) ignoreAdditionalFlexClasses
config.description.ignoreAdditionalFlexClasses = ignore Additional Flex Classes.
config.name.hwAcceleratedGraphics = Hardware accelerated graphics
config.description.hwAcceleratedGraphics = This will turn on option "sun.java2d.opengl" for hardware accelerated graphics.
config.name.gui.avm2.splitPane.docs.dividerLocationPercent=(Internal) splitPanedocsdividerLocationPercent
config.description.gui.avm2.splitPane.docs.dividerLocationPercent=splitPane docs divider Location Percent
config.name.gui.dump.splitPane.dividerLocationPercent = (Internal) dumpsplitPanedividerLocationPercent
config.description.gui.dump.splitPane.dividerLocationPercent = dump splitPane divider Location Percent.
#after 11.3.0
config.name.useAdobeFlashPlayerForPreviews = (Deprecated) Use Adobe Flash player for preview of objects
config.description.useAdobeFlashPlayerForPreviews = Use Adobe Flash player for preview of objects. WARNING: FlashPlayer was discontinued on 2021-01-12.
#after 12.0.1
config.name.showLineNumbersInPCodeGraphvizGraph = Show line numbers in Graphviz graphs
config.description.showLineNumbersInPCodeGraphvizGraph = Show line numbers in the P-code graphviz graph.
config.name.padAs3PCodeInstructionName=Pad AS3 P-code instruction names
config.description.padAs3PCodeInstructionName=Pad AS3 P-code instruction names with spaces
#after 13.0.2
config.name.indentAs3PCode=Indent AS3 P-code
config.description.indentAs3PCode=Indent AS3 P-code blocks like trait/body/code
config.name.labelOnSeparateLineAs3PCode=Label in AS3 P-code on separate line
config.description.labelOnSeparateLineAs3PCode=Make label in AS3 P-code stand on separate line
config.name.useOldStyleGetSetLocalsAs3PCode=Use oldstyle getlocal_x instead of getlocalx in AS3 P-code
config.description.useOldStyleGetSetLocalsAs3PCode=Use oldstyle getlocal_x, setlocal_x from FFDec 12.x or older
config.name.useOldStyleLookupSwitchAs3PCode=Use oldstyle lookupswitch without brackets in AS3 P-code
config.description.useOldStyleLookupSwitchAs3PCode=Use oldstyle lookupswitch from FFDec 12.x or older
#after 13.0.3
config.name.checkForModifications = Check for file modifications outside FFDec
config.description.checkForModifications = Check for modifications of files by other applications and ask to reload.
config.name.warning.initializers = Warn on AS3 slot/const editation about initializers
config.description.warning.initializers = Show warning on AS3 slot/const editation about initializers.
config.name.parametersPanelInSearchResults = Show parameters panel in search results
config.description.parametersPanelInSearchResults = Show panel with parameters like search text / ignore case / regexp in search results window.
config.name.displayAs3PCodeDocsPanel=Show docs panel in AS3 P-code
config.description.displayAs3PCodeDocsPanel=Show panel with documentation of instructions and code structure in AS3 P-code editation and display
config.name.displayAs3TraitsListAndConstantsPanel=Show AS3 traits list and constants panel
config.description.displayAs3TraitsListAndConstantsPanel=Show panel with list of traits and constants under the tag tree for AS3
#after 14.1.0
config.name.useAsTypeIcons = Use script icons based on item type 
config.description.useAsTypeIcons = Use different icons for different script types (class/interface/frame/...).
config.name.limitAs3PCodeOffsetMatching=Limit of AS3 P-code offset matching
config.description.limitAs3PCodeOffsetMatching=Limit of instructions in AS3 P-code which are offset-matched to AS3 script
#after 14.2.1
config.name.showSlowRenderingWarning = Log warning when rendering is too slow
config.description.showSlowRenderingWarning = Logs warning when internal flash viewer is too slow to display content.
#after 14.3.1
config.name.autoCloseQuotes = Auto close single quotes on script edit
config.description.autoCloseQuotes = Automatically inserts second single quote ' on typing first one.
config.name.autoCloseDoubleQuotes = Auto close double quotes on script edit
config.description.autoCloseDoubleQuotes = Automatically inserts second double quote " on typing first one.
config.name.autoCloseBrackets = Auto close brackets on script edit
config.description.autoCloseBrackets = Automatically inserts closing bracket ] on typing opening [.
config.name.autoCloseParenthesis = Auto close parenthesis on script edit
config.description.autoCloseParenthesis = Automatically inserts closing parenthesis ) on typing opening (.
config.name.showDialogOnError = Show error dialog on every error
config.description.showDialogOnError = Automatically displays error dialog on every error occurrence.
#after 14.4.0
config.name.limitSameChars = Limit of the same characters for \\{xx}C (repeat) escape
config.description.limitSameChars = Maximum number of the same characters in a row in P-code strings or obfuscated names before replacing with \\{xx}C repeat escape.
#after 14.5.2
config.name.showImportScriptsInfo = Show information before importing scripts
config.description.showImportScriptsInfo = Displays some info about how importing scripts works after clicking Import scripts in the menu.
config.name.showImportTextInfo = Show information before importing text
config.description.showImportTextInfo = Displays some info about how importing text works after clicking Import text in the menu.
config.name.showImportSymbolClassInfo = Show information before importing Symbol-Class
config.description.showImportSymbolClassInfo = Displays some info about how Symbol-Class importing works after clicking Import Symbol-Class in the menu.
config.name.showImportXmlInfo = Show information before importing XML
config.description.showImportXmlInfo = Displays some info about how XML importing works after clicking Import XML in the menu.
#after 15.1.1
config.name.lastSessionTagListSelection = Last session tag list selection
config.description.lastSessionTagListSelection = Contains the selection from the last session on the list selection view.
config.name.lastView = Last view
config.description.lastView = Last displayed view mode.
config.name.swfSpecificCustomConfigs = SWF specific custom configurations
config.description.swfSpecificCustomConfigs = Contains the SWF specific configurations in custom format.
config.name.warningOpeningReadOnly = Warn on opening readonly SWF
config.description.warningOpeningReadOnly = Show warning when opening SWF from readonly source.
# after 16.1.0
config.name.showImportImageInfo = Show information before importing images
config.description.showImportImageInfo = Displays some info about how importing images works after clicking Import images in the menu.
config.name.autoPlaySwfs = Autoplay SWF previews
config.description.autoPlaySwfs = Automatically play SWF preview on SWF node selection.
config.name.expandFirstLevelOfTreeOnLoad = Expand first level of tree on SWF load
config.description.expandFirstLevelOfTreeOnLoad = Automatically expands first level of nodes in the tree on SWF open.
# after 16.2.0
config.name.allowPlacingDefinesIntoSprites = Allow placing define tags into DefineSprite
config.description.allowPlacingDefinesIntoSprites = Allows placing (moving/copying/dragging into) define type tags into DefineSprite.
config.name.allowDragAndDropInTagListTree = Allow drag and drop in tag list view
config.description.allowDragAndDropInTagListTree = Allows moving / copying tags with drag and drop in the tree of tag list view.
config.name.allowMiterClipLinestyle = (REMOVED) Allow miter clip line styles (SLOW)
config.description.allowMiterClipLinestyle = Allow using custom renderer which supports miter clip line styles, but is slow.
advancedSettings.search = Search:
# after 16.3.1
config.name.animateSubsprites = Animate subsprites in preview
config.description.animateSubsprites = Allow subsprite animation on timeline preview.
config.name.autoPlayPreviews = Autoplay previews
config.description.autoPlayPreviews = Automatically play previews.
config.name.maxCachedTime = Maximum temporary cache time
config.description.maxCachedTime = Maximum time in milliseconds before item (which was not accessed since) is removed from cache. Set this to 0 to unlimited caching.
config.name.airLibLocation = 6) AIR library path (airglobal.swc)
config.description.airLibLocation = Location of airglobal.swc AIR library. It can be used mostly for AS3 compilation.
config.name.showImportShapeInfo = Show information before importing shapes
config.description.showImportShapeInfo = Displays some info about how importing shapes works after clicking Import shapes in the menu.
config.name.pinnedItemsTagTreePaths = Pinned items paths in tag tree
config.description.pinnedItemsTagTreePaths = Paths of nodes of tag tree which are pinned.
config.name.pinnedItemsTagListPaths = Pinned items paths in tag list view tree
config.description.pinnedItemsTagListPaths = Paths of nodes of tag list view tree which are pinned.
config.name.flattenASPackages = Flatten ActionScript packages
config.description.flattenASPackages = Make one item per package instead of package tree.
config.name.gui.scale = UI scale factor
config.description.gui.scale = Scaling factor of graphics interface. Set this to 2.0 on Mac retina displays. Application true exit (not just restart after asking) is required.
config.name.warning.video.vlc = Warn on missing VLC
config.description.warning.video.vlc = Show warning about VLC media player required when opening SWFs with DefineVideoStream tags when VLC is not available.
config.name.playFrameSounds = Play frame sounds
config.description.playFrameSounds = Play sounds on displaying frames.
config.name.fixAntialiasConflation = Extend shape area to fix antialias conflation
config.description.fixAntialiasConflation = Fixes conflation artifacts between adjacent shapes caused by antialiasing by extending contour of shape by half pixel.
config.name.autoPlaySounds = Autoplay sounds
config.description.autoPlaySounds = Automatically play sounds (DefineSound) on treenode selection.
config.name.deobfuscateAs12RemoveInvalidNamesAssignments=AS1/2 deobfuscation: Remove variable declarations with obfuscated names
config.description.deobfuscateAs12RemoveInvalidNamesAssignments=During deobfuscation of AS1/2, remove variable declarations which have nonstandard name. WARNING: This could damage SWFs which rely on obfuscated names.
config.name.gui.splitPanePlace.dividerLocationPercent = (Internal) Splitter place location
config.description.gui.splitPanePlace.dividerLocationPercent = 
config.name.gui.splitPaneTransform1.dividerLocationPercent=(Internal) Splitter transformation1 location
config.description.gui.splitPaneTransform1.dividerLocationPercent=
config.name.gui.splitPaneTransform2.dividerLocationPercent=(Internal) Splitter transformation2 location
config.description.gui.splitPaneTransform2.dividerLocationPercent=
config.name.gui.transform.lastExpandedCards = (Internal) Last expanded transformation cards
config.description.gui.transform.lastExpandedCards = 
config.name.doubleClickNodeToEdit = Double click to start editing
config.description.doubleClickNodeToEdit = Double clicking tree node starts its editation.
config.name.warningDeobfuscation = Warn on switching deobfuscation
config.description.warningDeobfuscation = Show warning on switching deobfuscation on/off.
config.name.warningRenameIdentifiers = Warn on switching auto rename identifiers
config.description.warningRenameIdentifiers = Show warning when turning on auto rename identifiers feature.
config.name.showImportMovieInfo = Show information before importing movies
config.description.showImportMovieInfo = Displays some info about how importing movies works after clicking Import movies in the menu.
config.name.showImportSoundInfo = Show information before importing sounds
config.description.showImportSoundInfo = Displays some info about how importing sounds works after clicking Import sounds in the menu.
config.name.svgRetainBounds = Retain shape bounds during SVG export
config.description.svgRetainBounds = During SVG export, the x, y position of the shape is exported exactly as in SWF (e.g. positive or negative).
config.name.disableBitmapSmoothing = Disable bitmap smoothing
config.description.disableBitmapSmoothing = Disable smoothed bitmap fills during display - show all as nonsmoothed (pixelated). This does not apply to exported images.
config.name.pinnedItemsScrollPos = Pinned items scroll/caret positions
config.description.pinnedItemsScrollPos = Scroll or caret positions of pinned items.
config.name.maxRememberedScrollposItems = Max number of remembered scroll positions
config.description.maxRememberedScrollposItems = Maximum number of remembered scroll position items.
config.name.rememberScriptsScrollPos = Remember scripts scroll/caret position
config.description.rememberScriptsScrollPos = The script scroll/caret position is retained when switching items and saved for pinned items.
config.name.rememberFoldersScrollPos = Remember folders scroll position
config.description.rememberFoldersScrollPos = Folders scroll position is retained when switching items and saved for pinned items.
#after 18.3.6
config.name.warning.initializers.class = Warn on AS3 class trait editation about script initializer
config.description.warning.initializers.class = Show warning on AS3 class trait editation about initializer.
#after 18.4.1
config.name.maxCachedNum = Maximum number of cached items per single cache
config.description.maxCachedNum = Maximum number of cached items before older items are removed from cache. Lower value = less memory, slower app. Higher value = more memory, faster app. Set this to 0 to unlimited caching.
config.name.warning.cannotencrypt = Warn when cannot save encrypted
config.description.warning.cannotencrypt = Show warning when cannot save SWF file which was encrypted using HARMAN Air encryption.
#after 18.5.0
config.name.lastExportEnableEmbed = Last setting of export embedded assets
config.description.lastExportEnableEmbed = Last setting of exporting embedded assets via [Embed] metadata.
config.name.lastFlaExportVersion = Last FLA export version
config.description.lastFlaExportVersion = Last exported FLA version.
config.name.lastFlaExportCompressed = Last FLA export compressed
config.description.lastFlaExportCompressed = Last exported FLA version compressed.
#after 19.0.0
config.name.showImportSpriteInfo = Show information before importing sprites
config.description.showImportSpriteInfo = Displays some info about how importing sprites works after clicking Import sprites in the menu.
config.name.displayAs12PCodeDocsPanel=Show docs panel in AS1/2 P-code
config.description.displayAs12PCodeDocsPanel=Show panel with documentation of actions in AS1/2 P-code editation and display
config.name.gui.action.splitPane.docs.dividerLocationPercent = (Internal) AS 1/2 splitPanedocsdividerLocationPercent
config.description.action.avm2.splitPane.docs.dividerLocationPercent=AS 1/2 splitPane docs divider Location Percent
#after 19.1.2
config.name.rememberLastScreen = Remember last used screen (on multiple monitors)
config.description.rememberLastScreen = Remember last used screen on configuration with multiple screen devices (monitors).
config.name.lastMainWindowScreenIndex = Last main window screen index
config.description.lastMainWindowScreenIndex = Last main window screen index.
config.name.lastMainWindowScreenX = Last main window screen X
config.description.lastMainWindowScreenX = Last main window screen X coordinate.
config.name.lastMainWindowScreenY = Last main window screen Y
config.description.lastMainWindowScreenY = Last main window screen Y coordinate.
config.name.lastMainWindowScreenWidth = Last main window screen width
config.description.lastMainWindowScreenWidth = Last main window screen width.
config.name.lastMainWindowScreenHeight = Last main window screen width
config.description.lastMainWindowScreenHeight = Last main window screen width.
config.name.displayAs12PCodePanel=Show AS1/2 P-code panel
config.description.displayAs12PCodePanel=Show panel with disassembled P-code actions for ActionScript 1 and 2
config.name.displayAs3PCodePanel=Show AS3 P-code panel
config.description.displayAs3PCodePanel=Show panel with disassembled P-code instructions for ActionScript 3
config.name.flaExportUseMappedFontLayout = FLA export - use mapped font layout
config.description.flaExportUseMappedFontLayout = Use assigned source font advance values when determining letterspacing when actual font has no layout during FLA export.
#after 20.0.0
config.name.formatting.tab.size = Tab size
config.description.formatting.tab.size = Number of spaces per tab.
config.name.boxBlurPixelsLimit = Box blur filter pixels limit
config.description.boxBlurPixelsLimit = Maximum number of pixels to calculate boxblur filter. The actual limit is this number multiplied by 10000. If the number of pixels is greater, then blurX and blurY is decreased.
config.name.as3ExportNamesUseClassNamesOnly=Exported assets have names bases on classes only (AS3)
config.description.as3ExportNamesUseClassNamesOnly=Exported asset files (images, sound, ...) take names only from SymbolClass tag - their assigned classes. No character id is added. Also when multiple classes is assigned to same asset, it is exported multiple times. (For ActionScript 3 SWFs)
config.name.jnaTempDirectory = JNA Temporary directory
config.description.jnaTempDirectory = Temporary directory path for JNA DLLs, etc. This needs to be set to a path not including any Unicode characters. When not set, current user TEMP directory is used.
config.name.flaExportFixShapes = FLA export - fix shapes (slow)
config.description.flaExportFixShapes = Apply procedure of splitting overlapping edges to try fix missing fills on some kind of shapes. This may be very slow on some complex shapes.
config.name.lastExportResampleWav = Last setting of resampling wav
config.description.lastExportResampleWav = Last setting of resampling wav to 44kHz.
config.name.previewResampleSound = Resample in sound previews
config.description.previewResampleSound = Resample to 44kHz in sound previews.
config.name.lastExportTransparentBackground = Last setting of ignoring background color in frame export
config.description.lastExportTransparentBackground = Last setting of ignoring background color for frame export to make background transparency.
config.name.warningAbcClean = Warn on Abc clean action
config.description.warningAbcClean = Show warning before doing Abc clean action.
config.name.warningAddFunction = Warn on adding new function in AS3 P-code
config.description.warningAddFunction = Show warning before creating new function in AS3 P-code. It also shows some info how the action works.
#after 21.0.2
config.name.linkAllClasses = Add link to all classes (sound, font, image)
config.description.linkAllClasses = Add special script that links all (sound, font, image) classes in the SWF. This is useful when no other script links them, to be still available in compiled file.

#after 21.1.0
config.name.recentColors = Recent colors
config.description.recentColors = Recent colors in the color dialog.

#after 21.1.1
config.name.gui.splitPaneEasyVertical.dividerLocationPercent = (Internal) Simple UI vertical splitter location
config.description.gui.splitPaneEasyVertical.dividerLocationPercent = 

config.name.gui.splitPaneEasyHorizontal.dividerLocationPercent = (Internal) Simple UI horizontal splitter location
config.description.gui.splitPaneEasyHorizontal.dividerLocationPercent =

config.name.lastSessionEasySwf = Last Simple editor session file
config.description.lastSessionEasySwf = Contains the selected SWF from the last session in Simple editor.

config.name.maxScriptLineLength = Maximum script line length
config.description.maxScriptLineLength = Maximum line length in the script editor before line wrapping occurs. 0 = unlimited. On linux there might be problems on displaying very large lines so that's why it's limited by default.

#after 21.1.3
config.name.lastSolEditorDirectory = Last Sol editor directory
config.description.lastSolEditorDirectory = Directory where last SOL file was opened/saved.

#after 22.0.2
config.name.halfTransparentParentLayersEasy = Half transparent parent layers in Simple editor
config.description.halfTransparentParentLayersEasy = Shows parent layers when editing subMovieClips as half transparent. False = do not show parent layers at all.

config.name.showRuler = Show ruler
config.description.showRuler = Show ruler when displaying / editing SWF objects.

config.name.snapToGuides = Snap to guides
config.description.snapToGuides = Enables snapping cursor to guides while moving objects.

config.name.snapToObjects = Snap to objects
config.description.snapToGuides = Enables snapping cursor to other objects while moving an object.

config.name.snapToPixels = Snap to pixels
config.description.snapToGuides = Enables snapping cursor to whole pixels while moving objects.

config.name.snapAlign = Snap align
config.description.snapAlign = Enables snapping cursor align lines while moving objects.

config.name.showGuides = Show guides
config.description.showGuides = Set this to false to hide guides.

config.name.lockGuides = Lock guides
config.description.lockGuides = Disables moving of guides so they stay in position and cannot be moved.

config.name.showGrid = Show grid
config.description.showGrid = Shows grid on stage.

config.name.gridVerticalSpace = Grid vertical spacing (px)
config.description.gridVerticalSpace = Vertical space between grid lines in pixels.

config.name.gridHorizontalSpace = Grid horizontal spacing (px)
config.description.gridHorizontalSpace = Horizontal space between grid lines in pixels.

config.name.snapToGrid = Snap to grid
config.description.snapToGrid = Enables snapping cursor to grid while moving objects.

config.name.gridOverObjects = Show grid over objects
config.description.gridOverObjects = When the grid is displayed, it is shown over objects on stage.

config.name.gridColor = Grid color
config.description.gridColor = Color of the drawn grid.

config.name.guidesColor = Guides color
config.description.guidesColor = Color of the drawn guides.

config.name.gridSnapAccuracy = Grid snap accuracy
config.description.gridSnapAccuracy = How far must be cursor to grid to be snapped.

config.name.guidesSnapAccuracy = Guides snap accuracy
config.description.guidesSnapAccuracy = How far must be cursor to guide to be snapped.

config.name.snapAlignObjectHorizontalSpace = Snap align object horizontal spacing
config.description.snapAlignObjectHorizontalSpace = Horizontal spaces between objects during align snapping.
    
config.name.snapAlignObjectVerticalSpace = Snap align object vertical spacing
config.description.snapAlignObjectVerticalSpace = Vertical spaces between objects during align snapping.

config.name.snapAlignStageBorder = Snap align stage border
config.description.snapAlignStageBorder = Space between border during align snapping.

config.name.snapAlignCenterAlignmentHorizontal = Snap align horizontal center alignment
config.description.snapAlignCenterAlignmentHorizontal = Enables snap align of object center horizontally.

config.name.snapAlignCenterAlignmentVertical = Snap align vertical center alignment
config.description.snapAlignCenterAlignmentVertical = Enables snap align of object center vertically.

#after 23.0.1
config.name.warning.linkTypes = Show warning when clicking external file link
config.description.warning.linkTypes = Warn on clicking a link in the script editor which leads to another SWF file.

config.name.showCodeCompletionOnDot = Show code completion on dot(.) key
config.description.showCodeCompletionOnDot = Automatically show code completion window on pressing dot(.) key. When disabled, it can be shown with Ctrl+space key.
