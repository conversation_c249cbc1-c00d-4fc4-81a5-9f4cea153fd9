# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Processar todas as classes
dialog.title = PCode deobfuscation
deobfuscation.level = Nivel de deobfusca\u00e7\u00e3o do c\u00f3digo:
deobfuscation.removedeadcode = Remover codigo "morto"
deobfuscation.removetraps = Remover armadilhas
deobfuscation.restorecontrolflow = Restaurar o fluxo de control
button.ok = OK
button.cancel = Cancelar
