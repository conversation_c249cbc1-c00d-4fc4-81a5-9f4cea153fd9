# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Traiter toutes les classes
dialog.title = D\u00e9sobfuscation assembleur
deobfuscation.level = Niveau de d\u00e9sobfuscation du code :
deobfuscation.removedeadcode = Supprimer le code inutile
deobfuscation.removetraps = Supprimer les pi\u00e8ges
deobfuscation.restorecontrolflow = R\u00e9tablir le contr\u00f4le de flux
button.ok = OK
button.cancel = Annuler
