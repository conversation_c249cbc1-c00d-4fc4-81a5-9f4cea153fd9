# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Kszta\u0142ty
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = HTML5 Canvas
texts = Teksty
texts.plain = Prosty tekst
texts.formatted = Sformatowany tekst
texts.svg = SVG
images = Obrazy
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Filmy
movies.flv = FLV (Bez d\u017awi\u0119ku)
sounds = D\u017awi\u0119ki
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (Tylko d\u017awi\u0119k)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Skrypty
scripts.as = ActionScript
scripts.pcode = P-kod
scripts.pcode_hex=P-kod z Hex
scripts.hex = Hex
binaryData = Dane binarne
binaryData.raw = Surowe dane
dialog.title = Eksport...
button.ok = OK
button.cancel = Anuluj
morphshapes = Morphshapes
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = HTML5 Canvas
frames = Klatki
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = HTML5 Canvas
frames.pdf = PDF
frames.bmp = BMP
fonts = Czcionki
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Przybli\u017cenie
zoom.percent = %
zoom.invalid = Nieprawid\u0142owa warto\u015b\u0107 przybli\u017cenia.
