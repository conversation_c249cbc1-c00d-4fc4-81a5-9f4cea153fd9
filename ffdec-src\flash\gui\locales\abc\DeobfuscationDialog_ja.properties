# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = \u3059\u3079\u3066\u306e\u30af\u30e9\u30b9\u3092\u51e6\u7406\u3059\u308b
dialog.title = P-code\u306e\u96e3\u8aad\u89e3\u9664
deobfuscation.level = \u30b3\u30fc\u30c9\u306e\u96e3\u8aad\u5316\u89e3\u9664\u30ec\u30d9\u30eb:
deobfuscation.removedeadcode = \u30c7\u30c3\u30c9\u30b3\u30fc\u30c9\u3092\u524a\u9664\u3059\u308b
deobfuscation.removetraps = \u30c8\u30e9\u30c3\u30d7\u3092\u524a\u9664\u3059\u308b
deobfuscation.restorecontrolflow = \u5236\u5fa1\u30d5\u30ed\u30fc\u3092\u5fa9\u5143\u3059\u308b
button.ok = OK
button.cancel = \u30ad\u30e3\u30f3\u30bb\u30eb
