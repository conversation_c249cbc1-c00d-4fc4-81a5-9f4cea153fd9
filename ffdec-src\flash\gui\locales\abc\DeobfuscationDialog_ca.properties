# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Processa totes les classes
dialog.title = Desofuscaci\u00f3 de Codi P
deobfuscation.level = Nivell de desofuscaci\u00f3 del codi:
deobfuscation.removedeadcode = Elimina el codi mort
deobfuscation.removetraps = Elimina les trampes
deobfuscation.restorecontrolflow = Elimina el flux de control
button.ok = B\u00e9
button.cancel = Cancel\u00b7la
