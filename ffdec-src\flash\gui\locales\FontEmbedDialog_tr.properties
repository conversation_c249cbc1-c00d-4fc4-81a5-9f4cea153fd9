# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%available% / %total% karakter)
dialog.title = Yaz\u0131 tipi g\u00f6mme
label.individual = Bireysel karakterler:
button.loadfont = Yaz\u0131 tipini diskten y\u00fckle...
filter.ttf = True Type Yaz\u0131 Tipi dosyalar\u0131 (*.ttf)
error.invalidfontfile = Ge\u00e7ersiz yaz\u0131 tipi dosyas\u0131
error.cannotreadfontfile = Yaz\u0131 tipi dosyas\u0131 okunam\u0131yor
installed = Y\u00fcklendi: 
ttffile.noselection = TTF dosyas\u0131: <select>
ttffile.selection = TTF dosyas\u0131: %fontname% (%filename%)
allcharacters = T\u00fcm karakterler (%available% karakter)
#after 14.0.0
ascentdescentleading = \u00c7\u0131k\u0131\u015f, ini\u015f ve y\u00f6nlendirmeyi ayarla
#after 19.1.2
font.name = Yaz\u0131 tipi ad\u0131:
font.name.default = Yaz\u0131 tipim
font.source = Kaynak:
