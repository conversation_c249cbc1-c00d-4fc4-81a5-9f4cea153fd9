# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = Yol \u00e7\u00f6z\u00fcmleme
info = SWF yolunda bulunamad\u0131\u011f\u0131nda varl\u0131klar\u0131n aranaca\u011f\u0131 dizinleri ayarlay\u0131n.\r\n "data:" \u00f6nekiyle ba\u015flayan yollar\u0131n\u0131z varsa, "data:|C:\\MyData\\Dir" gibi, yolun sahip olmas\u0131 gereken \u00f6neki ay\u0131rmak i\u00e7in "|" boru i\u015faretini kullanabilirsiniz.\r\nSat\u0131r ba\u015f\u0131na bir yol. Bu \u015fu anda yaln\u0131zca GFX etiketlerinde kullan\u0131l\u0131r.
button.ok = Tamam
button.cancel = \u0130ptal
