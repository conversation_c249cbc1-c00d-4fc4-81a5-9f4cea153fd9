# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%available% \u0438\u0437 %total% \u0441\u0438\u043c\u0432\u043e\u043b\u043e\u0432)
dialog.title = \u0412\u0441\u0442\u0440\u0430\u0438\u0432\u0430\u043d\u0438\u0435 \u0448\u0440\u0438\u0444\u0442\u043e\u0432
label.individual = \u041e\u0442\u0434\u0435\u043b\u044c\u043d\u044b\u0435 \u0441\u0438\u043c\u0432\u043e\u043b\u044b:
button.loadfont = \u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044c \u0448\u0440\u0438\u0444\u0442 \u0441 \u0434\u0438\u0441\u043a\u0430...
filter.ttf = \u0424\u0430\u0439\u043b\u044b True Type \u0448\u0440\u0438\u0444\u0442\u043e\u0432 (*.ttf)
error.invalidfontfile = \u041d\u0435\u0432\u0435\u0440\u043d\u044b\u0439 \u0444\u0430\u0439\u043b \u0448\u0440\u0438\u0444\u0442\u0430
error.cannotreadfontfile = \u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e \u043f\u0440\u043e\u0447\u0438\u0442\u0430\u0442\u044c \u0444\u0430\u0439\u043b \u0448\u0440\u0438\u0444\u0442\u0430
installed = \u0423\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u043e: 
ttffile.noselection = TTF \u0444\u0430\u0439\u043b: <\u0432\u044b\u0431\u0435\u0440\u0438\u0442\u0435>
ttffile.selection = TTF \u0444\u0430\u0439\u043b: %fontname% (%filename%)
allcharacters = \u0412\u0441\u0435 \u0441\u0438\u043c\u0432\u043e\u043b\u044b (%available% \u0441\u0438\u043c\u0432\u043e\u043b\u044b)
