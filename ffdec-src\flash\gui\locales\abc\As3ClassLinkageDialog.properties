# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = AS3 class linkage
button.ok = OK
button.proceed = Proceed
button.cancel = Cancel
classname = Fully qualified class name:
error.multipleClasses = Error: This character has already assigned more than single class, it cannot be renamed via this tool. However, you can still manually modify the SymbolClass tag.
error.alreadyAssignedClass = Error: This class is already assigned to different character
error.needToModify = Modify the classname to a new name.
class.found = Existing class with the name found.
class.notfound = Class with the name does not exist yet.
symbolClassAppropriate = SymbolClass tag in the nearest appropriate frame will be modified or created.
class.notfound.createAsk = Do you want the class to be created?
class.notfound.create = Yes, create class
class.notfound.create.parentType = Parent class name (fully qualified):
class.notfound.create.abc.where = Where to create byte code:
class.notfound.create.abc.where.existing = Existing DoABC tag
class.notfound.create.abc.where.new = New DoABC tag
class.notfound.onlySetClassName = No, just assign class name
class.notfound.onlySetClassName.symbolClass.where = Where to store the linkage data:
class.notfound.onlySetClassName.symbolClass.where.existing = Existing SymbolClass tag
class.notfound.onlySetClassName.symbolClass.where.new = New SymbolClass tag