# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Configuraci\u00f3 Avan\u00e7ada
advancedSettings.restartConfirmation = Has de reiniciar el programa per tal que algunes modificacions tinguin efecte. Vols reiniciar-lo ara?
advancedSettings.columns.name = Nom
advancedSettings.columns.value = Valor
advancedSettings.columns.description = Descripci\u00f3
default = per defecte
config.group.name.export = Exporta
config.group.description.export = Configuraci\u00f3 d'exportacions
config.group.name.script = Scripts
config.group.description.script = Relacionats amb la descompilaci\u00f3 d'ActionScript
config.group.name.update = Actualitzacions
config.group.description.update = Comprovaci\u00f3 d'actualitzacions
config.group.name.format = Formatat
config.group.description.format = Formatat del codi ActionScript
config.group.name.limit = L\u00edmits
config.group.description.limit = L\u00edmits de descompilaci\u00f3 per a codi ofuscat, etc.
config.group.name.ui = Interf\u00edcie
config.group.description.ui = Configuraci\u00f3 de la interf\u00edcie d'usuari
config.group.name.debug = Depuraci\u00f3
config.group.description.debug = Par\u00e0metres de Depuraci\u00f3
config.group.name.display = Reproducci\u00f3
config.group.description.display = Reproducci\u00f3 d'objectes Flash, etc.
config.group.name.decompilation = Descompilaci\u00f3
config.group.description.decompilation = Funcions globals relacionades amb la descompilaci\u00f3
config.group.name.other = Altres
config.group.description.other = Altres configuracions no categoritzades
config.name.openMultipleFiles = Obertura de m\u00faltiples fitxers
config.description.openMultipleFiles = Permet obrir m\u00faltiples fitxers a la vegada en una sola finestra
config.name.decompile = Mostra el codi font ActionScript
config.description.decompile = Pots desactivar la descompilaci\u00f3 d'AS, llavors nom\u00e9s es mostra el codi P
config.name.dumpView = Visualitza Abocament
config.description.dumpView = Visualitza l'abocament de dades en cru
config.name.useHexColorFormat = Format de colors hex
config.description.useHexColorFormat = Mostra els colors en format hexadecimal
config.name.parallelSpeedUp = Acceleraci\u00f3 Paral\u00b7lela
config.description.parallelSpeedUp = El paral\u00b7lelisme pot accelerar la descompilaci\u00f3
config.name.parallelSpeedUpThreadCount = Nombre de fils (0 = auto)
config.description.parallelSpeedUpThreadCount = Nombre de fils per a l'acceleraci\u00f3 paral\u00b7lela. 0 = processor count - 1.
config.name.autoDeobfuscate = Desofuscaci\u00f3 autom\u00e0tica
config.description.autoDeobfuscate = Executa la desofuscaci\u00f3 a cada fitxer abans de la descompilaci\u00f3 d'ActionScript
config.name.cacheOnDisk = Utilitza cau en el disc
config.description.cacheOnDisk = Desa al cau del disc dur les parts ja descompilades en lloc de la mem\u00f2ria
config.name.internalFlashViewer = Utilitza el visualitzador Flash propi
config.description.internalFlashViewer = Utilitza el Visualitzador Flash JPEXS en lloc del Reproductor Flash est\u00e0ndard per a reproduir parts de flash
config.name.gotoMainClassOnStartup = Ves a la classe principal en iniciar (AS3)
config.description.gotoMainClassOnStartup = Navega a la classe de document del fitxer AS3 en obrir el SWF
config.name.autoRenameIdentifiers = Renomenament autom\u00e0tic d'identificadors
config.description.autoRenameIdentifiers = Renomena autom\u00e0ticament els identificadors inv\u00e0lids en carregar el SWF
config.name.offeredAssociation = (Intern) Associaci\u00f3 amb els fitxers SWF reprodu\u00efts
config.description.offeredAssociation = El di\u00e0leg sobre associacions de fitxers ja s'ha mostrat
config.name.decimalAddress = Utilitza adreces decimals
config.description.decimalAddress = Utilitza adreces decimals en lloc d'hexadecimals
config.name.showAllAddresses = Mostra totes les adreces
config.description.showAllAddresses = Mostra totes les adreces d'instruccions ActionScript
config.name.useFrameCache = Utilitza cau de marcs
config.description.useFrameCache = Desa els marcs en cau abans de tornar a renderitzar
config.name.useRibbonInterface = Interf\u00edcie de cinta
config.description.useRibbonInterface = Desmarca-ho per utilitzar la interf\u00edcie cl\u00e0ssica sense men\u00fa de cinta
config.name.openFolderAfterFlaExport = Obre la carpeta despr\u00e9s d'exportar un FLA
config.description.openFolderAfterFlaExport = Mostra el directori de sortida despr\u00e9s d'exportar un fitxer FLA
config.name.useDetailedLogging = Registre Detallat
config.description.useDetailedLogging = Registra missatges d'error detallats i la informaci\u00f3 de depuraci\u00f3
config.name.resolveConstants = Resol constants en codi P AS1/2 
config.description.resolveConstants = Desactiva-ho per mostrar 'constantxx' en lloc dels valors reals a la finestra de codi P
config.name.sublimiter = L\u00edmit de subs de codi
config.description.sublimiter = L\u00edmit de subs de codi per a codi ofuscat.
config.name.exportTimeout = Temps l\u00edmit total d'exportaci\u00f3 (segons)
config.description.exportTimeout = El descompilador aturar\u00e0 l'exportaci\u00f3 despr\u00e9s d'at\u00e8nyer aquest temps
config.name.decompilationTimeoutFile = Temps l\u00edmit de descompilaci\u00f3 d'un sol fitxer (segons)
config.description.decompilationTimeoutFile = El descompilador aturar\u00e0 la descompilaci\u00f3 d'ActionScript despr\u00e9s d'at\u00e8nyer aquest temps en un sol fitxer
config.name.paramNamesEnable = Activa els noms de par\u00e0metres a AS3
config.description.paramNamesEnable = Utilitzar noms de par\u00e0metres en la descompilaci\u00f3 pot provocar problemes perqu\u00e8 els programes oficials com ara Flash CS 5.5 insereixen \u00edndexs de noms de par\u00e0metres incorrectament
config.name.displayFileName = Mostra el nom del SWF al t\u00edtol
config.description.displayFileName = Mostra el nom del fitxer/url SWF al t\u00edtol de la finestra (llavors en pots fer captures de pantalla)
config.name.dumpTags = Aboca les etiquetes a la consola
config.description.dumpTags = Aboca les etiquetes a la consola en llegir un fitxer SWF
config.name.decompilationTimeoutSingleMethod = AS3: temps l\u00edmit de descompilaci\u00f3 en un sol m\u00e8tode (segons)
config.description.decompilationTimeoutSingleMethod = El descompilador aturar\u00e0 la descompilaci\u00f3 d'ActionScript despr\u00e9s d'at\u00e8nyer aquest temps en un m\u00e8tode
config.name.lastRenameType = (Intern) Darrer tipus de renomenament
config.description.lastRenameType = Darrer tipus d'identificadors de renomenament utilitzat
config.name.lastSaveDir = (Intern) Darrer directori de desat
config.description.lastSaveDir = Darrer directori de desat utilitzat
config.name.lastOpenDir = (Intern) Darrer directory obert
config.description.lastOpenDir = Darrer directory obert utilitzat
config.name.lastExportDir = (Intern) Darrer directori d'exportaci\u00f3
config.description.lastExportDir = Darrer directori d'exportaci\u00f3 utilitzat
config.name.locale = Idioma
config.description.locale = Identificador de locales
config.name.registerNameFormat = Format de les variables de registre
config.description.registerNameFormat = Format dels noms de les variables de registre locals. Utilitza %d per al n\u00famero de registre.
config.name.maxRecentFileCount = Recompte m\u00e0x. recent
config.description.maxRecentFileCount = Nombre m\u00e0xim de fitxers recents
config.name.recentFiles = (Intern) Fitxers recents
config.description.recentFiles = Fitxers oberts recentment
config.name.fontPairingMap = (Intern) Parells de tipografies a importar
config.description.fontPairingMap = Parells de tipografies per a importar car\u00e0cters nous
config.name.lastUpdatesCheckDate = (Intern) Darrera data de comprovaci\u00f3 d'actualitzacions
config.description.lastUpdatesCheckDate = El darrer dia que es van comprovar les actualitzacions al servidor
config.name.gui.window.width = (Intern) Amplada de la darrera finestra
config.description.gui.window.width = L'amplada de la darrera finestra desada
config.name.gui.window.height = (Intern) Al\u00e7ada de la darrera finestra
config.description.gui.window.height = L'al\u00e7ada de la darrera finestra desada
config.name.gui.window.maximized.horizontal = (Intern) Finestra maximitzada horitzontalment
config.description.gui.window.maximized.horizontal = Darrer estat de la finestra - maximitzada horitzontalment
config.name.gui.window.maximized.vertical = (Intern) Finestra maximitzada verticalment
config.description.gui.window.maximized.vertical = Darrer estat de la finestra - maximitzada verticalment
config.name.gui.avm2.splitPane.dividerLocationPercent=(Intern) Ubicaci\u00f3 del Splitter AS3
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Intern) Ubicaci\u00f3 del Splitter AS1/2
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Intern) Previsualitza la ubicaci\u00f3 del splitter
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Intern) Ubicaci\u00f3 del Splitter 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Intern) Ubicaci\u00f3 del Splitter 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Desa com a mode d'escala EXE
config.description.saveAsExeScaleMode = Mode d'escala per a exporaci\u00f3 EXE
config.name.syntaxHighlightLimit = M\u00e0x. car\u00e0cters de ressaltat de sintaxi
config.description.syntaxHighlightLimit = Nombre m\u00e0xim de car\u00e0cters sobre els quals cal aplicar el ressaltat de sintaxi
config.name.guiFontPreviewSampleText = (Intern) Text d'exemple de la darrera previsualitzaci\u00f3 de tipografies
config.description.guiFontPreviewSampleText = \u00cdndex de la llista de textos d'exemple de la darrera previsualitzaci\u00f3 de tipografies
config.name.gui.fontPreviewWindow.width = (Intern) Amplada de la finestra de la darrera previsualitzaci\u00f3 de tipografies
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Intern) Al\u00e7ada de la finestra de la darrera previsualitzaci\u00f3 de tipografies
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Intern) X de la finestra de la darrera previsualitzaci\u00f3 de tipografies
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Intern) Y de la finestra de la darrera previsualitzaci\u00f3 de tipografies
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Car\u00e0cters de sagnat
config.description.formatting.indent.size = Nombre d'espais (o tabuladors) d'un sagnat
config.name.formatting.indent.useTabs = Tabuladors per sagnar
config.description.formatting.indent.useTabs = Utilitza tabuladors per sagnar en lloc d'espais
config.name.beginBlockOnNewLine = Clau en l\u00ednia nova
config.description.beginBlockOnNewLine = Comen\u00e7a el bloc amb una clau en una l\u00ednia nova
config.name.check.updates.delay = Interval de comprovaci\u00f3 d'actualitzacions
config.description.check.updates.delay = Temps m\u00ednim entre comprovacions autom\u00e0tiques d'actualitzacions en iniciar l'aplicaci\u00f3
config.name.check.updates.stable = Comprova les versions estables
config.description.check.updates.stable = S'estan comprovant les actualitzacions de versions estables
config.name.check.updates.nightly = Comprova les versions nocturnes
config.description.check.updates.nightly = S'estan comprovant les actualitzacions de versions nocturnes
config.name.check.updates.enabled = Comprovaci\u00f3 d'actualitzacions activada
config.description.check.updates.enabled = Es comprova l'exist\u00e8ncia d'actualitzacions en iniciar l'aplicaci\u00f3
config.name.export.formats = (Intern) Formats d'exportaci\u00f3
config.description.export.formats = Darrers formats d'exportaci\u00f3 utilitzats
config.name.textExportSingleFile = Exporta els text en un sol fitxer
config.description.textExportSingleFile = S'estan exportant els texts en un sol fitxer en lloc de m\u00faltiples
config.name.textExportSingleFileSeparator = Separador de text en l'exportaci\u00f3 de text d'un sol fitxer
config.description.textExportSingleFileSeparator = El text a inserir entre els texts de l'exportaci\u00f3 de text d'un sol fitxer
config.name.textExportSingleFileRecordSeparator = Separador de registres en l'exportaci\u00f3 de text d'un sol fitxer
config.description.textExportSingleFileRecordSeparator = El text a inserir entre els registres de l'exportaci\u00f3 de text d'un sol fitxer
config.name.warning.experimental.as12edit=Avisa d'una edici\u00f3 directa d'AS1/2
config.description.warning.experimental.as12edit=Mostra un av\u00eds en utilitzar l'edici\u00f3 directa experimental d'AS1/2
config.name.warning.experimental.as3edit=Avisa d'una edici\u00f3 directa d'AS3
config.description.warning.experimental.as3edit=Mostra un av\u00eds en utilitzar l'edici\u00f3 directa experimental d'AS3
config.name.packJavaScripts = Empaqueta els JavaScripts
config.description.packJavaScripts = Executa l'empaquetador de JavaScripts als scripts creats a l'Exportaci\u00f3 de Tela
config.name.textExportExportFontFace = Utilitza 'font-face' en l'exportaci\u00f3 SVG
config.description.textExportExportFontFace = Incrusta els fitxers de tipografies en SVG utilitzant 'font-face' en lloc de 'shapes'
config.name.lzmaFastBytes = Bytes r\u00e0pids LZMA (v\u00e0lors valids: 5-255)
config.description.lzmaFastBytes = Par\u00e0metre de bytes r\u00e0pids del codificador LZMA
config.name.pluginPath = Cam\u00ed de l'Extensi\u00f3
config.description.pluginPath = -   
config.name.showMethodBodyId = Mostra l'id del methodbody
config.description.showMethodBodyId = Mostra l'identificador del methodbody per a importaci\u00f3 de l\u00ednia d'ordres
config.name.export.zoom = (Intern) Zoom d'exportaci\u00f3
config.description.export.zoom = Darrer zoom d'exportaci\u00f3 utilitzat
config.name.debuggerPort = Port de depuraci\u00f3
config.description.debuggerPort = Port utilitzat per a la depuraci\u00f3 de s\u00f2col
config.name.displayDebuggerInfo = (Intern) Mostra la info del depurador
config.description.displayDebuggerInfo = Mostra la informaci\u00f3 del depurador abans d'engegar-lo
config.name.randomDebuggerPackage = Utilitza un nom de paquet aleatori per al Depurador
config.description.randomDebuggerPackage = Aix\u00f2 renomena el paquet del Depurador amb una cadena aleat\u00f2ria que fa que la pres\u00e8ncia del depurador sigui m\u00e9s dif\u00edcil de detectar per part d'ActionScript
config.name.lastDebuggerReplaceFunction = (Intern) La darrera substituci\u00f3 de rastreig seleccionada
config.description.lastDebuggerReplaceFunction = El nom de funci\u00f3 que s'ha seleccionat per darrer cop en la funci\u00f3 de rastreig de substituci\u00f3 amb el depurador
config.name.getLocalNamesFromDebugInfo = AS3: Obt\u00e9 els noms locals dels registres a partir de la informaci\u00f3 del depurador
config.description.getLocalNamesFromDebugInfo = Si la informaci\u00f3 de depuraci\u00f3 es troba present, renomena els registres locals de _loc_x_ a noms reals. Aix\u00f2 es pot desactivar perqu\u00e8 alguns ofuscadors utilitzen noms de registre inv\u00e0lids
config.name.tagTreeShowEmptyFolders = Mostra carpetes buides
config.description.tagTreeShowEmptyFolders = Mostra les carpetes buides a l'arbre d'etiquetes.
config.name.autoLoadEmbeddedSwfs = Auto Carrega SWFs integrats
config.description.autoLoadEmbeddedSwfs = Carrega autom\u00e0ticament els SWFs integrats a partir de les etiquetes DefineBinaryData.
config.name.overrideTextExportFileName = Ignora nom de fitxer d'exportaci\u00f3 text
config.description.overrideTextExportFileName = Pots personalitzar el nom de fitxer del text exportat. Utilitza l'etiqueta {filename} per fer servir el nom de fitxer del SWF actual.
config.name.showOldTextDuringTextEditing = Mostra el text anterior durant l'edici\u00f3
config.description.showOldTextDuringTextEditing = Mostra el text original de l'etiqueta de text en color gris a l'\u00e0rea de previsualitzaci\u00f3.
config.group.name.import = Importaci\u00f3
config.group.description.import = Configuraci\u00f3 d'importacions
config.name.textImportResizeTextBoundsMode = Mode d'amidament dels l\u00edmits de text
config.description.textImportResizeTextBoundsMode = Mode d'amidament dels l\u00edmits del text despr\u00e9s d'editar-lo
config.name.showCloseConfirmation = Torna a mostrar la confirmaci\u00f3 de tancament del SWF
config.description.showCloseConfirmation = Torna a mostrar la confirmaci\u00f3 de tancament del SWF per als fitxers modificats.
config.name.showCodeSavedMessage = Torna a mostrar el missatge de codi desat
config.description.showCodeSavedMessage = Torna a mostrar el missatge de codi desat
config.name.showTraitSavedMessage = Torna a mostrar el missatge de tret desat
config.description.showTraitSavedMessage = Torna a mostrar el missatge de tret desat
config.name.updateProxyAddress = Adre\u00e7a de la Proxy http per a la comprovaci\u00f3 d'actualitzacions
config.description.updateProxyAddress = Adre\u00e7a de la Proxy http per a la comprovaci\u00f3 d'actualitzacions. Format: example.com:8080
config.name.editorMode = Mode de l'Editor
config.description.editorMode = Fes editables les \u00e0rees de text autom\u00e0ticament quan se seleccioni un node Text o Script
config.name.autoSaveTagModifications = Desa autom\u00e0ticament les modificacions d'etiquetes
config.description.autoSaveTagModifications = Desa els canvis quan seleccionis una nova etiqueta a l'arbre
config.name.saveSessionOnExit = Desa la sessi\u00f3 en sortir
config.description.saveSessionOnExit = Desa la sessi\u00f3 actual i la reobre despr\u00e9s del reinici de FFDec (nom\u00e9s funciona amb fitxers reals)
config.name._showDebugMenu=Mostra el men\u00fa de depuraci\u00f3 de FFDec
config.description._showDebugMenu=Mostra el men\u00fa de depuraci\u00f3 a la banda per depurar el descompilador.
config.name.allowOnlyOneInstance = Permet nom\u00e9s una inst\u00e0ncia de FFDec (Nom\u00e9s en SO Windows)
config.description.allowOnlyOneInstance = Llavors, FFDec nom\u00e9s es pot executar un cop, tots els fitxers oberts s'afegiran a una finestra. Nom\u00e9s funciona amb el sistema operatiu Windows.
config.name.scriptExportSingleFile = Exporta els scripts a un sol fitxer
config.description.scriptExportSingleFile = Exporta els scripts a un sol fitxer en lloc de m\u00faltiples fitxers
config.name.setFFDecVersionInExportedFont = Posa el n\u00famero de versi\u00f3 de FFDec a la tipografia exportada
config.description.setFFDecVersionInExportedFont = Quan aquest par\u00e0metre est\u00e0 desactivat, FFDec no afegir\u00e0 el n\u00famero de versi\u00f3 actual de FFDec a la tipografia exportada.
config.name.gui.skin = Pell d'Interf\u00edcie d'Usuari
config.description.gui.skin = Pell d'aparen\u00e7a
config.name.lastSessionFiles = Fitxers de la darrera sessi\u00f3
config.description.lastSessionFiles = Cont\u00e9 els fitxers oberts de la darrera sessi\u00f3
config.name.lastSessionSelection = Selecci\u00f3 de la darrera sessi\u00f3
config.description.lastSessionSelection = Cont\u00e9 la selecci\u00f3 de la darrera sessi\u00f3
config.name.loopMedia = Cicla els sons i els sprites
config.description.loopMedia = Reinicia autom\u00e0ticament la reproducci\u00f3 dels sons i els sprites
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Intern) Ubicaci\u00f3 del Timeline Splitter
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Posa imatges a la mem\u00f2ria cau
config.description.cacheImages = Posa a la  mem\u00f2ria cau els objectes d'imatge descodificats
config.name.swfSpecificConfigs = Configuracions espec\u00edfiques de SWF
config.description.swfSpecificConfigs = Cont\u00e9 les configuracions espec\u00edfiques de SWF
config.name.exeExportMode = Mode d'exportaci\u00f3 EXE
config.description.exeExportMode = Mode d'exportaci\u00f3 EXE
config.name.ignoreCLikePackages = Ignora FlashCC / Alchemy o paquets similars
config.description.ignoreCLikePackages = Normalment, els paquets FlashCC/Alchemy no es poden descompilar correctament. Els pots desactivar per accelerar la descompilaci\u00f3 d'altres paquets.
config.name.overwriteExistingFiles = Sobreescriu els fitxers existents
config.description.overwriteExistingFiles = Sobreescriu els fitxers existents durant l'exportaci\u00f3. Actualment, nom\u00e9s per a scripts for AS2/3
config.name.smartNumberFormatting = Utilitza formatat intel\u00b7ligent de n\u00fameros
config.description.smartNumberFormatting = Formateja els n\u00fameros especials (per exemple, colors i hores)
config.name.enableScriptInitializerDisplay = (REMOVED) Mostra els iniciadors de scripts
config.description.enableScriptInitializerDisplay = Activa la mostra i edici\u00f3 dels iniciadors de scripts. Pot ser que aquest par\u00e0metre afegeixi un salt de l\u00ednia a cada classe per ressaltar-la.
config.name.autoOpenLoadedSWFs = Obre els SWFs carregats durant l'execuci\u00f3 (Visor extern = nom\u00e9s WIN)
config.description.autoOpenLoadedSWFs = Obre autom\u00e0ticament tots els SWFs carregats del carregador de classes AS3 en executar SWF quan es reproduieix en el reproductor extern de FFDec. Aquesta caracter\u00edstica nom\u00e9s esta a Windows.
config.name.lastSessionFileTitles = T\u00edtols de fitxers de la darrera sessi\u00f3
config.description.lastSessionFileTitles = Cont\u00e9 els t\u00edtols de fitxers oberts la darrera sessi\u00f3 (per exemple, quan es carreguen des d'un URL, etc.)
config.group.name.paths = Camins
config.group.description.paths = Ubicaci\u00f3 dels fitxers necessaris
config.group.tip.paths = Baixa el projector i Playerglobal de la <a href="%link1%">p\u00e0gina web d'Adobe</a>. El Flex SDK es pot baixar de <a href="%link2%">web apache</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Cam\u00ed del projector de Flash Player
config.description.playerLocation = Ubicaci\u00f3 de l'executable independent del reproductor de flash. S'utilitza per a l'acci\u00f3 Executa.
config.name.playerDebugLocation = 2) Cam\u00ed del depurador del projector de Flash Player
config.description.playerDebugLocation = Ubicaci\u00f3 de l'executable independent del depurador de flash. S'utilitza per a l'acci\u00f3 Depura.
config.name.playerLibLocation = 3) Cam\u00ed de PlayerGlobal (.swc)
config.description.playerLibLocation = Ubicaci\u00f3 de la llibreria flash playerglobal.swc. S'utilitza principalment per a la compilaci\u00f3 dAS3.
config.name.debugHalt = Atura l'execuci\u00f3 en comen\u00e7ar la depuraci\u00f3
config.description.debugHalt = Pausa el SWF en comen\u00e7ar la depuraci\u00f3.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Intern) Depura la ubicaci\u00f3 del splitter de men\u00fa
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Consell: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = Depura la ubicaci\u00f3 del splitter de men\u00fa AS1/2
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = Retard abans de canviar el SWF al reproductor extern en ms
config.description.setMovieDelay = No es recomana de canviar aquest valor per sota de 1000ms
config.name.warning.svgImport = Avisa en importar SVG
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Utilitza emplenament no suavitzat quan se substitueix una forma per una imatge
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=AS1/2 en el propi visualitzador de flash (Experimental)
config.description.internalFlashViewer.execute.as12=Prova d'executar ActionScript 1/2 durant la reproducci\u00f3 de SWF utilitzant el visualitzador de flash de FFDec
config.name.warning.hexViewNotUpToDate = Mostra l'av\u00eds de Vista Hexadecimal no actualitzada
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = Mostra les insruccions \u00a7\u00a7dup
config.description.displayDupInstructions = Mostra les instruccions \u00a7\u00a7dup en el codi. Sense elles, el codi es pot compilar f\u00f2cilment, pero algun codi duplicat amb efectes secundaris es podria executar dos cops.
config.name.useRegExprLiteral = Descompila les Expressions Regulars com un literal /patr\u00f3/modi.
config.description.useRegExprLiteral = Utilitza la sintaxi /patr\u00f3/modi en descompilar les expressions regulars. Altrament, s'utilitza new RegExp("pat","mod")
config.name.handleSkinPartsAutomatically = Manipula autom\u00e0ticament les metadades [SkinPart]
config.description.handleSkinPartsAutomatically = Descompilla i edita directament de forma autom\u00f2tica les metadades [SkinPart]. Si es desactiva, l'atribut _skinParts el seu m\u00e8tode 'getter' queda visible i editable manualment.
config.name.simplifyExpressions = Simplifica les expressions
config.description.simplifyExpressions = Aval\u00faa i simplifica les expressions per fer el codi m\u00e9s legible
config.name.resetLetterSpacingOnTextImport = Reinicia l'Espaiat de la Lletra en importar text
config.description.resetLetterSpacingOnTextImport = \u00datil per a tipografies cir\u00edl\u00b7liques, perqu\u00e8 s\u00f3n m\u00e9s amples
config.name.flexSdkLocation = 4) Cam\u00ed del directori Flex SDK
config.description.flexSdkLocation = Ubicaci\u00f3 del Flex SDK d'Adobe. S'utilitza principalment per a la compilaci\u00f3 d'AS3.
config.name.useFlexAs3Compiler=Utilitza el compilador d'AS3 Flex SDK AS3
config.description.useFlexAs3Compiler=Utilitza el compilador d'AS3 de Flex SDK en l'edici\u00f3 directa d'ActionScript (Cal establir el directori de Flex SDK)
config.name.showSetAdvanceValuesMessage = Mostra un altre cop la informaci\u00f3 sobre els valors dels par\u00f2metres avan\u00e7ats
config.description.showSetAdvanceValuesMessage = Mostra un altre cop la informaci\u00f3 sobre com establir els par\u00f2metres avan\u00e7ats
config.name.gui.fontSizeMultiplier = Multiplicador de mida de tipografia
config.description.gui.fontSizeMultiplier = Multiplicador de mida de tipografia
#Do not translate the Font Styles (Plain... etc.)
config.name.gui.sourceFont = Estil de tipografia origen
config.description.gui.sourceFont = NomTipo-EstilTipo(Plain,Bold,Italic,BoldItalic)-MidaTipo
