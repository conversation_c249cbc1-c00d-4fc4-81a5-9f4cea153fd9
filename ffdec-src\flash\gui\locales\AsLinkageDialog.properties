# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
dialog.title = AS linkage
button.ok = OK
button.proceed = Proceed
button.cancel = Cancel
identifier = ActionScript identifier:
classname = ActionScript2 class (fully qualified):
class.parentname = Parent class name (fully qualified):
error.alreadyExistsClass = Error: This class is already exists
error.cannotRemoveIdentifierClassExists = Error: Cannot remove identifier, class already exists
linkage.notfound.exportAssets.where = Where to store the linkage data:
linkage.notfound.exportAssets.where.existing = Existing ExportAssets tag
linkage.notfound.exportAssets.where.new = New ExportAssets tag