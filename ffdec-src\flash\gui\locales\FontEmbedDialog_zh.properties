# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
range.description = %name% (%total%\u4e2a\u5b57\u7b26\u4e2d\u5b58\u5728%available%\u4e2a)
dialog.title = \u5b57\u4f53\u5d4c\u5165
label.individual = \u5355\u4e2a\u5b57\u7b26:
button.loadfont = \u4ece\u786c\u76d8\u4e2d\u8f7d\u5165\u5b57\u4f53...
filter.ttf = TrueType\u5b57\u4f53\u6587\u4ef6(*.ttf)
error.invalidfontfile = \u65e0\u6548\u7684\u5b57\u4f53\u6587\u4ef6
error.cannotreadfontfile = \u65e0\u6cd5\u8bfb\u53d6\u5b57\u4f53\u6587\u4ef6
installed = \u5df2\u5b89\u88c5: 
ttffile.noselection = TTF\u6587\u4ef6: <select>
ttffile.selection = TTF\u6587\u4ef6: %fontname% (%filename%)
allcharacters = \u6240\u6709\u5b57\u7b26(%available%\u4e2a\u5b57\u7b26)
#after 14.0.0
ascentdescentleading = \u8bbe\u7f6e\u4e0a\u95f4\u8ddd,\u4e0b\u95f4\u8ddd\u548c\u884c\u8ddd
#after 19.1.2
font.name = \u5b57\u4f53\u540d\u79f0:
font.name.default = \u6211\u7684\u5b57\u4f53
font.source = \u6e90:
