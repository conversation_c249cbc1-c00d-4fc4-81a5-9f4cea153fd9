# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = \u9ad8\u7ea7\u8bbe\u7f6e
advancedSettings.restartConfirmation = \u60a8\u5fc5\u987b\u91cd\u542f\u7a0b\u5e8f\u4ee5\u4f7f\u4fee\u6539\u7684\u8bbe\u7f6e\u751f\u6548\uff0c\u8981\u7acb\u523b\u91cd\u542f\u7a0b\u5e8f\u5417\uff1f
advancedSettings.columns.name = \u540d\u79f0
advancedSettings.columns.value = \u503c
advancedSettings.columns.description = \u63cf\u8ff0
default = \u9ed8\u8ba4
config.group.name.export = \u5bfc\u51fa
config.group.description.export = \u5bfc\u51fa\u76f8\u5173\u8bbe\u7f6e
config.group.name.script = \u811a\u672c
config.group.description.script = \u4ee3\u7801\u53cd\u7f16\u8bd1\u76f8\u5173
config.group.name.update = \u66f4\u65b0
config.group.description.update = \u8f6f\u4ef6\u66f4\u65b0\u68c0\u67e5
config.group.name.format = \u6392\u7248
config.group.description.format = \u4ee3\u7801\u663e\u793a\u7684\u76f8\u5173\u8bbe\u7f6e
config.group.name.limit = \u9650\u5236
config.group.description.limit = \u6df7\u6dc6\u4ee3\u7801\u7b49\u7684\u53cd\u7f16\u8bd1\u9650\u5236\u76f8\u5173
config.group.name.ui = \u754c\u9762
config.group.description.ui = \u7528\u6237\u754c\u9762\u8bbe\u7f6e
config.group.name.debug = \u8c03\u8bd5
config.group.description.debug = \u8c03\u8bd5\u76f8\u5173\u7684\u8bbe\u7f6e
config.group.name.display = \u663e\u793a
config.group.description.display = Flash\u7b49\u5bf9\u8c61\u663e\u793a\u76f8\u5173
config.group.name.decompilation = \u53cd\u7f16\u8bd1
config.group.description.decompilation = \u5168\u5c40\u53cd\u7f16\u8bd1\u76f8\u5173\u7684\u529f\u80fd
config.group.name.other = \u5176\u5b83
config.group.description.other = \u5176\u5b83\u672a\u5206\u7c7b\u7684\u8bbe\u7f6e
config.name.openMultipleFiles = \u6253\u5f00\u591a\u4e2a\u6587\u4ef6
config.description.openMultipleFiles = \u5141\u8bb8\u5728\u4e00\u4e2a\u7a97\u53e3\u4e2d\u540c\u65f6\u6253\u5f00\u591a\u4e2a\u6587\u4ef6
config.name.decompile = \u663e\u793aAS\u6e90\u4ee3\u7801
config.description.decompile = \u60a8\u53ef\u4ee5\u7981\u7528AS\u53cd\u7f16\u8bd1\uff0c\u53ea\u663e\u793aP-code
config.name.dumpView = \u8f6c\u50a8\u89c6\u56fe
config.description.dumpView = \u67e5\u770b\u539f\u59cb\u6570\u636e\u8f6c\u50a8
config.name.useHexColorFormat = \u5341\u516d\u8fdb\u5236\u989c\u8272\u683c\u5f0f
config.description.useHexColorFormat = \u663e\u793a\u5341\u516d\u8fdb\u5236\u683c\u5f0f\u989c\u8272
config.name.parallelSpeedUp = \u591a\u7ebf\u7a0b\u52a0\u901f
config.description.parallelSpeedUp = \u4ec5\u53ef\u52a0\u5feb\u53cd\u7f16\u8bd1\u901f\u5ea6\uff0c\u90e8\u5206\u5355\u7ebf\u7a0b\u529f\u80fd\u53ef\u80fd\u4f1a\u51fa\u73b0\u95ee\u9898
config.name.parallelSpeedUpThreadCount = \u7ebf\u7a0b\u6570(0 = auto)
config.description.parallelSpeedUpThreadCount = \u591a\u7ebf\u7a0b\u52a0\u901f\u7684\u7ebf\u7a0b\u6570\u91cf.0 = processor count - 1
config.name.autoDeobfuscate = \u81ea\u52a8\u53cd\u6df7\u6dc6
config.description.autoDeobfuscate = \u5728\u53cd\u7f16\u8bd1ActionScript\u4e4b\u524d\uff0c\u5bf9\u6bcf\u4e2a\u6587\u4ef6\u8fd0\u884c\u53cd\u6df7\u6dc6
config.name.cacheOnDisk = \u786c\u76d8\u7f13\u5b58
config.description.cacheOnDisk = \u4e0d\u5360\u7528\u5185\u5b58\uff0c\u800c\u5728\u786c\u76d8\u9a71\u52a8\u5668\u4e0a\u7f13\u5b58\u5df2\u7ecf\u53cd\u7f16\u8bd1\u7684\u90e8\u5206
config.name.internalFlashViewer = \u4f7f\u7528\u5185\u90e8Flash Player
config.description.internalFlashViewer = \u4f7f\u7528JPEXS Flash Viewer\u6765\u663e\u793aFlash\u63a7\u4ef6
config.name.gotoMainClassOnStartup = \u542f\u52a8\u65f6\u8f6c\u5230\u4e3b\u7c7b (AS3)
config.description.gotoMainClassOnStartup = \u5728SWF\u6253\u5f00\u65f6\u8f6c\u5230\u5230AS3\u6587\u4ef6\u7684\u6587\u6863\u7c7b
config.name.autoRenameIdentifiers = \u81ea\u52a8\u91cd\u547d\u540d\u6807\u8bc6\u7b26
config.description.autoRenameIdentifiers = \u5728SWF\u52a0\u8f7d\u65f6\u81ea\u52a8\u91cd\u547d\u540d\u65e0\u6548\u6807\u8bc6\u7b26
config.name.offeredAssociation = (\u5185\u90e8)\u5173\u8054SWF\u6587\u4ef6\u663e\u793a
config.description.offeredAssociation = \u5df2\u7ecf\u663e\u793a\u6709\u5173\u6587\u4ef6\u5173\u8054\u7684\u5bf9\u8bdd\u6846
config.name.decimalAddress = \u4f7f\u7528\u5341\u8fdb\u5236\u5730\u5740
config.description.decimalAddress = \u4f7f\u7528\u5341\u8fdb\u5236\u5730\u5740\u800c\u4e0d\u662f\u5341\u516d\u8fdb\u5236
config.name.showAllAddresses = \u663e\u793a\u6240\u6709\u5730\u5740
config.description.showAllAddresses = \u663e\u793a\u6240\u6709AS\u5730\u5740
config.name.useFrameCache = \u4f7f\u7528\u5e27\u7f13\u5b58
config.description.useFrameCache = \u5728\u518d\u6b21\u6e32\u67d3\u4e4b\u524d\u7f13\u5b58\u5e27
config.name.useRibbonInterface = \u529f\u80fd\u533a\u754c\u9762
config.description.useRibbonInterface = \u5173\u95ed\u4ee5\u4f7f\u7528\u4e0d\u5e26\u529f\u80fd\u533a\u83dc\u5355\u7684\u7ecf\u5178\u754c\u9762
config.name.openFolderAfterFlaExport = FLA\u5bfc\u51fa\u540e\u6253\u5f00\u6587\u4ef6\u5939
config.description.openFolderAfterFlaExport = \u5bfc\u51faFLA\u6587\u4ef6\u540e\u663e\u793a\u8f93\u51fa\u76ee\u5f55
config.name.useDetailedLogging = FFDec\u8be6\u7ec6\u65e5\u5fd7\u8bb0\u5f55
config.description.useDetailedLogging = \u8bb0\u5f55\u8be6\u7ec6\u7684\u9519\u8bef\u6d88\u606f\u548c\u4fe1\u606f\u4ee5\u8c03\u8bd5FFDec
config.name._debugMode = FFDec\u8c03\u8bd5\u6a21\u5f0f
config.description._debugMode = \u8fdb\u5165\u8c03\u8bd5FFDec\u7684\u6a21\u5f0f\uff0c\u5e76\u6253\u5f00\u8c03\u8bd5\u83dc\u5355\u3002\u8fd9\u4e0eSWF\u8c03\u8bd5\u529f\u80fd\u65e0\u5173\u3002
config.name.resolveConstants = \u89e3\u6790AS1/2 p-code\u4e2d\u7684\u5e38\u91cf
config.description.resolveConstants = \u5173\u95ed\u6b64\u9009\u9879\u53ef\u5728P-code\u7a97\u53e3\u4e2d\u663e\u793a" constantxx"\u800c\u4e0d\u662f\u5b9e\u9645\u503c
config.name.sublimiter = \u5b50\u4ee3\u7801\u9650\u5236
config.description.sublimiter = \u6df7\u6dc6\u4ee3\u7801\u7684\u5b50\u4ee3\u7801\u9650\u5236\u3002
config.name.exportTimeout = \u5bfc\u51fa\u8d85\u65f6(\u79d2)
config.description.exportTimeout = \u5230\u8fbe\u6b64\u65f6\u95f4\u540e\uff0c\u53cd\u7f16\u8bd1\u5668\u5c06\u505c\u6b62\u5bfc\u51fa
config.name.decompilationTimeoutFile = \u5355\u6587\u4ef6\u53cd\u7f16\u8bd1\u8d85\u65f6(\u79d2)
config.description.decompilationTimeoutFile = \u8fbe\u5230\u8fd9\u4e2a\u65f6\u95f4\u540e\uff0c\u8f6f\u4ef6\u5c06\u505c\u6b62AS\u7684\u53cd\u7f16\u8bd1
config.name.paramNamesEnable = \u5728AS3\u542f\u7528\u53c2\u6570\u540d\u79f0
config.description.paramNamesEnable = \u5728\u53cd\u7f16\u8bd1\u4e2d\u4f7f\u7528\u53c2\u6570\u540d\u79f0\u53ef\u80fd\u4f1a\u5bfc\u81f4\u95ee\u9898\uff0c\u56e0\u4e3aFlash CS 5.5\u7b49\u5b98\u65b9\u7a0b\u5e8f\u4f1a\u63d2\u5165\u9519\u8bef\u7684\u53c2\u6570\u540d\u79f0\u7d22\u5f15
config.name.displayFileName = \u5728\u6807\u9898\u663e\u793aSWF\u540d\u79f0
config.description.displayFileName = \u5728\u7a97\u53e3\u6807\u9898\u4e2d\u663e\u793aSWF\u6587\u4ef6/URL\u540d\u79f0(\u7136\u540e\u53ef\u4ee5\u8fdb\u884c\u5c4f\u5e55\u622a\u56fe)
config.name._debugCopy = FFDec\u8c03\u8bd5\u91cd\u7f16\u8bd1
config.description._debugCopy = \u5c1d\u8bd5\u5728\u6253\u5f00\u540e\u7acb\u5373\u518d\u6b21\u7f16\u8bd1SWF\u6587\u4ef6\uff0c\u4ee5\u786e\u4fdd\u5b83\u751f\u6210\u76f8\u540c\u7684\u4e8c\u8fdb\u5236\u4ee3\u7801\uff0c\u4ec5\u7528\u4e8e\u8c03\u8bd5FFDec\uff01
config.name.dumpTags = \u8f6c\u50a8\u6807\u7b7e\u5230\u63a7\u5236\u53f0
config.description.dumpTags = \u5728\u8bfb\u53d6SWF\u6587\u4ef6\u65f6\u5c06\u6807\u7b7e\u8f6c\u50a8\u5230\u63a7\u5236\u53f0
config.name.decompilationTimeoutSingleMethod = AS3:\u5355\u65b9\u6cd5\u53cd\u7f16\u8bd1\u8d85\u65f6(\u79d2)
config.description.decompilationTimeoutSingleMethod = \u5728\u67d0\u4e2a\u65b9\u6cd5\u8fbe\u5230\u6b64\u65f6\u95f4\u540e\uff0c\u8f6f\u4ef6\u5c06\u505c\u6b62AS\u53cd\u7f16\u8bd1
config.name.lastRenameType = (\u5185\u90e8)\u4e0a\u6b21\u91cd\u547d\u540d\u7c7b\u578b
config.description.lastRenameType = \u4e0a\u6b21\u4f7f\u7528\u7684\u91cd\u547d\u540d\u6807\u8bc6\u7b26\u7c7b\u578b
config.name.lastSaveDir = (\u5185\u90e8)\u4e0a\u6b21\u4fdd\u5b58\u76ee\u5f55
config.description.lastSaveDir = \u4e0a\u6b21\u4f7f\u7528\u7684\u4fdd\u5b58\u76ee\u5f55
config.name.lastOpenDir = (\u5185\u90e8)\u4e0a\u6b21\u6253\u5f00\u76ee\u5f55
config.description.lastOpenDir = \u4e0a\u6b21\u4f7f\u7528\u7684\u6253\u5f00\u76ee\u5f55
config.name.lastExportDir = (\u5185\u90e8)\u4e0a\u6b21\u5bfc\u51fa\u76ee\u5f55
config.description.lastExportDir = \u4e0a\u6b21\u4f7f\u7528\u7684\u5bfc\u51fa\u76ee\u5f55
config.name.locale = \u8bed\u8a00
config.description.locale = \u8bed\u8a00\u73af\u5883\u6807\u8bc6\u7b26
config.name.registerNameFormat = \u5bc4\u5b58\u5668\u53d8\u91cf\u683c\u5f0f
config.description.registerNameFormat = \u672c\u5730\u5bc4\u5b58\u5668\u53d8\u91cf\u540d\u79f0\u7684\u683c\u5f0f\u3002 \u4f7f\u7528%d\u4f5c\u4e3a\u5bc4\u5b58\u5668\u53f7\u3002
config.name.maxRecentFileCount = \u6700\u8fd1\u6587\u4ef6\u6700\u5927\u6570\u91cf
config.description.maxRecentFileCount = \u6700\u8fd1\u6587\u4ef6\u7684\u6700\u5927\u6570\u91cf
config.name.recentFiles = (\u5185\u90e8)\u6700\u8fd1\u7684\u6587\u4ef6
config.description.recentFiles = \u6700\u8fd1\u6253\u5f00\u7684\u6587\u4ef6
config.name.fontPairingMap = (\u5185\u90e8)\u5b57\u4f53\u914d\u5bf9\u5bfc\u5165
config.description.fontPairingMap = \u5b57\u4f53\u914d\u5bf9\u5bfc\u5165\u65b0\u7684\u5b57\u7b26
config.name.lastUpdatesCheckDate = (\u5185\u90e8)\u6700\u8fd1\u66f4\u65b0\u68c0\u67e5\u65e5\u671f
config.description.lastUpdatesCheckDate = \u4e0a\u6b21\u4ece\u670d\u52a1\u5668\u68c0\u67e5\u66f4\u65b0\u7684\u65e5\u671f
config.name.gui.window.width = (\u5185\u90e8)\u4e0a\u6b21\u7a97\u53e3\u5bbd\u5ea6
config.description.gui.window.width = \u6700\u540e\u4fdd\u5b58\u7684\u7a97\u53e3\u5bbd\u5ea6
config.name.gui.window.height = (\u5185\u90e8)\u4e0a\u6b21\u7a97\u53e3\u9ad8\u5ea6
config.description.gui.window.height = \u6700\u540e\u4fdd\u5b58\u7684\u7a97\u53e3\u9ad8\u5ea6
config.name.gui.window.maximized.horizontal = (\u5185\u90e8)\u7a97\u53e3\u6700\u5927\u5316\u5bbd\u5ea6
config.description.gui.window.maximized.horizontal = \u6700\u540e\u7a97\u53e3\u72b6\u6001 - \u6a2a\u5411\u6700\u5927\u5316
config.name.gui.window.maximized.vertical = (\u5185\u90e8)\u7a97\u53e3\u6700\u5927\u5316\u9ad8\u5ea6
config.description.gui.window.maximized.vertical = \u6700\u540e\u7a97\u53e3\u72b6\u6001 - \u5782\u76f4\u6700\u5927\u5316
config.name.gui.avm2.splitPane.dividerLocationPercent = (\u5185\u90e8)AS3\u5206\u79bb\u5668\u4f4d\u7f6e
config.description.gui.avm2.splitPane.dividerLocationPercent = 
config.name.gui.actionSplitPane.dividerLocationPercent = (\u5185\u90e8)AS1/2\u5206\u79bb\u5668\u4f4d\u7f6e
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (\u5185\u90e8)\u9884\u89c8\u5206\u79bb\u5668\u4f4d\u7f6e
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent = (\u5185\u90e8)\u5206\u79bb\u5668\u4f4d\u7f6e1
config.description.gui.splitPane1.dividerLocationPercent = 
config.name.gui.splitPane2.dividerLocationPercent = (\u5185\u90e8)\u5206\u79bb\u5668\u4f4d\u7f6e2
config.description.gui.splitPane2.dividerLocationPercent = 
config.name.saveAsExeScaleMode = \u53e6\u5b58\u4e3aEXE\u7684\u7f29\u653e\u6a21\u5f0f
config.description.saveAsExeScaleMode = EXE\u5bfc\u51fa\u7684\u7f29\u653e\u6a21\u5f0f
config.name.syntaxHighlightLimit = \u8bed\u6cd5\u9ad8\u4eae\u6700\u5927\u5b57\u7b26
config.description.syntaxHighlightLimit = \u8bed\u6cd5\u9ad8\u4eae\u5f00\u542f\u65f6\u7684\u6700\u5927\u5b57\u7b26\u6570
config.name.guiFontPreviewSampleText = (\u5185\u90e8)\u4e0a\u6b21\u5b57\u4f53\u9884\u89c8\u793a\u4f8b\u6587\u672c
config.description.guiFontPreviewSampleText = \u6700\u540e\u5b57\u4f53\u9884\u89c8\u793a\u4f8b\u6587\u672c\u5217\u8868\u7d22\u5f15
config.name.gui.fontPreviewWindow.width = (\u5185\u90e8)\u4e0a\u6b21\u5b57\u4f53\u9884\u89c8\u7a97\u53e3\u5bbd\u5ea6
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (\u5185\u90e8)\u4e0a\u6b21\u5b57\u4f53\u9884\u89c8\u7a97\u53e3\u9ad8\u5ea6
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (\u5185\u90e8)\u4e0a\u6b21\u5b57\u4f53\u9884\u89c8\u7a97\u53e3X
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (\u5185\u90e8)\u4e0a\u6b21\u5b57\u4f53\u9884\u89c8\u7a97\u53e3Y
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = \u6bcf\u4e2a\u7f29\u8fdb\u7684\u5b57\u7b26\u6570
config.description.formatting.indent.size = \u4e00\u4e2a\u7f29\u8fdb\u7684\u7a7a\u683c(\u6216\u5236\u8868\u7b26)\u6570
config.name.formatting.indent.useTabs = \u5236\u8868\u7b26\u7f29\u8fdb
config.description.formatting.indent.useTabs = \u4f7f\u7528\u5236\u8868\u7b26\u800c\u4e0d\u662f\u7a7a\u683c\u8fdb\u884c\u7f29\u8fdb
config.name.beginBlockOnNewLine = \u65b0\u884c\u5927\u62ec\u53f7
config.description.beginBlockOnNewLine = \u5728\u65b0\u884c\u7684\u5757\u5f00\u59cb\u65f6\u4f7f\u7528\u5927\u62ec\u53f7
config.name.check.updates.delay = \u66f4\u65b0\u68c0\u67e5\u5ef6\u8fdf
config.description.check.updates.delay = \u8f6f\u4ef6\u542f\u52a8\u4e0e\u66f4\u65b0\u81ea\u52a8\u68c0\u67e5\u4e4b\u95f4\u7684\u6700\u77ed\u65f6\u95f4
config.name.check.updates.stable = \u7a33\u5b9a\u7248\u672c
config.description.check.updates.stable = \u68c0\u67e5\u7a33\u5b9a\u7248\u672c\u66f4\u65b0
config.name.check.updates.nightly = \u6bcf\u591c\u6784\u5efa\u7248\u672c
config.description.check.updates.nightly = \u68c0\u67e5\u6bcf\u591c\u6784\u5efa\u7248\u672c\u66f4\u65b0
config.name.check.updates.enabled = \u542f\u7528\u66f4\u65b0\u68c0\u67e5
config.description.check.updates.enabled = \u5728\u5e94\u7528\u7a0b\u5e8f\u542f\u52a8\u65f6\u81ea\u52a8\u68c0\u67e5\u66f4\u65b0
config.name.export.formats = (\u5185\u90e8)\u5bfc\u51fa\u683c\u5f0f
config.description.export.formats = \u4e0a\u6b21\u4f7f\u7528\u7684\u5bfc\u51fa\u683c\u5f0f
config.name.textExportSingleFile = \u5c06\u6587\u672c\u5bfc\u51fa\u5230\u5355\u6587\u4ef6
config.description.textExportSingleFile = \u5c06\u6587\u672c\u5bfc\u51fa\u5230\u4e00\u4e2a\u6587\u4ef6\u800c\u4e0d\u662f\u591a\u4e2a\u6587\u4ef6
config.name.textExportSingleFileSeparator = \u5355\u6587\u4ef6\u5bfc\u51fa\u6587\u672c\u7684\u5206\u9694\u7b26
config.description.textExportSingleFileSeparator = \u5355\u6587\u4ef6\u6587\u672c\u5bfc\u51fa\u4e2d\u5206\u5272\u5404\u6587\u672c\u7684\u5206\u9694\u7b26
config.name.textExportSingleFileRecordSeparator = \u5355\u6587\u4ef6\u5bfc\u51fa\u6587\u672c\u7684\u8bb0\u5f55\u5206\u9694\u7b26
config.description.textExportSingleFileRecordSeparator = \u5355\u6587\u4ef6\u6587\u672c\u5bfc\u51fa\u4e2d\u63d2\u5165\u6587\u672c\u8bb0\u5f55\u4e4b\u95f4\u7684\u6587\u672c
config.name.warning.experimental.as12edit = AS1/2\u76f4\u63a5\u7f16\u8f91\u8b66\u544a
config.description.warning.experimental.as12edit = \u5728AS1/2\u76f4\u63a5\u7f16\u8f91\u65f6\u663e\u793a\u5b9e\u9a8c\u6027\u8b66\u544a
config.name.warning.experimental.as3edit = AS3\u76f4\u63a5\u7f16\u8f91\u8b66\u544a
config.description.warning.experimental.as3edit = \u5728AS3\u76f4\u63a5\u7f16\u8f91\u65f6\u663e\u793a\u5b9e\u9a8c\u6027\u8b66\u544a
config.name.packJavaScripts = \u6253\u5305JavaScript
config.description.packJavaScripts = \u5bf9\u4f7f\u7528Canvas Export\u521b\u5efa\u7684\u811a\u672c\u8fd0\u884cJS\u6253\u5305\u5668\u3002
config.name.textExportExportFontFace = \u5728SVG\u5bfc\u51fa\u4e2d\u4f7f\u7528font-face
config.description.textExportExportFontFace = \u4f7f\u7528font-face\u800c\u4e0d\u662f\u5f62\u72b6\u5c06\u5b57\u4f53\u6587\u4ef6\u5d4c\u5165SVG
config.name.lzmaFastBytes = LZMA\u5feb\u901f\u5b57\u8282(\u6709\u6548\u503c:5-255)
config.description.lzmaFastBytes = LZMA\u7f16\u7801\u5668\u7684\u5feb\u901f\u5b57\u8282\u53c2\u6570
config.name.pluginPath = \u63d2\u4ef6\u8def\u5f84
config.description.pluginPath = -
config.name.showMethodBodyId = \u663e\u793a\u65b9\u6cd5\u4e3b\u4f53id
config.description.showMethodBodyId = \u663e\u793a\u7528\u4e8e\u547d\u4ee4\u884c\u5bfc\u5165\u7684\u65b9\u6cd5\u4e3b\u4f53\u7684id
config.name.export.zoom = (\u5185\u90e8)\u5bfc\u51fa\u7f29\u653e
config.description.export.zoom = \u4e0a\u6b21\u4f7f\u7528\u7684\u5bfc\u51fa\u7f29\u653e
config.name.debuggerPort = \u8c03\u8bd5\u7aef\u53e3
config.description.debuggerPort = \u7528\u4e8esocket\u8c03\u8bd5\u7684\u7aef\u53e3
config.name.displayDebuggerInfo = (\u5185\u90e8)\u663e\u793a\u8c03\u8bd5\u5668\u4fe1\u606f
config.description.displayDebuggerInfo = \u5728\u5207\u6362\u8c03\u8bd5\u5668\u4e4b\u524d\u663e\u793a\u6709\u5173\u8c03\u8bd5\u5668\u7684\u4fe1\u606f
config.name.randomDebuggerPackage = \u4f7f\u7528\u968f\u673a\u8c03\u8bd5\u5305\u540d
config.description.randomDebuggerPackage = \u8fd9\u4f1a\u5c06Debugger\u5305\u91cd\u547d\u540d\u4e3a\u968f\u673a\u5b57\u7b26\u4e32\uff0c\u8fd9\u4f7f\u5f97AS\u5f88\u96be\u68c0\u6d4b\u5230\u8c03\u8bd5\u5668\u7684\u5b58\u5728
config.name.lastDebuggerReplaceFunction = (\u5185\u90e8)\u6700\u540e\u4e00\u6b21\u9009\u62e9\u7684trace\u66ff\u6362
config.description.lastDebuggerReplaceFunction = \u5728\u7528\u8c03\u8bd5\u5668\u66ff\u6362\u8ddf\u8e2a\u529f\u80fd\u4e2d\u6700\u540e\u9009\u62e9\u7684\u51fd\u6570\u540d\u79f0
config.name.getLocalNamesFromDebugInfo = AS3:\u4ece\u8c03\u8bd5\u4fe1\u606f\u83b7\u53d6\u672c\u5730\u5bc4\u5b58\u5668\u540d\u79f0
config.description.getLocalNamesFromDebugInfo = \u5982\u679c\u5b58\u5728\u8c03\u8bd5\u4fe1\u606f\uff0c\u5219\u5c06\u672c\u5730\u5bc4\u5b58\u5668\u4ece_loc_x_\u91cd\u547d\u540d\u4e3a\u771f\u5b9e\u540d\u79f0\u3002 \u7531\u4e8e\u67d0\u4e9b\u6df7\u6dc6\u5668\u5728\u90a3\u91cc\u4f7f\u7528\u4e86\u65e0\u6548\u7684\u5bc4\u5b58\u5668\u540d\u79f0\uff0c\u56e0\u6b64\u53ef\u4ee5\u5c06\u5176\u5173\u95ed\u3002
config.name.tagTreeShowEmptyFolders = \u663e\u793a\u7a7a\u6587\u4ef6\u5939
config.description.tagTreeShowEmptyFolders = \u5728\u6807\u7b7e\u6811\u4e2d\u663e\u793a\u7a7a\u6587\u4ef6\u5939
config.name.autoLoadEmbeddedSwfs = \u81ea\u52a8\u8f7d\u5165\u5d4c\u5165\u7684SWF
config.description.autoLoadEmbeddedSwfs = \u4eceDefineBinaryData\u6807\u7b7e\u81ea\u52a8\u52a0\u8f7d\u5d4c\u5165\u5f0fSWF
config.name.overrideTextExportFileName = \u8986\u76d6\u6587\u672c\u5bfc\u51fa\u6587\u4ef6\u540d
config.description.overrideTextExportFileName = \u60a8\u53ef\u4ee5\u81ea\u5b9a\u4e49\u5bfc\u51fa\u6587\u672c\u7684\u6587\u4ef6\u540d\u3002 \u4f7f\u7528{filename}\u5360\u4f4d\u7b26\u53ef\u4ee5\u4f7f\u7528\u5f53\u524dSWF\u6587\u4ef6\u540d\u3002
config.name.showOldTextDuringTextEditing = \u5728\u6587\u672c\u7f16\u8f91\u4e2d\u663e\u793a\u65e7\u6587\u672c
config.description.showOldTextDuringTextEditing = \u5728\u9884\u89c8\u533a\u57df\u663e\u793a\u7070\u8272\u6587\u672c\u6807\u8bb0\u7684\u539f\u59cb\u6587\u672c
config.group.name.import = \u5bfc\u5165
config.group.description.import = \u5bfc\u5165\u76f8\u5173\u8bbe\u7f6e
config.name.textImportResizeTextBoundsMode = \u6587\u672c\u8fb9\u754c\u8c03\u6574\u6a21\u5f0f
config.description.textImportResizeTextBoundsMode = \u6587\u672c\u7f16\u8f91\u540e\uff0c\u6587\u672c\u8fb9\u754c\u7684\u8c03\u6574\u6a21\u5f0f\u3002
config.name.showCloseConfirmation = \u663e\u793a\u5df2\u4fee\u6539SWF\u5173\u95ed\u786e\u8ba4
config.description.showCloseConfirmation = \u518d\u6b21\u663e\u793a\u5df2\u4fee\u6539\u6587\u4ef6\u7684SWF\u5173\u95ed\u786e\u8ba4
config.name.showCodeSavedMessage = \u518d\u6b21\u663e\u793a\u4ee3\u7801\u4fdd\u5b58\u4fe1\u606f
config.description.showCodeSavedMessage = \u518d\u6b21\u663e\u793a\u5df2\u4fdd\u5b58\u4ee3\u7801\u7684\u4fe1\u606f
config.name.showTraitSavedMessage = \u518d\u6b21\u663e\u793a\u7279\u5f81\u4fdd\u5b58\u4fe1\u606f
config.description.showTraitSavedMessage = \u518d\u6b21\u663e\u793a\u5df2\u4fdd\u5b58\u7684\u7279\u5f81\u4fe1\u606f
config.name.updateProxyAddress = \u7528\u4e8e\u68c0\u67e5\u66f4\u65b0\u7684Http\u4ee3\u7406\u5730\u5740
config.description.updateProxyAddress = \u7528\u4e8e\u68c0\u67e5\u66f4\u65b0\u7684Http\u4ee3\u7406\u5730\u5740\u3002 \u683c\u5f0f:example.com:8080
config.name.editorMode = \u7f16\u8f91\u6a21\u5f0f
config.description.editorMode = \u9009\u62e9\u6587\u672c\u6216\u811a\u672c\u65f6\uff0c\u4f7f\u6587\u672c\u533a\u57df\u53ef\u81ea\u52a8\u8fdb\u5165\u7f16\u8f91\u72b6\u6001
config.name.autoSaveTagModifications = \u81ea\u52a8\u4fdd\u5b58\u6807\u7b7e\u4fee\u6539
config.description.autoSaveTagModifications = \u5728\u6807\u7b7e\u6811\u4e2d\u9009\u62e9\u65b0\u6807\u7b7e\u65f6\u4fdd\u5b58\u66f4\u6539
config.name.saveSessionOnExit = \u9000\u51fa\u65f6\u4fdd\u5b58\u4f1a\u8bdd
config.description.saveSessionOnExit = \u4fdd\u5b58\u5f53\u524d\u4f1a\u8bdd\u5e76\u5728FFDec\u91cd\u65b0\u542f\u52a8\u540e\u91cd\u65b0\u6253\u5f00\u5b83(\u4ec5\u9002\u7528\u4e8e\u5b9e\u9645\u6587\u4ef6)
config.name._showDebugMenu = \u663e\u793aFFDec\u8c03\u8bd5\u83dc\u5355
config.description._showDebugMenu = \u5728\u529f\u80fd\u533a\u4e2d\u663e\u793a\u8c03\u8bd5\u83dc\u5355\uff0c\u7528\u4e8e\u8c03\u8bd5\u53cd\u7f16\u8bd1\u5668\u3002
config.name.allowOnlyOneInstance = \u4ec5\u5141\u8bb8\u4e00\u4e2aFFDec\u5b9e\u4f8b(\u4ec5Windows)
config.description.allowOnlyOneInstance = FFDec\u53ea\u80fd\u8fd0\u884c\u4e00\u6b21\uff0c\u6240\u6709\u6253\u5f00\u7684\u6587\u4ef6\u90fd\u5c06\u6dfb\u52a0\u5230\u4e00\u4e2a\u7a97\u53e3\u4e2d\u3002 \u5b83\u4ec5\u9002\u7528\u4e8eWindows\u64cd\u4f5c\u7cfb\u7edf\u3002
config.name.scriptExportSingleFile = \u5c06\u811a\u672c\u5bfc\u51fa\u5230\u5355\u6587\u4ef6
config.description.scriptExportSingleFile = \u5c06\u811a\u672c\u5bfc\u51fa\u5230\u5355\u4e2a\u6587\u4ef6\u800c\u4e0d\u662f\u591a\u4e2a\u6587\u4ef6
config.name.setFFDecVersionInExportedFont = \u4e3a\u5bfc\u51fa\u7684\u5b57\u4f53\u8bbe\u7f6eFFDec\u7248\u672c\u53f7
config.description.setFFDecVersionInExportedFont = \u7981\u7528\u6b64\u8bbe\u7f6e\u540e\uff0cFFDec\u4e0d\u4f1a\u5c06\u5f53\u524d\u7684FFDec\u7248\u672c\u53f7\u6dfb\u52a0\u5230\u5bfc\u51fa\u7684\u5b57\u4f53\u4e2d\u3002
config.name.gui.skin = \u7528\u6237\u754c\u9762\u76ae\u80a4
config.description.gui.skin = \u5916\u89c2\u548c\u98ce\u683c\u76ae\u80a4
config.name.lastSessionFiles = \u4e0a\u6b21\u4f1a\u8bdd\u6587\u4ef6
config.description.lastSessionFiles = \u5305\u542b\u4e0a\u4e00\u4e2a\u4f1a\u8bdd\u4e2d\u6253\u5f00\u7684\u6587\u4ef6
config.name.lastSessionSelection = \u4e0a\u6b21\u4f1a\u8bdd\u9009\u62e9
config.description.lastSessionSelection = \u5305\u542b\u4e0a\u4e00\u4e2a\u4f1a\u8bdd\u7684\u9009\u62e9
config.name.loopMedia = \u5faa\u73af\u58f0\u97f3\u548c\u7cbe\u7075
config.description.loopMedia = \u81ea\u52a8\u91cd\u65b0\u5f00\u59cb\u64ad\u653e\u58f0\u97f3\u548c\u7cbe\u7075
config.name.gui.timeLineSplitPane.dividerLocationPercent = (\u5185\u90e8)\u65f6\u95f4\u8f74\u62c6\u5206\u5668\u4f4d\u7f6e
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = \u7f13\u5b58\u56fe\u50cf
config.description.cacheImages = \u7f13\u5b58\u89e3\u7801\u7684\u56fe\u50cf\u5bf9\u8c61
config.name.swfSpecificConfigs = SWF\u7279\u5b9a\u914d\u7f6e
config.description.swfSpecificConfigs = \u5305\u542bSWF\u7279\u5b9a\u914d\u7f6e
config.name.exeExportMode = EXE\u5bfc\u51fa\u6a21\u5f0f
config.description.exeExportMode = EXE\u5bfc\u51fa\u6a21\u5f0f
config.name.ignoreCLikePackages = \u5ffd\u7565FlashCC/Alchemy\u6216\u7c7b\u4f3c\u7684\u5305
config.description.ignoreCLikePackages = \u901a\u5e38\u65e0\u6cd5\u6b63\u786e\u53cd\u7f16\u8bd1FlashCC/Alchemy\u7684\u5305\u3002 \u60a8\u53ef\u4ee5\u7981\u7528\u5b83\u4eec\u4ee5\u52a0\u5feb\u5176\u4ed6\u5305\u7684\u53cd\u7f16\u8bd1\u901f\u5ea6\u3002
config.name.overwriteExistingFiles = \u8986\u76d6\u73b0\u6709\u6587\u4ef6
config.description.overwriteExistingFiles = \u5728\u5bfc\u51fa\u8fc7\u7a0b\u4e2d\u8986\u76d6\u73b0\u6709\u6587\u4ef6\u3002 \u5f53\u524d\u4ec5\u9002\u7528\u4e8eAS2/3\u811a\u672c
config.name.smartNumberFormatting = \u4f7f\u7528\u667a\u80fd\u6570\u5b57\u683c\u5f0f
config.description.smartNumberFormatting = \u683c\u5f0f\u5316\u7279\u6b8a\u6570\u5b57(\u4f8b\u5982\u989c\u8272\u548c\u65f6\u95f4)
config.name.enableScriptInitializerDisplay = (\u5df2\u79fb\u9664) \u663e\u793a\u811a\u672c\u521d\u59cb\u5316\u5668
config.description.enableScriptInitializerDisplay = \u542f\u7528\u811a\u672c\u521d\u59cb\u5316\u7a0b\u5e8f\u7684\u663e\u793a\u548c\u7f16\u8f91\u3002 \u6b64\u8bbe\u7f6e\u53ef\u4ee5\u5411\u6bcf\u4e2a\u7c7b\u6587\u4ef6\u6dfb\u52a0\u4e00\u4e2a\u6362\u884c\u7b26\u4ee5\u7a81\u51fa\u663e\u793a\u3002
config.name.autoOpenLoadedSWFs = \u5728\u8fd0\u884c\u671f\u95f4\u6253\u5f00\u52a0\u8f7d\u7684SWF(\u5916\u90e8\u67e5\u770b\u5668=\u4ec5WIN)
config.description.autoOpenLoadedSWFs = \u5728FFDec\u5916\u90e8\u64ad\u653e\u5668\u4e2d\u64ad\u653e\u65f6\uff0c\u901a\u8fc7\u8fd0\u884cSWF\u81ea\u52a8\u6253\u5f00\u7531AS3\u7c7b\u52a0\u8f7d\u7a0b\u5e8f\u52a0\u8f7d\u7684\u6240\u6709SWF\u3002 \u6b64\u529f\u80fd\u4ec5Windows\u3002
config.name.lastSessionFileTitles = \u4e0a\u6b21\u4f1a\u8bdd\u6587\u4ef6\u6807\u9898
config.description.lastSessionFileTitles = \u5305\u542b\u4e0a\u4e00\u4e2a\u4f1a\u8bdd\u4e2d\u6253\u5f00\u7684\u6587\u4ef6\u6807\u9898(\u4f8b\u5982\uff0c\u4eceURL\u7b49\u52a0\u8f7d\u65f6)
config.group.name.paths = \u8def\u5f84
config.group.description.paths = \u6240\u9700\u6587\u4ef6\u4f4d\u7f6e
config.group.tip.paths = \u5728<a href="%link1%">Adobe\u5b98\u7f51</a>\u4e0a\u4e0b\u8f7dPlayer\u548cPlayerGlobal\u3002 Flex SDK\u53ef\u4ee5\u5728<a href="%link2%">apache web</a>\u4e0a\u4e0b\u8f7d\u3002
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Flash Player\u76ee\u5f55
config.description.playerLocation = \u72ec\u7acbFlash Player\u53ef\u6267\u884c\u6587\u4ef6\u7684\u4f4d\u7f6e\u3002 \u7528\u4e8e\u8fd0\u884c\u64cd\u4f5c\u3002
config.name.playerDebugLocation = 2) Flash Player\u8c03\u8bd5\u5668\u76ee\u5f55
config.description.playerDebugLocation = \u72ec\u7acb\u7684\u8c03\u8bd5Flash Player\u53ef\u6267\u884c\u6587\u4ef6\u7684\u4f4d\u7f6e\u3002\u7528\u4e8e\u8c03\u8bd5\u64cd\u4f5c\u3002
config.name.playerLibLocation = 3) PlayerGlobal(.swc)\u76ee\u5f55
config.description.playerLibLocation = playerglobal.swc\u5e93\u7684\u4f4d\u7f6e\u3002 \u5b83\u4e3b\u8981\u7528\u4e8eAS3\u7f16\u8bd1\uff0c\u60a8\u53ef\u4ee5\u4eceAdobe\u5b98\u7f51\u4e0b\u8f7d\u3002
config.name.debugHalt = \u5728\u8c03\u8bd5\u5f00\u59cb\u505c\u6b62\u64ad\u653e
config.description.debugHalt = \u5f00\u59cb\u8c03\u8bd5\u65f6\u6682\u505cSWF\u3002
config.name.gui.avm2.splitPane.vars.dividerLocationPercent = (\u5185\u90e8)\u8c03\u8bd5\u83dc\u5355\u62c6\u5206\u5668\u4f4d\u7f6e
config.description.gui.avm2.splitPane.vars.dividerLocationPercent = 
tip = \u63d0\u793a: 
config.name.gui.action.splitPane.vars.dividerLocationPercent = (\u5185\u90e8)AS1/2\u8c03\u8bd5\u83dc\u5355\u5206\u914d\u5668\u4f4d\u7f6e
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = \u66f4\u6539\u5916\u90e8\u64ad\u653e\u5668\u4e2d\u7684SWF\u4e4b\u524d\u7684\u5ef6\u8fdf(\u6beb\u79d2)
config.description.setMovieDelay = \u4e0d\u5efa\u8bae\u5c06\u6b64\u503c\u66f4\u6539\u4e3a1000ms\u4ee5\u4e0b
config.name.warning.svgImport = SVG\u5bfc\u5165\u8b66\u544a
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = \u5c06\u5f62\u72b6\u66ff\u6362\u4e3a\u56fe\u50cf\u65f6\uff0c\u8bf7\u4f7f\u7528\u4e0d\u5e73\u6ed1\u7684\u586b\u5145
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12 = \u5185\u90e8FlashViewer\u4e2d\u7684AS1/2(\u5b9e\u9a8c\u6027)
config.description.internalFlashViewer.execute.as12 = SWF\u64ad\u653e\u8fc7\u7a0b\u4e2d\u6267\u884c\u7684AS1/2\u5c1d\u8bd5\u4f7f\u7528FFDec FlashViewer
config.name.warning.hexViewNotUpToDate = \u663e\u793a\u5341\u516d\u8fdb\u5236\u89c6\u56fe\u8fc7\u65e7\u8b66\u544a
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = \u663e\u793a\u00a7\u00a7dup\u6307\u4ee4
config.description.displayDupInstructions = \u5728\u4ee3\u7801\u4e2d\u663e\u793a\u00a7\u00a7dup\u6307\u4ee4\u3002 \u6ca1\u6709\u5b83\u4eec\uff0c\u4ee3\u7801\u53ef\u4ee5\u5f88\u5bb9\u6613\u5730\u7f16\u8bd1\uff0c\u4f46\u662f\u67d0\u4e9b\u5e26\u6709\u526f\u4f5c\u7528\u7684\u590d\u5236\u4ee3\u7801\u53ef\u4ee5\u6267\u884c\u4e24\u6b21\u3002
config.name.useRegExprLiteral = \u5c06\u6b63\u5219\u8868\u8fbe\u5f0f\u53cd\u7f16\u8bd1\u4e3a/pattern/mod\u6587\u5b57\u3002
config.description.useRegExprLiteral = \u53cd\u7f16\u8bd1\u6b63\u5219\u8868\u8fbe\u5f0f\u65f6\uff0c\u8bf7\u4f7f\u7528/pattern/mod\u8bed\u6cd5\u3002 \u5426\u5219\u4f7f\u7528\u65b0\u7684RegExp("pat","mod")
config.name.handleSkinPartsAutomatically = \u81ea\u52a8\u5904\u7406[SkinPart]\u5143\u6570\u636e
config.description.handleSkinPartsAutomatically = \u81ea\u52a8\u53cd\u7f16\u8bd1\u5e76\u76f4\u63a5\u7f16\u8f91[SkinPart]\u5143\u6570\u636e\u3002 \u7981\u7528\u65f6\uff0c_skinParts\u5c5e\u6027\u53ca\u5176getter\u65b9\u6cd5\u662f\u53ef\u89c1\u7684\uff0c\u53ef\u4ee5\u624b\u52a8\u7f16\u8f91\u3002
config.name.simplifyExpressions = \u7b80\u5316\u8868\u8fbe
config.description.simplifyExpressions = \u8bc4\u4f30\u5e76\u7b80\u5316\u8868\u8fbe\u5f0f\uff0c\u4f7f\u4ee3\u7801\u66f4\u6613\u8bfb
config.name.resetLetterSpacingOnTextImport = \u6587\u672c\u5bfc\u5165\u65f6\u91cd\u7f6e\u5b57\u6bcd\u95f4\u8ddd
config.description.resetLetterSpacingOnTextImport = \u5bf9\u4e8e\u897f\u91cc\u5c14\u5b57\u4f53\u975e\u5e38\u6709\u6548\uff0c\u56e0\u4e3a\u5b83\u4eec\u66f4\u5bbd
config.name.flexSdkLocation = 4) Flex SDK\u76ee\u5f55
config.description.flexSdkLocation = Adobe Flex SDK\u7684\u4f4d\u7f6e\u3002 \u5b83\u4e3b\u8981\u7528\u4e8eAS3\u7f16\u8bd1\u3002
config.name.useFlexAs3Compiler = \u4f7f\u7528Flex SDK AS3\u7f16\u8bd1\u5668
config.description.useFlexAs3Compiler = \u5728ActionScript\u76f4\u63a5\u7f16\u8f91\u7684\u540c\u65f6\u4f7f\u7528Flex SDK\u4e2d\u7684AS3\u7f16\u8bd1\u5668(\u9700\u8981\u8bbe\u7f6eFlex SDK\u76ee\u5f55)
config.name.showSetAdvanceValuesMessage = \u518d\u6b21\u663e\u793a\u6709\u5173\u8bbe\u7f6e\u9ad8\u7ea7\u503c\u7684\u4fe1\u606f
config.description.showSetAdvanceValuesMessage = \u518d\u6b21\u663e\u793a\u6709\u5173\u8bbe\u7f6e\u9ad8\u7ea7\u503c\u7684\u4fe1\u606f
config.name.gui.fontSizeMultiplier = \u5b57\u4f53\u5927\u5c0f\u500d\u6570
config.description.gui.fontSizeMultiplier = \u5b57\u4f53\u5927\u5c0f\u500d\u6570
config.name.graphVizDotLocation = 5) GraphViz Dot\u53ef\u6267\u884c\u6587\u4ef6\u76ee\u5f55
config.description.graphVizDotLocation = GraphViz\u5e94\u7528\u7a0b\u5e8f\u7684dot.exe(\u6216\u7c7b\u4f3c\u7684linux)\u8def\u5f84\uff0c\u7528\u4e8e\u663e\u793aGraph\u3002
#Do not translate the Font Styles which is in the parenthesis:(Plain,Bold,Italic,BoldItalic)
config.name.gui.sourceFont = \u6e90\u5b57\u4f53\u6837\u5f0f
config.description.gui.sourceFont = \u5b57\u4f53\u540d\u79f0-\u5b57\u4f53\u6837\u5f0f(Plain,Bold,Italic,BoldItalic)-\u5b57\u4f53\u5927\u5c0f
#after 11.1.0
config.name.as12DeobfuscatorExecutionLimit = AS1/2\u53bb\u6df7\u6dc6\u5668\u6267\u884c\u9650\u5236
config.description.as12DeobfuscatorExecutionLimit = \u5728AS1/2\u6267\u884c\u53cd\u6df7\u6dc6\u8fc7\u7a0b\u4e2d\u5904\u7406\u7684\u6700\u5927\u6307\u4ee4\u6570
#option that ignore in 8.0.1 and other versions
config.name.showOriginalBytesInPcodeHex = (\u5185\u90e8)\u663e\u793a\u539f\u59cb\u5b57\u8282
config.description.showOriginalBytesInPcodeHex = \u4ee5Pcode\u5341\u516d\u8fdb\u5236\u663e\u793a\u539f\u59cb\u5b57\u8282
config.name.showFileOffsetInPcodeHex = (\u5185\u90e8)\u663e\u793a\u6587\u4ef6\u504f\u79fb
config.description.showFileOffsetInPcodeHex = \u4ee5Pcode\u5341\u516d\u8fdb\u5236\u663e\u793a\u6587\u4ef6\u504f\u79fb
config.name._enableFlexExport = (\u5185\u90e8)\u542f\u7528Flex\u5bfc\u51fa
config.description.enableFlexExport = \u542f\u7528Flex\u5bfc\u51fa
config.name._ignoreAdditionalFlexClasses = (\u5185\u90e8)\u5ffd\u7565\u5176\u4ed6Flex\u7c7b
config.description.ignoreAdditionalFlexClasses = \u5ffd\u7565\u5176\u4ed6Flex\u7c7b
config.name.hwAcceleratedGraphics = 
config.description.hwAcceleratedGraphics =
config.name.gui.avm2.splitPane.docs.dividerLocationPercent = (\u5185\u90e8)\u6587\u6863\u7a97\u683c\u5206\u9694\u4f4d\u7f6e\u767e\u5206\u6bd4
config.description.gui.avm2.splitPane.docs.dividerLocationPercent = \u62c6\u5206\u7a97\u683c\u6587\u6863\u5206\u9694\u7ebf\u4f4d\u7f6e\u767e\u5206\u6bd4
config.name.gui.dump.splitPane.dividerLocationPercent = (\u5185\u90e8)\u8f6c\u50a8\u7a97\u683c\u5206\u9694\u4f4d\u7f6e\u767e\u5206\u6bd4
config.description.gui.dump.splitPane.dividerLocationPercent = \u8f6c\u50a8\u62c6\u5206\u7a97\u683c\u5206\u9694\u7b26\u4f4d\u7f6e\u767e\u5206\u6bd4
#after 11.3.0
config.name.useAdobeFlashPlayerForPreviews = (\u5f03\u7528) \u4f7f\u7528Adobe Flash player\u9884\u89c8
config.description.useAdobeFlashPlayerForPreviews = \u4f7f\u7528Adobe Flash player\u9884\u89c8\u3002\u8b66\u544a: FlashPlayer\u5df2\u505c\u6b62\u66f4\u65b0
#after 12.0.1
config.name.showLineNumbersInPCodeGraphvizGraph = \u5728\u53ef\u89c6\u5316\u89c6\u56fe\u4e2d\u663e\u793a\u884c\u53f7
config.description.showLineNumbersInPCodeGraphvizGraph = \u5728P-code\u53ef\u89c6\u5316\u89c6\u56fe\u4e2d\u663e\u793a\u884c\u53f7
config.name.padAs3PCodeInstructionName = \u586b\u5145AS3 P-code\u6307\u4ee4\u540d
config.description.padAs3PCodeInstructionName = \u7528\u7a7a\u95f4\u586b\u5145AS3 P-code\u6307\u4ee4\u540d
#after 13.0.2
config.name.indentAs3PCode = \u7f29\u8fdb AS3 P-code
config.description.indentAs3PCode = \u7f29\u8fdb AS3 P-code trait/body/code\u5757
config.name.labelOnSeparateLineAs3PCode = AS3 P-code\u6807\u7b7e\u5355\u72ec\u4e00\u884c
config.description.labelOnSeparateLineAs3PCode = \u5c06AS3 P-code\u7684\u6807\u7b7e\u653e\u5728\u5355\u72ec\u7684\u4e00\u884c\u4e0a
config.name.useOldStyleGetSetLocalsAs3PCode = AS3 P-code\u4e2d\u4f7f\u7528getlocal_x\u4ee3\u66ffgetlocalx
config.description.useOldStyleGetSetLocalsAs3PCode = \u4f7f\u7528FFDec12\u7248\u672c\u4ee5\u524d\u7684\u65e7\u5f0fgetlocal_x, setlocal_x
config.name.useOldStyleLookupSwitchAs3PCode = AS3 P-code\u4e2d\u4f7f\u7528\u4e0d\u5e26\u62ec\u53f7\u7684\u65e7\u5f0flookupswitch
config.description.useOldStyleLookupSwitchAs3PCode = \u4f7f\u7528FFDec12\u7248\u672c\u4ee5\u524d\u7684\u65e7\u5f0flookupswitch
#after 13.0.3
config.name.checkForModifications = \u68c0\u67e5\u8f6f\u4ef6\u5916\u7684\u6587\u4ef6\u66f4\u6539\u72b6\u6001
config.description.checkForModifications = \u68c0\u67e5\u8f6f\u4ef6\u5916\u7684\u6587\u4ef6\u4fee\u6539\u5e76\u8be2\u95ee\u91cd\u65b0\u52a0\u8f7d
config.name.warning.initializers = AS3 slot/const\u521d\u59cb\u5316\u7f16\u8f91\u8b66\u544a
config.description.warning.initializers = \u5728AS3 slot/const\u7f16\u8f91\u4e2d\u663e\u793a\u5173\u4e8e\u521d\u59cb\u5316\u7684\u8b66\u544a
config.name.parametersPanelInSearchResults = \u5728\u641c\u7d22\u7ed3\u679c\u4e2d\u663e\u793a\u53c2\u6570\u9762\u677f
config.description.parametersPanelInSearchResults = \u5728\u641c\u7d22\u7ed3\u679c\u7a97\u53e3\u663e\u793a\u9762\u677f\u53c2\u6570\uff0c\u5982\u641c\u7d22\u6587\u672c/\u5ffd\u7565\u5927\u5c0f\u5199/\u6b63\u5219\u8868\u8fbe\u5f0f
config.name.displayAs3PCodeDocsPanel = \u663e\u793aAS3 P-code\u6587\u6863\u9762\u677f
config.description.displayAs3PCodeDocsPanel = \u5728AS3 P-code\u4e2d\u663e\u793a\u5e2e\u52a9\u6587\u6863
config.name.displayAs3TraitsListAndConstantsPanel = \u663e\u793aAS3\u7279\u5f81\u548c\u5e38\u91cf\u9762\u677f
config.description.displayAs3TraitsListAndConstantsPanel = \u5728AS3\u7684\u6807\u7b7e\u6811\u4e0b\u663e\u793atraits\u548cconstants\u5217\u8868\u9762\u677f
#after 14.1.0
config.name.useAsTypeIcons = \u6839\u636e\u9879\u76ee\u7c7b\u578b\u4f7f\u7528\u811a\u672c\u56fe\u6807
config.description.useAsTypeIcons = \u5bf9\u4e0d\u540c\u7684\u811a\u672c\u7c7b\u578b\u4f7f\u7528\u4e0d\u540c\u7684\u56fe\u6807(\u7c7b/\u63a5\u53e3/\u5e27/...)
config.name.limitAs3PCodeOffsetMatching = AS3 P-code\u504f\u79fb\u5339\u914d\u9650\u5236
config.description.limitAs3PCodeOffsetMatching = \u4e0eAS3\u811a\u672c\u504f\u79fb\u5339\u914d\u7684AS3 P-code\u6307\u4ee4\u9650\u5236
#after 14.2.1
config.name.showSlowRenderingWarning = \u6e32\u67d3\u8fc7\u6162\u65f6\u65e5\u5fd7\u8b66\u544a
config.description.showSlowRenderingWarning = \u5185\u90e8\u64ad\u653e\u5668\u8fc7\u6162\u65f6\u65e5\u5fd7\u8b66\u544a
#after 14.3.1
config.name.autoCloseQuotes = \u811a\u672c\u7f16\u8f91\u4e2d\u81ea\u52a8\u5173\u95ed''
config.description.autoCloseQuotes = \u8f93\u5165'\u65f6\u81ea\u52a8\u63d2\u5165\u7b2c\u4e8c\u4e2a
config.name.autoCloseDoubleQuotes = \u811a\u672c\u7f16\u8f91\u4e2d\u81ea\u52a8\u5173\u95ed""
config.description.autoCloseDoubleQuotes = \u8f93\u5165"\u65f6\u81ea\u52a8\u63d2\u5165\u7b2c\u4e8c\u4e2a
config.name.autoCloseBrackets = \u811a\u672c\u7f16\u8f91\u4e2d\u81ea\u52a8\u5173\u95ed[]
config.description.autoCloseBrackets = \u8f93\u5165[\u65f6\u81ea\u52a8\u63d2\u5165]
config.name.autoCloseParenthesis = \u811a\u672c\u7f16\u8f91\u4e2d\u81ea\u52a8\u5173\u95ed()
config.description.autoCloseParenthesis = \u8f93\u5165(\u65f6\u81ea\u52a8\u63d2\u5165)
config.name.showDialogOnError = \u51fa\u9519\u65f6\u5747\u5f39\u51fa\u9519\u8bef\u5bf9\u8bdd\u6846
config.description.showDialogOnError = \u6bcf\u4e2a\u9519\u8bef\u53d1\u751f\u65f6\u90fd\u663e\u793a\u9519\u8bef\u5bf9\u8bdd\u6846
#after 14.4.0
config.name.limitSameChars = \u76f8\u540c\u5b57\u7b26\u9650\u5236
config.description.limitSameChars = P-code\u5b57\u7b26\u4e32\u4e00\u884c\u4e2d\u7684\u76f8\u540c\u5b57\u7b26\u6216\u88ab\u66ff\u6362\u4e3a\\{xx}C (repeat) escape\u524d\u6df7\u6dc6\u540d\u79f0\u7684\u6700\u5927\u503c
#after 14.5.2
config.name.showImportScriptsInfo = \u5bfc\u5165\u811a\u672c\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportScriptsInfo = \u5728\u83dc\u5355\u4e2d\u70b9\u51fb\u5bfc\u5165\u811a\u672c\u540e\uff0c\u663e\u793a\u5bfc\u5165\u811a\u672c\u7684\u6559\u7a0b
config.name.showImportTextInfo = \u5bfc\u5165\u6587\u672c\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportTextInfo = \u5728\u83dc\u5355\u4e2d\u70b9\u51fb\u5bfc\u5165\u6587\u672c\u540e\uff0c\u663e\u793a\u5bfc\u5165\u6587\u672c\u7684\u6559\u7a0b
config.name.showImportSymbolClassInfo = \u5bfc\u5165\u7b26\u53f7\u7c7b\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportSymbolClassInfo = \u5728\u83dc\u5355\u4e2d\u70b9\u51fb\u5bfc\u5165\u7b26\u53f7\u7c7b\u540e\uff0c\u663e\u793a\u5bfc\u5165\u7b26\u53f7\u7c7b\u7684\u6559\u7a0b
config.name.showImportXmlInfo = \u5bfc\u5165XML\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportXmlInfo = \u5728\u83dc\u5355\u4e2d\u70b9\u51fb\u5bfc\u5165XML\u540e\uff0c\u663e\u793a\u5bfc\u5165XML\u7684\u6559\u7a0b
#after 15.1.1
config.name.lastSessionTagListSelection = \u4e0a\u6b21\u4f1a\u8bdd\u6807\u7b7e\u5217\u8868\u9009\u62e9
config.description.lastSessionTagListSelection = \u5305\u542b\u4e0a\u6b21\u4f1a\u8bdd\u5217\u8868\u9009\u62e9\u89c6\u56fe\u4e2d\u7684\u9009\u62e9
config.name.lastView = \u4e0a\u6b21\u89c6\u56fe
config.description.lastView = \u4e0a\u6b21\u663e\u793a\u7684\u89c6\u56fe\u6a21\u5f0f
config.name.swfSpecificCustomConfigs = SWF\u7279\u5b9a\u81ea\u5b9a\u4e49\u914d\u7f6e
config.description.swfSpecificCustomConfigs = \u5305\u542b\u81ea\u5b9a\u4e49\u683c\u5f0f\u7684SWF\u7279\u5b9a\u914d\u7f6e
config.name.warningOpeningReadOnly = \u6253\u5f00\u53ea\u8bfbSWF\u65f6\u8b66\u544a
config.description.warningOpeningReadOnly = \u4ece\u53ea\u8bfb\u6e90\u6253\u5f00SWF\u65f6\u8b66\u544a
# after 16.1.0
config.name.showImportImageInfo = \u5bfc\u5165\u56fe\u50cf\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportImageInfo = \u5728\u83dc\u5355\u4e2d\u70b9\u51fb\u5bfc\u5165\u56fe\u50cf\u540e\uff0c\u663e\u793a\u5bfc\u5165\u56fe\u50cf\u7684\u6559\u7a0b
config.name.autoPlaySwfs = \u81ea\u52a8\u64ad\u653eSWF\u9884\u89c8
config.description.autoPlaySwfs = \u9009\u62e9SWF\u8282\u70b9\u65f6\u81ea\u52a8\u64ad\u653eSWF\u9884\u89c8
config.name.expandFirstLevelOfTreeOnLoad = SWF\u52a0\u8f7d\u65f6\u5c55\u5f00\u4e00\u7ea7\u8282\u70b9
config.description.expandFirstLevelOfTreeOnLoad = \u6253\u5f00SWF\u65f6\u81ea\u52a8\u5c55\u5f00\u5206\u652f\u4e2d\u7684\u4e00\u7ea7\u8282\u70b9
# after 16.2.0
config.name.allowPlacingDefinesIntoSprites = \u5c06define\u6807\u7b7e\u653e\u5165DefineSprite
config.description.allowPlacingDefinesIntoSprites = \u5c06(moving/copying/dragging into)define\u6807\u7b7e\u653e\u5165DefineSprite
config.name.allowDragAndDropInTagListTree = \u6807\u7b7e\u5217\u8868\u62d6\u653e
config.description.allowDragAndDropInTagListTree = \u5141\u8bb8\u5728\u6807\u7b7e\u5217\u8868\u901a\u8fc7\u62d6\u653e\u79fb\u52a8/\u590d\u5236\u6807\u7b7e
config.name.allowMiterClipLinestyle = (\u5df2\u79fb\u9664)\u659c\u5207\u5939\u7ebf\u6837\u5f0f(\u6162)
config.description.allowMiterClipLinestyle = \u5141\u8bb8\u4f7f\u7528\u652f\u6301\u659c\u5207\u5939\u7ebf\u6837\u5f0f\u7684\u81ea\u5b9a\u4e49\u6e32\u67d3\u5668\uff0c\u4f46\u901f\u5ea6\u5f88\u6162
advancedSettings.search = \u641c\u7d22:
# after 16.3.1
config.name.animateSubsprites = \u9884\u89c8\u5b50\u52a8\u753b
config.description.animateSubsprites = \u9884\u89c8\u65f6\u95f4\u8f74\u4e2d\u7684\u5b50\u52a8\u753b
config.name.autoPlayPreviews = \u9884\u89c8\u81ea\u52a8\u64ad\u653e
config.description.autoPlayPreviews = \u81ea\u52a8\u64ad\u653e\u9884\u89c8\u52a8\u753b
config.name.maxCachedTime = \u4e34\u65f6\u7f13\u5b58\u65f6\u95f4
config.description.maxCachedTime = \u9879\u76ee\u4e0d\u8bbf\u95ee\u540e\u4ece\u7f13\u5b58\u79fb\u9664\u7684\u6700\u5927\u65f6\u95f4.0\u4e3a\u65e0\u9650\u5236
config.name.airLibLocation = 6) AIR\u5e93\u8def\u5f84(airglobal.swc)
config.description.airLibLocation = AIR\u5e93(airglobal.swc)\u7684\u8def\u5f84. \u5e38\u7528\u4e8eAS3\u6c47\u7f16.
config.name.showImportShapeInfo = \u5bfc\u5165\u5f62\u72b6\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportShapeInfo = \u5728\u83dc\u5355\u4e2d\u70b9\u51fb\u5bfc\u5165\u5f62\u72b6\u540e\uff0c\u663e\u793a\u5bfc\u5165\u5f62\u72b6\u7684\u6559\u7a0b
config.name.pinnedItemsTagTreePaths = \u6807\u7b7e\u89c6\u56fe\u4e2d\u56fa\u5b9a\u7684\u9879\u76ee\u8def\u5f84
config.description.pinnedItemsTagTreePaths = \u5df2\u56fa\u5b9a\u7684\u6807\u7b7e\u8282\u70b9\u7684\u8def\u5f84
config.name.pinnedItemsTagListPaths = \u6807\u7b7e\u5217\u8868\u4e2d\u56fa\u5b9a\u7684\u9879\u76ee\u8def\u5f84
config.description.pinnedItemsTagListPaths = \u5df2\u56fa\u5b9a\u7684\u6807\u7b7e\u8282\u70b9\u7684\u8def\u5f84
config.name.flattenASPackages = \u5c55\u5f00ActionScript\u5305
config.description.flattenASPackages = \u6bcf\u4e2a\u5305\u5355\u72ec\u4e00\u4e2a\u9879\u76ee\u800c\u4e0d\u662f\u5305\u5206\u652f\u7ed3\u6784
config.name.gui.scale = UI\u7f29\u653e\u8bbe\u7f6e
config.description.gui.scale = \u56fe\u5f62\u754c\u9762\u7684\u7f29\u653e\u8bbe\u7f6e.Mac Retina\u8bbe\u7f6e\u4e3a2.0.\u9700\u8981\u5b8c\u5168\u5173\u95ed\u8f6f\u4ef6
config.name.warning.video.vlc = VLC\u64ad\u653e\u5668\u63d0\u793a
config.description.warning.video.vlc = \u6253\u5f00\u6709DefineVideostream\u6807\u7b7e\u7684SWF\u65f6\uff0c\u663e\u793aVLC media player\u76f8\u5173\u63d0\u793a
config.name.playFrameSounds = \u64ad\u653e\u5e27\u58f0\u97f3
config.description.playFrameSounds = \u663e\u793a\u5e27\u65f6\u64ad\u653e\u58f0\u97f3
config.name.fixAntialiasConflation =
config.description.fixAntialiasConflation = 
config.name.autoPlaySounds = \u81ea\u52a8\u64ad\u653e\u58f0\u97f3
config.description.autoPlaySounds = \u9009\u4e2d\u8282\u70b9\u65f6\u81ea\u52a8\u64ad\u653eDefineSound
config.name.deobfuscateAs12RemoveInvalidNamesAssignments = AS1/2\u53cd\u6df7\u6dc6: \u5220\u9664\u5e26\u6df7\u6dc6\u540d\u79f0\u7684\u53d8\u91cf\u58f0\u660e
config.description.deobfuscateAs12RemoveInvalidNamesAssignments = AS1/2\u53cd\u6df7\u6dc6\u65f6, \u5220\u9664\u5177\u6709\u975e\u6807\u51c6\u540d\u79f0\u7684\u53d8\u91cf\u58f0\u660e. \u8b66\u544a: \u8fd9\u53ef\u80fd\u4f1a\u635f\u574f\u4f9d\u8d56\u6df7\u6dc6\u540d\u79f0\u7684SWF
config.name.gui.splitPanePlace.dividerLocationPercent = (\u5185\u90e8)\u5206\u79bb\u5668\u4f4d\u7f6e
config.description.gui.splitPanePlace.dividerLocationPercent = 
config.name.gui.splitPaneTransform1.dividerLocationPercent = (\u5185\u90e8)\u5206\u79bb\u5668\u8f6c\u63621\u4f4d\u7f6e
config.name.gui.splitPaneTransform2.dividerLocationPercent = (\u5185\u90e8)\u5206\u79bb\u5668\u8f6c\u63622\u4f4d\u7f6e
config.name.gui.transform.lastExpandedCards = (\u5185\u90e8)\u4e0a\u6b21\u5c55\u5f00\u7684\u8f6c\u6362\u5361
config.name.doubleClickNodeToEdit = \u53cc\u51fb\u5f00\u59cb\u7f16\u8f91
config.description.doubleClickNodeToEdit = \u53cc\u51fb\u8282\u70b9\u5bf9\u5176\u5f00\u59cb\u7f16\u8f91
config.name.warningDeobfuscation = \u5207\u6362\u53cd\u6df7\u6dc6\u65f6\u8b66\u544a
config.description.warningDeobfuscation = \u5f00\u5173\u53cd\u6df7\u6dc6\u65f6\u663e\u793a\u8b66\u544a
config.name.warningRenameIdentifiers = \u5207\u6362\u81ea\u52a8\u91cd\u547d\u540d\u6807\u8bc6\u7b26\u65f6\u8b66\u544a
config.description.warningRenameIdentifiers = \u5f00\u5173\u81ea\u52a8\u91cd\u547d\u540d\u6807\u8bc6\u7b26\u65f6\u663e\u793a\u8b66\u544a
config.name.showImportMovieInfo = \u5bfc\u5165\u89c6\u9891\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportMovieInfo = \u5728\u83dc\u5355\u4e2d\u70b9\u51fb\u5bfc\u5165\u89c6\u9891\u540e\uff0c\u663e\u793a\u5bfc\u5165\u89c6\u9891\u7684\u6559\u7a0b
config.name.showImportSoundInfo = \u5bfc\u5165\u58f0\u97f3\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportSoundInfo = \u5728\u83dc\u5355\u4e2d\u70b9\u51fb\u5bfc\u5165\u58f0\u97f3\u540e\uff0c\u663e\u793a\u5bfc\u5165\u58f0\u97f3\u7684\u6559\u7a0b
config.name.svgRetainBounds = \u5bfc\u51faSVG\u65f6\u4fdd\u6301\u5f62\u72b6\u754c\u9650
config.description.svgRetainBounds = \u5bfc\u51faSVG\u65f6, \u5f62\u72b6\u7684x,y\u4f4d\u7f6e\u4e0eSWF\u4e2d\u4fdd\u6301\u4e00\u81f4(\u4f8b\u5982\u6b63\u6216\u8d1f)
config.name.disableBitmapSmoothing = \u7981\u7528\u5e73\u6ed1\u4f4d\u56fe
config.description.disableBitmapSmoothing = \u663e\u793a\u65f6\u7981\u7528\u5e73\u6ed1\u4f4d\u56fe\u586b\u5145 - \u5168\u90e8\u663e\u793a\u4e3a\u975e\u5e73\u6ed1(\u50cf\u7d20).\u4e0d\u9002\u7528\u4e8e\u5bfc\u51fa\u7684\u56fe\u50cf
config.name.pinnedItemsScrollPos = \u56fa\u5b9a\u9879\u76ee\u6eda\u52a8/\u63d2\u5165\u4f4d\u7f6e
config.description.pinnedItemsScrollPos = \u56fa\u5b9a\u9879\u76ee\u7684\u6eda\u52a8/\u63d2\u5165\u4f4d\u7f6e
config.name.maxRememberedScrollposItems = \u6eda\u52a8\u4f4d\u7f6e\u6700\u5927\u8bb0\u5fc6\u6570
config.description.maxRememberedScrollposItems = \u8bb0\u5fc6\u6eda\u52a8\u4f4d\u7f6e\u9879\u76ee\u7684\u6700\u5927\u503c
config.name.rememberScriptsScrollPos = \u8bb0\u4f4f\u811a\u672c\u6eda\u52a8/\u63d2\u5165\u4f4d\u7f6e
config.description.rememberScriptsScrollPos = \u811a\u672c\u6eda\u52a8/\u63d2\u5165\u4f4d\u7f6e\u5728\u5207\u6362\u9879\u76ee\u65f6\u4fdd\u5b58
config.name.rememberFoldersScrollPos = \u8bb0\u4f4f\u6587\u4ef6\u5939\u6eda\u52a8\u4f4d\u7f6e
config.description.rememberFoldersScrollPos = \u6587\u4ef6\u5939\u6eda\u52a8\u4f4d\u7f6e\u5728\u5207\u6362\u9879\u76ee\u65f6\u4fdd\u5b58
#after 18.3.6
config.name.warning.initializers.class = \u5173\u4e8e\u811a\u672c\u521d\u59cb\u5316\u7a0b\u5e8f\u7684AS3\u7c7b\u7279\u5f81\u7f16\u8f91\u8b66\u544a
config.description.warning.initializers.class = \u5728AS3\u7c7b\u7279\u5f81\u7f16\u8f91\u4e2d\u663e\u793a\u6709\u5173\u521d\u59cb\u5316\u7a0b\u5e8f\u7684\u8b66\u544a
#after 18.4.1
config.name.maxCachedNum = \u6bcf\u4e2a\u7f13\u5b58\u7684\u6700\u5927\u7f13\u5b58\u9879\u6570
config.description.maxCachedNum = \u4ece\u7f13\u5b58\u4e2d\u5220\u9664\u65e7\u9879\u76ee\u4e4b\u524d\u7684\u6700\u5927\u7f13\u5b58\u9879\u76ee\u6570.\u66f4\u4f4e\u7684\u6570\u503c = \u66f4\u5c11\u7684\u5185\u5b58, \u5219\u5e94\u7528\u7a0b\u5e8f\u8d8a\u6162. \u66f4\u9ad8\u7684\u6570\u503c = \u66f4\u591a\u7684\u5185\u5b58, \u5219\u5e94\u7528\u7a0b\u5e8f\u8d8a\u5feb.\u5c06\u5176\u8bbe\u7f6e\u4e3a0\u5373\u53ef\u5b9e\u73b0\u65e0\u9650\u5236\u7f13\u5b58.
config.name.warning.cannotencrypt = \u65e0\u6cd5\u4fdd\u5b58\u52a0\u5bc6\u65f6\u53d1\u51fa\u8b66\u544a
config.description.warning.cannotencrypt = \u65e0\u6cd5\u4fdd\u5b58\u4f7f\u7528HARMAN Air\u52a0\u5bc6\u6280\u672f\u52a0\u5bc6\u7684SWF\u6587\u4ef6\u65f6\u663e\u793a\u8b66\u544a
#after 18.5.0
config.name.lastExportEnableEmbed = \u5bfc\u51fa\u5d4c\u5165\u8d44\u6e90\u7684\u6700\u540e\u8bbe\u7f6e
config.description.lastExportEnableEmbed = \u901a\u8fc7[Embed]\u5143\u6570\u636e\u6807\u7b7e\u5bfc\u51fa\u5d4c\u5165\u8d44\u6e90\u7684\u6700\u540e\u8bbe\u7f6e
config.name.lastFlaExportVersion = \u6700\u65b0\u7684FLA\u5bfc\u51fa\u7248\u672c
config.description.lastFlaExportVersion = \u4e0a\u6b21\u5bfc\u51fa\u7684FLA\u7248\u672c
config.name.lastFlaExportCompressed = \u6700\u65b0\u7684FLA\u538b\u7f29\u65b9\u5f0f
config.description.lastFlaExportCompressed = \u4e0a\u6b21\u5bfc\u51fa\u7684FLA\u538b\u7f29\u65b9\u5f0f
#after 19.0.0
config.name.showImportSpriteInfo = \u5bfc\u5165\u7cbe\u7075(sprite)\u524d\u663e\u793a\u4fe1\u606f
config.description.showImportSpriteInfo = \u5728\u83dc\u5355\u4e2d\u5355\u51fb"\u5bfc\u5165\u7cbe\u7075"\u540e\u663e\u793a\u6709\u5173\u5bfc\u5165\u7cbe\u7075\u5982\u4f55\u5de5\u4f5c\u7684\u4e00\u4e9b\u4fe1\u606f
config.name.displayAs12PCodeDocsPanel = \u5728AS1/2 P-code\u4e2d\u663e\u793a\u6587\u6863\u9762\u677f
config.description.displayAs12PCodeDocsPanel = \u5728AS1/2 P-code\u7f16\u8f91\u548c\u5c55\u793a\u4e2d\u663e\u793a\u5305\u542b\u64cd\u4f5c\u6587\u6863\u7684\u9762\u677f
config.name.gui.action.splitPane.docs.dividerLocationPercent = (\u5185\u90e8)AS 1/2\u5206\u9694\u9762\u677f\u6587\u6863\u5206\u9694\u7b26\u4f4d\u7f6e\u767e\u5206\u6bd4
config.description.action.avm2.splitPane.docs.dividerLocationPercent = AS 1/2\u5206\u9694\u9762\u677f\u6587\u6863\u5206\u9694\u7b26\u4f4d\u7f6e\u767e\u5206\u6bd4
#after 19.1.2
config.name.rememberLastScreen = \u8bb0\u4f4f\u4e0a\u6b21\u4f7f\u7528\u7684\u5c4f\u5e55\uff08\u5728\u591a\u4e2a\u663e\u793a\u5668\u4e0a\uff09
config.description.rememberLastScreen = \u5728\u591a\u5c4f\u5e55\u914d\u7f6e\u4e2d\u8bb0\u4f4f\u4e0a\u6b21\u4f7f\u7528\u7684\u5c4f\u5e55
config.name.lastMainWindowScreenIndex = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55\u7d22\u5f15
config.description.lastMainWindowScreenIndex = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55\u7d22\u5f15
config.name.lastMainWindowScreenX = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55X
config.description.lastMainWindowScreenX = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55X\u5750\u6807
config.name.lastMainWindowScreenY = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55Y
config.description.lastMainWindowScreenY = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55Y\u5750\u6807
config.name.lastMainWindowScreenWidth = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55\u5bbd\u5ea6
config.description.lastMainWindowScreenWidth = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55\u5bbd\u5ea6
config.name.lastMainWindowScreenHeight = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55\u5bbd\u5ea6
config.description.lastMainWindowScreenHeight = \u6700\u540e\u4e00\u4e2a\u4e3b\u7a97\u53e3\u7684\u5c4f\u5e55\u5bbd\u5ea6
config.name.displayAs12PCodePanel = \u663e\u793aAS1/2 P-code\u9762\u677f
config.description.displayAs12PCodePanel = \u663e\u793aActionScript 1\u548c2\u7684\u53cd\u6c47\u7f16P-code\u64cd\u4f5c\u9762\u677f
config.name.displayAs3PCodePanel = \u663e\u793aAS3 P-code\u9762\u677f
config.description.displayAs3PCodePanel = \u663e\u793aActionScript 3\u7684\u53cd\u6c47\u7f16P-code\u6307\u4ee4\u9762\u677f
config.name.flaExportUseMappedFontLayout = FLA\u5bfc\u51fa-\u4f7f\u7528\u6620\u5c04\u5b57\u4f53\u5e03\u5c40
config.description.flaExportUseMappedFontLayout = \u5f53FLA\u5bfc\u51fa\u8fc7\u7a0b\u4e2d\u5b9e\u9645\u5b57\u4f53\u6ca1\u6709\u5e03\u5c40\u65f6,\u5728\u786e\u5b9a\u5b57\u6bcd\u95f4\u8ddd\u65f6\u4f7f\u7528\u6307\u5b9a\u7684\u6e90\u5b57\u4f53\u524d\u8fdb\u503c
#after 20.0.0
config.name.formatting.tab.size = \u9009\u9879\u5361\u5927\u5c0f
config.description.formatting.tab.size = \u6bcf\u4e2a\u9009\u9879\u5361\u7684\u7a7a\u683c\u6570
config.name.boxBlurPixelsLimit = \u6846\u6a21\u7cca\u6ee4\u955c\u50cf\u7d20\u9650\u5236
config.description.boxBlurPixelsLimit = \u8ba1\u7b97boxblur\u6ee4\u955c\u7684\u6700\u5927\u50cf\u7d20\u6570.\u5b9e\u9645\u9650\u5236\u662f\u8fd9\u4e2a\u6570\u5b57\u4e58\u4ee510000.\u5982\u679c\u50cf\u7d20\u6570\u8f83\u5927,\u5219blurX\u548cblurY\u4f1a\u51cf\u5c11
config.name.as3ExportNamesUseClassNamesOnly = \u5bfc\u51fa\u8d44\u4ea7\u7684\u540d\u79f0\u4ec5\u57fa\u4e8e\u7c7b\u522b(AS3)
config.description.as3ExportNamesUseClassNamesOnly = \u5bfc\u51fa\u7684\u8d44\u4ea7\u6587\u4ef6(\u56fe\u50cf,\u58f0\u97f3\u7b49)\u4ec5\u4eceSymbolClass\u6807\u7b7e\u4e2d\u83b7\u53d6\u540d\u79f0,\u5373\u5176\u5206\u914d\u7684\u7c7b\u522b.\u672a\u6dfb\u52a0\u5b57\u7b26id.\u6b64\u5916,\u5f53\u4e3a\u540c\u4e00\u8d44\u4ea7\u5206\u914d\u591a\u4e2a\u7c7b\u522b\u65f6,\u5b83\u4f1a\u88ab\u591a\u6b21\u5bfc\u51fa.(\u9002\u7528\u4e8eActionScript 3 SWF)
config.name.jnaTempDirectory = JNA\u4e34\u65f6\u76ee\u5f55
config.description.jnaTempDirectory = JNA DLL\u7684\u4e34\u65f6\u76ee\u5f55\u8def\u5f84\u7b49.\u8fd9\u9700\u8981\u8bbe\u7f6e\u4e3a\u4e0d\u5305\u542b\u4efb\u4f55Unicode\u5b57\u7b26\u7684\u8def\u5f84.\u5982\u679c\u672a\u8bbe\u7f6e,\u5219\u4f7f\u7528\u5f53\u524d\u7528\u6237TEMP\u76ee\u5f55
config.name.flaExportFixShapes = FLA\u5bfc\u51fa-\u4fee\u590d\u5f62\u72b6(\u7f13\u6162)
config.description.flaExportFixShapes = \u5e94\u7528\u5206\u5272\u91cd\u53e0\u8fb9\u7684\u8fc7\u7a0b\u6765\u5c1d\u8bd5\u4fee\u590d\u67d0\u4e9b\u5f62\u72b6\u4e0a\u7f3a\u5931\u7684\u586b\u5145.\u5bf9\u4e8e\u67d0\u4e9b\u590d\u6742\u7684\u5f62\u72b6,\u8fd9\u53ef\u80fd\u4f1a\u975e\u5e38\u7f13\u6162
config.name.lastExportResampleWav = \u91cd\u65b0\u91c7\u6837wav\u7684\u6700\u540e\u8bbe\u7f6e
config.description.lastExportResampleWav = \u4e0a\u6b21\u5c06wav\u91cd\u65b0\u91c7\u6837\u8bbe\u7f6e\u4e3a44kHz
config.name.previewResampleSound = \u5728\u58f0\u97f3\u9884\u89c8\u4e2d\u91cd\u65b0\u91c7\u6837
config.description.previewResampleSound = \u5728\u58f0\u97f3\u9884\u89c8\u4e2d\u91cd\u65b0\u91c7\u6837\u81f344kHz
config.name.lastExportTransparentBackground = \u5e27\u5bfc\u51fa\u4e2d\u5ffd\u7565\u80cc\u666f\u989c\u8272\u7684\u6700\u540e\u8bbe\u7f6e
config.description.lastExportTransparentBackground = \u5ffd\u7565\u5e27\u5bfc\u51fa\u7684\u80cc\u666f\u989c\u8272\u4ee5\u4f7f\u80cc\u666f\u900f\u660e\u7684\u6700\u540e\u8bbe\u7f6e
config.name.warningAbcClean = \u6e05\u7406\u5b57\u8282\u7801\u64cd\u4f5c\u65f6\u8b66\u544a
config.description.warningAbcClean = \u5728\u6267\u884c\u5b57\u8282\u7801\u6e05\u7406\u64cd\u4f5c\u4e4b\u524d\u663e\u793a\u8b66\u544a
config.name.warningAddFunction = \u5728AS3 P-code\u4e2d\u6dfb\u52a0\u65b0\u51fd\u6570\u65f6\u8b66\u544a
config.description.warningAddFunction = \u5728AS3 P-code\u4e2d\u521b\u5efa\u65b0\u51fd\u6570\u4e4b\u524d\u663e\u793a\u8b66\u544a.\u5b83\u8fd8\u663e\u793a\u4e86\u4e00\u4e9b\u64cd\u4f5c\u5982\u4f55\u5de5\u4f5c\u7684\u4fe1\u606f
#after 21.0.2
config.name.linkAllClasses = \u6dfb\u52a0\u6240\u6709\u7c7b(\u58f0\u97f3,\u5b57\u4f53,\u56fe\u50cf)\u7684\u94fe\u63a5
config.description.linkAllClasses = \u6dfb\u52a0\u94fe\u63a5SWF\u4e2d\u6240\u6709(\u58f0\u97f3,\u5b57\u4f53,\u56fe\u50cf)\u7c7b\u7684\u7279\u6b8a\u811a\u672c.\u5f53\u6ca1\u6709\u5176\u4ed6\u811a\u672c\u94fe\u63a5\u5b83\u4eec\u65f6\u5f88\u6709\u7528,\u5728\u7f16\u8bd1\u6587\u4ef6\u4e2d\u4ecd\u7136\u53ef\u7528
#after 21.1.0
config.name.recentColors = \u6700\u8fd1\u7684\u989c\u8272
config.description.recentColors = \u989c\u8272\u5bf9\u8bdd\u6846\u4e2d\u7684\u6700\u65b0\u989c\u8272
#after 21.1.1
config.name.gui.splitPaneEasyVertical.dividerLocationPercent = (\u5185\u90e8)\u7b80\u6613UI\u5782\u76f4\u62c6\u5206\u5668\u4f4d\u7f6e
config.name.gui.splitPaneEasyHorizontal.dividerLocationPercent = (\u5185\u90e8)\u7b80\u6613UI\u6c34\u5e73\u62c6\u5206\u5668\u4f4d\u7f6e
config.name.lastSessionEasySwf = \u4e0a\u6b21\u7b80\u6613\u7f16\u8f91\u5668\u4f1a\u8bdd\u6587\u4ef6
config.description.lastSessionEasySwf = \u5305\u542b\u5728\u7b80\u6613\u7f16\u8f91\u5668\u4e0b\u4e0a\u6b21\u4f1a\u8bdd\u4e2d\u9009\u62e9\u7684SWF
config.name.maxScriptLineLength = \u6700\u5927\u811a\u672c\u884c\u957f\u5ea6
config.description.maxScriptLineLength = \u6362\u884c\u524d\u811a\u672c\u7f16\u8f91\u5668\u4e2d\u7684\u6700\u5927\u884c\u957f\u5ea6.0=\u65e0\u9650\u5236.\u5728linux\u4e0a\u663e\u793a\u975e\u5e38\u5927\u7684\u884c\u53ef\u80fd\u4f1a\u6709\u95ee\u9898,\u8fd9\u5c31\u662f\u4e3a\u4ec0\u4e48\u5b83\u5728\u9ed8\u8ba4\u60c5\u51b5\u4e0b\u53d7\u5230\u9650\u5236
#after 21.1.3
config.name.lastSolEditorDirectory = \u4e0a\u6b21\u5b58\u6863\u7f16\u8f91\u5668\u76ee\u5f55
config.description.lastSolEditorDirectory = \u4e0a\u6b21\u6253\u5f00/\u4fdd\u5b58SOL\u6587\u4ef6\u7684\u76ee\u5f55
