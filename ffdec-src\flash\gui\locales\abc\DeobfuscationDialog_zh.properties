# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = \u5904\u7406\u6240\u6709\u7c7b
dialog.title = P\u4ee3\u7801\u53cd\u6df7\u6dc6
deobfuscation.level = \u4ee3\u7801\u53cd\u6df7\u6dc6\u7b49\u7ea7:
deobfuscation.removedeadcode = \u5220\u9664\u65e0\u6548\u4ee3\u7801
deobfuscation.removetraps = \u6e05\u9664traps
deobfuscation.restorecontrolflow = \u91cd\u5efa\u63a7\u5236\u6d41
button.ok = \u786e\u5b9a
button.cancel = \u53d6\u6d88
deobfuscation.scope = \u8303\u56f4
deobfuscation.scope.method = \u5f53\u524d\u65b9\u6cd5
deobfuscation.scope.script = \u5f53\u524d\u811a\u672c
deobfuscation.scope.swf = \u6574\u4e2aSWF
warning.modify = \u8b66\u544a: \u6b64\u64cd\u4f5c\u5c06\u66f4\u6539\u6574\u4e2aSWF\u6587\u4ef6.\r\n\u5982\u679c\u4f60\u53ea\u9700\u8981\u53cd\u6df7\u6dc6\u663e\u793a, \r\n\u8bf7\u4f7f\u7528\u8bbe\u7f6e\u754c\u9762\u4e2d\u7684"\u81ea\u52a8\u53cd\u6df7\u6dc6"\u9009\u9879\r\n\u6216\u70b9\u51fb\u811a\u672c\u7f16\u8f91\u5668\u4e0a\u65b9\u7684\u5c0f\u836f\u4e38\u56fe\u6807.
