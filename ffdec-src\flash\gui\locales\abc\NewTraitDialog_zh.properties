# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
type.method = \u65b9\u6cd5
type.getter = \u83b7\u53d6\u5668
type.setter = \u8bbe\u7f6e\u5668
type.const = \u5e38\u91cf
type.slot = \u69fd(\u53d8\u91cf)
checkbox.static = \u9759\u6001
dialog.title = \u65b0\u5efa Trait
error.name = \u60a8\u5fc5\u987b\u4e3a Trait \u547d\u540d
