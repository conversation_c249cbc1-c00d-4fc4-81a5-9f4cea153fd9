# Copyright (C) 2024 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
library = Kitapl\u0131k
library.folder.images = g\u00f6r\u00fcnt\u00fcler
library.folder.graphics = grafikler
library.folder.shapeTweens = aralar \u015fekli
library.folder.texts = metinler
library.folder.fonts = yaz\u0131 tipleri
library.folder.movieClips = film klipleri
library.folder.buttons = d\u00fc\u011fmeler
library.folder.sounds = sesler
library.folder.videos = videolar
library.header.name = Ad
library.header.asLinkage = AS Ba\u011flant\u0131s\u0131
item.image = g\u00f6r\u00fcnt\u00fc
item.graphic = grafik
item.shapeTween = ara \u015fekli
item.text = metin
item.font = yaz\u0131 tipi
item.movieClip = film klibi
item.button = d\u00fc\u011fme
item.sound = ses
item.video = video
item.unknown = bilinmeyen
undo = %action% geri al
undo.cannot = Geri al\u0131nam\u0131yor
redo = %action% yinele
redo.cannot = Yinelenemiyor
transform = D\u00f6n\u00fc\u015ft\u00fcr
action.addFrame = \u00c7er\u00e7eve ekle
action.addKeyFrame = Anahtar \u00e7er\u00e7eve ekle
action.addKeyFrameWithBlankFrameBefore = \u00d6ncesinde bo\u015f kareler olan anahtar \u00e7er\u00e7eve ekle
action.removeFrame = \u00c7er\u00e7eveyi kald\u0131r
action.transform = D\u00f6n\u00fc\u015ft\u00fcr
action.move = Ta\u015f\u0131
action.addToStage = Sahneye ekle
action.change = %item% de\u011fi\u015ftir
action.change.compression = S\u0131k\u0131\u015ft\u0131rma
action.change.swfVersion = SWF s\u00fcr\u00fcm\u00fc
action.change.encrypted = \u015eifrelenmi\u015f
action.change.gfx = GFX
action.change.frameRate = \u00c7er\u00e7eve h\u0131z\u0131
action.change.width = Geni\u015filik
action.change.height = Y\u00fckseklik
action.change.colorEffect = Renk efekti
timeline.main = Ana zaman \u00e7izelgesi
timeline.item = %item% zaman \u00e7izelgesi
timeline.item.cancel = \u0130ptal - Ana zaman \u00e7izelgesine gitmek i\u00e7in t\u0131klay\u0131n
properties = \u00d6zellikler
properties.document = Belge
properties.instance.single = %item% \u00f6rne\u011fi
properties.instance.multiple = %count% \u00f6rnek
properties.instance.none = \u00d6rnek yok
properties.instance.header.positionSize = Konum ve boyut
properties.instance.header.colorEffect = Renk efekti
property.label = %item%:
property.instance.item = \u00d6\u011fe
property.instance.colorEffect.alpha = Alfa
property.instance.colorEffect.red = K\u0131rm\u0131z\u0131
property.instance.colorEffect.green = Ye\u015fil
property.instance.colorEffect.blue = Mavi
property.instance.positionSize.x = X
property.instance.positionSize.y = Y
property.instance.positionSize.width = G
property.instance.positionSize.height = Y
properties.instance.header.display = G\u00f6r\u00fcnt\u00fcle
property.instance.display.visible = G\u00f6r\u00fclebilir
property.instance.display.blending = Kar\u0131\u015ft\u0131rma
property.instance.display.blending.normal = Normal
property.instance.display.blending.layer = Katman
property.instance.display.blending.multiply = \u00c7arp
property.instance.display.blending.screen = Ekran
property.instance.display.blending.lighten = Ayd\u0131nlat
property.instance.display.blending.darken = Karart
property.instance.display.blending.difference = Fark
property.instance.display.blending.add = Ekle
property.instance.display.blending.subtract = \u00c7\u0131kart
property.instance.display.blending.invert = Ters \u00e7evir
property.instance.display.blending.alpha = Alfa
property.instance.display.blending.erase = Sil
property.instance.display.blending.overlay = Kaplama
property.instance.display.blending.hardlight = Sabit \u0131\u015f\u0131k
property.instance.display.cacheAsBitmap = Bitmap olarak \u00f6nbelle\u011fe al
property.instance.display.cacheAsBitmap.transparent = Saydam
property.instance.display.cacheAsBitmap.opaque = Opak
#after 22.0.2
property.document.backgroundColor = Arka plan rengi
properties.instance.header.filters = Filtreler
property.instance.filters.header.property = \u00d6zellik
property.instance.filters.header.value = De\u011fer
property.instance.filters.indeterminate = Belirsiz
property.instance.filters = Filtreler
property.instance.filters.menu.add = Filtre ekle
property.instance.filters.menu.add.removeAll = T\u00fcm\u00fcn\u00fc kald\u0131r
property.instance.filters.menu.remove = Filtreyi kald\u0131r
filter.bevel = E\u011fim
filter.blur = Bulan\u0131kla\u015ft\u0131r
filter.colormatrix = Rengi ayarla
filter.convolution = Evri\u015fim
filter.dropshadow = G\u00f6lge d\u00fc\u015f\u00fcr
filter.glow = Par\u0131lt\u0131
filter.gradientbevel = Degrade e\u011fimi
filter.gradientglow = Degrade par\u0131lt\u0131s\u0131
convolution.identity = Kimlik
convolution.boxBlur = Kutu bulan\u0131kl\u0131\u011f\u0131
convolution.gaussianBlur = Gauss bulan\u0131kl\u0131\u011f\u0131
convolution.sharpen = Keskinle\u015ftir
convolution.edgeDetectionXSobel = Kenar alg\u0131lama X (Sobel)
convolution.edgeDetectionYSobel = Kenar alg\u0131lama Y (Sobel)
convolution.edgeDetectionXPrewitt = Kenar alg\u0131lama X (Prewitt)
convolution.edgeDetectionYPrewitt = Kenar alg\u0131lama Y (Prewitt)
convolution.edgeDetectionXScharr = Kenar alg\u0131lama X (Scharr)
convolution.edgeDetectionYScharr = Kenar alg\u0131lama Y (Scharr)
convolution.laplacian = Laplasyen
convolution.emboss = Kabartma
convolution.outline = Anahat
convolution.motionBlurX = Hareket bulan\u0131kl\u0131\u011f\u0131 X
convolution.motionBlurY = Hareket bulan\u0131kl\u0131\u011f\u0131 Y
convolution.highPass = Y\u00fcksek ge\u00e7i\u015f
property.instance.filters.menu.clipboard = Pano
property.instance.filters.menu.clipboard.copySelected = Se\u00e7ileni kopyala
property.instance.filters.menu.clipboard.copyAll = T\u00fcm\u00fcn\u00fc kopyala
property.instance.filters.menu.clipboard.paste = Yap\u0131\u015ft\u0131r
property.linkXY = X ve Y \u00f6zellik de\u011ferlerini ba\u011fla
property.instance.filters.strength = g\u00fc\u00e7
property.instance.filters.blurX = bulan\u0131kl\u0131k X
property.instance.filters.blurY = bulan\u0131kl\u0131k Y
property.instance.filters.passes = ge\u00e7i\u015fler
property.instance.filters.gradient = gradyan
property.instance.filters.innerShadow = i\u00e7 g\u00f6lge
property.instance.filters.compositeSource = bile\u015fik kaynak
property.instance.filters.onTop = \u00fcstte
property.instance.filters.dropShadowColor = damla g\u00f6lge rengi
property.instance.filters.knockout = yok et
property.instance.filters.distance = mesafe
property.instance.filters.angle = a\u00e7\u0131
property.instance.filters.shadowColor = g\u00f6lge rengi
property.instance.filters.highlightColor = vurgu rengi
property.instance.filters.brightness = parlakl\u0131k
property.instance.filters.contrast = kontrast
property.instance.filters.saturation = doygunluk
property.instance.filters.hue = renk tonu
property.instance.filters.divisor = b\u00f6len
property.instance.filters.bias = e\u011fim
property.instance.filters.clamp = kelep\u00e7e
property.instance.filters.defaultColor = varsay\u0131lan renk
property.instance.filters.preserveAlpha = alfay\u0131 koru
property.instance.filters.matrix = matris
property.instance.filters.glowColor = par\u0131lt\u0131 rengi
property.instance.filters.innerGlow = i\u00e7 par\u0131lt\u0131
property.instance.filters.menu.enable = Filtreyi etkinle\u015ftir veya devre d\u0131\u015f\u0131 b\u0131rak
property.instance.display.ratio = Oran
