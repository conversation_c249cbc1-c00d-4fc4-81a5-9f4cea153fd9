# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
proxy.start = Proxy ind\u00edt\u00e1s
proxy.stop = Proxy le\u00e1ll\u00edt\u00e1s
port = Port:
open = Megnyit\u00e1s
clear = Tiszt\u00edt\u00e1s
rename = \u00c1tnevez\u00e9s
remove = Elt\u00e1vol\u00edt\u00e1s
sniff = Sniff:
dialog.title = Proxy
error = Hiba
error.port = Hib\u00e1s portsz\u00e1m form\u00e1tum.
copy.url = URL m\u00e1sol\u00e1sa
save.as = Ment\u00e9s m\u00e1sk\u00e9nt...
replace = Csere...
error.save.as = Nem lehet menteni a f\u00e1jlt
error.replace = Nem lehet cser\u00e9lni az adatot
error.start.server = Nem tudom elind\u00edtani a kiszolg\u00e1l\u00f3t a %port% sz\u00e1m\u00fa porton. K\u00e9rem ellen\u0151rizze, hogy blokkolja-e az adott portot egy m\u00e1sik program.
column.accessed = Hozz\u00e1f\u00e9rve
column.size = M\u00e9ret
column.url = URL
