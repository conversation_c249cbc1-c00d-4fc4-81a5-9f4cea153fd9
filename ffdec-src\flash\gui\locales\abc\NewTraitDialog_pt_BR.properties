# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
type.method = M\u00e9todo
type.getter = Getter
type.setter = Setter
type.const = Const
type.slot = Espa\u00e7o (vari\u00e1vel)
checkbox.static = Est\u00e1tica
dialog.title = Novo trait
error.name = Voc\u00ea deve especificar o nome do trait
