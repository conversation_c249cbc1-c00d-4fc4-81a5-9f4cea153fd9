# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Alle klassen behandelen
dialog.title = P-code deobfuscatie
deobfuscation.level = Code deobfuscatie niveau:
deobfuscation.removedeadcode = Dode code verwijderen
deobfuscation.removetraps = 'Traps' verwijderen
deobfuscation.restorecontrolflow = Controle van de stroom flow herstellen
button.ok = OK
button.cancel = Annuleren
deobfuscation.scope = Bereik:
deobfuscation.scope.method = Huidige methode
deobfuscation.scope.script = Huidig script
deobfuscation.scope.swf = Hele SWF
warning.modify = WAARSCHUWING: Deze actie wijzigt het SWF-bestand.\r\nAls u alleen deobfuscatie wilt weergeven, gebruik dan\r\nde optie 'Automatische deobfuscatie' in Instellingen\r\noch het kleine pilpictogram boven het script
