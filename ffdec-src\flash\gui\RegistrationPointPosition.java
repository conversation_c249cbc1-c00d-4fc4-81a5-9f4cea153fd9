/*
 *  Copyright (C) 2022-2025 JPEXS
 * 
 *  This program is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 * 
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 * 
 *  You should have received a copy of the GNU General Public License
 *  along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */
package com.jpexs.decompiler.flash.gui;

/**
 * <AUTHOR>
 */
public enum RegistrationPointPosition {
    TOP_LEFT(0, 0),
    TOP(0.5, 0),
    TOP_RIGHT(1, 0),
    LEFT(0, 0.5),
    CENTER(0.5, 0.5),
    RIGHT(1, 0.5),
    BOTTOM_LEFT(0, 1),
    BOTTOM(0.5, 1),
    BOTTOM_RIGHT(1, 1);

    private RegistrationPointPosition(double positionX, double positionY) {
        this.positionX = positionX;
        this.positionY = positionY;
    }

    private final double positionX;
    private final double positionY;

    public double getPositionX() {
        return positionX;
    }

    public double getPositionY() {
        return positionY;
    }

}
