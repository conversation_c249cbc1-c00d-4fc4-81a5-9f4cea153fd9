# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
advancedSettings.dialog.title = Ajustes Avanzados
advancedSettings.restartConfirmation = Debe reiniciar el programa para que algunas modificaciones surtan efecto. \u00bfDesea reiniciar ahora?
advancedSettings.columns.name = Nombre
advancedSettings.columns.value = Valor
advancedSettings.columns.description = Descripci\u00f3n
default = default
config.group.name.export = Exportar
config.group.description.export = Configuraci\u00f3n de exportaci\u00f3n
config.group.name.script = Scripts
config.group.description.script = decompilaci\u00f3n relacionada ActionScript
config.group.name.update = Actualizaciones
config.group.description.update = Comprobando actualizaciones
config.group.name.format = Formato
config.group.description.format = Formato del c\u00f3digo ActionScript
config.group.name.limit = L\u00edmites
config.group.description.limit = L\u00edmites de Decompilaci\u00f3n para el c\u00f3digo ofuscado, etc.
config.group.name.ui = Interface
config.group.description.ui = Configuraci\u00f3n de la interfaz de usuario
config.group.name.debug = Depuraci\u00f3n
config.group.description.debug = Configuraci\u00f3n de depuraci\u00f3n
config.group.name.display = Visualizaci\u00f3n
config.group.description.display = Visualizaci\u00f3n de objetos Flash, etc.
config.group.name.decompilation = Decompilation
config.group.description.decompilation = Global decompilation related functions
config.group.name.other = Otros
config.group.description.other = Otras configuraciones no categorizadas
config.name.openMultipleFiles = Abrir m\u00faltiples archivos
config.description.openMultipleFiles = Permitir abrir m\u00faltiples archivos en una sola vez en una misma ventana
config.name.decompile = Mostrar fuente de ActionScript
config.description.decompile = Puede desactivar la decompilaci\u00f3n de AS, entonces solo P-code es mostrado
config.name.dumpView = Vista de volcado
config.description.dumpView = Ver volcado de data en crudo
config.name.useHexColorFormat = Formato de color Hexadecimal
config.description.useHexColorFormat = Mostrar los colores en formato hexadecimal
config.name.parallelSpeedUp = Aceleraci\u00f3n en paralelo
config.description.parallelSpeedUp = El paralelismo puede acelerar la decompilac\u00f3n
config.name.parallelSpeedUpThreadCount = N\u00famero de hilos (0 = auto)
config.description.parallelSpeedUpThreadCount = N\u00famero de hilos para la aceleraci\u00f3n en paralelo. 0 = processor count - 1.
config.name.autoDeobfuscate = Desofuscaci\u00f3n autom\u00e1tica
config.description.autoDeobfuscate = Correr desofuscaci\u00f3n en cada archivo antes de la decompilaci\u00f3n de ActionScript
config.name.cacheOnDisk = Utilizar acumulaci\u00f3n en disco
config.description.cacheOnDisk = Acumular las partes ya decompiladas en el disco duro en lugar de la memoria
config.name.internalFlashViewer = Utilizar un visualizador de Flash propio
config.description.internalFlashViewer = Utilizar el visualizador de Flash de JPEXS en lugar del visualizador est\u00e1ndar para las partes
config.name.gotoMainClassOnStartup = Ir a la clase principal al iniciar (AS3)
config.description.gotoMainClassOnStartup = Navegar al documento de clases del archivo al abrir el SWF
config.name.autoRenameIdentifiers = Renombrar identificadores automaticamente
config.description.autoRenameIdentifiers = Renombrar identificadores inv\u00e1lidos al cargar el SWF
config.name.offeredAssociation = (Interno) Asociaci\u00f3n con archivos SWF mostrados Association with SWF files displayed
config.description.offeredAssociation = La ventana de di\u00e1logo acerca de la asociaci\u00f3n de archivos ya fue mostrada
config.name.decimalAddress = Utilizar direcciones decimales
config.description.decimalAddress = Utilizar direcciones decimales en lugar de hexadecimales
config.name.showAllAddresses = Mostrar todas las direcciones
config.description.showAllAddresses = Mostrar todas las direcciones de instrucciones del ActionScript
config.name.useFrameCache = Utilizar marco de almacenamiento
config.description.useFrameCache = Almancenar cuadros antes de hacer rendering otra vez
config.name.useRibbonInterface = Interface Ribbon
config.description.useRibbonInterface = Destildar para utilizar la interfaz cl\u00e1sica sin el men\u00fa ribbon
config.name.openFolderAfterFlaExport = Abrir carpeta luego de exportar el FLA
config.description.openFolderAfterFlaExport = Mostrar el directorio de salida luego de exportar el archivo FLA
config.name.useDetailedLogging = Anotaciones Detalladas
config.description.useDetailedLogging = Guardar mensajes de error detallados e informaci\u00f3n para prop\u00f3sitos de depuraci\u00f3n
config.name._debugMode=FFDec en modo depuraci\u00f3n
config.description._debugMode=Modo para depurar FFDec. Encender en men\u00fa depuraci\u00f3n. Esto no tiene relaci\u00f3n con la funcionalidad de depuraci\u00f3n
config.name.resolveConstants = Resolver constantes en el c\u00f3digo p-code AS1/2
config.description.resolveConstants = Apague esto para mostrar 'constantxx' en lugar de los valores reales en la ventana de P-code
config.name.sublimiter = L\u00edmite de los subs de c\u00f3digo
config.description.sublimiter = L\u00edmite de los subs de c\u00f3digo para el c\u00f3digo ofuscado
config.name.exportTimeout = Tiempo total de espera de exportaci\u00f3n (segundos)
config.description.exportTimeout = El decompilador detendr\u00e1 la exportaci\u00f3n al alcanzar este tiempo
config.name.decompilationTimeoutFile = Tiempo de espera para la decompilaci\u00f3n de un solo archivo (segundos)
config.description.decompilationTimeoutFile = El decompilador detendr\u00e1 la decompilaci\u00f3n de ActionScript al alcanzar este tiempo en un archivo
config.name.paramNamesEnable = Activar nombres de par\u00e1metros en AS3
config.description.paramNamesEnable = Utilizar nombres de par\u00e1metros en la decompilaci\u00f3n puede provocar problemas porque programas oficiales como Flash CS 5.5 insertan erroneamente \u00edndices de nombres de par\u00e1metros
config.name.displayFileName = Mostrar nombre del SWF en el t\u00edtulo
config.description.displayFileName = Muestra el nombre o url del archivo SWF en la ventana de t\u00edtulo (Puede tomar capturas de pantalla)
config.name._debugCopy=FFDec recompilaci\u00f3n de depuraci\u00f3n
config.description._debugCopy=Intenta compilar el archivo SWF otra vez luego de abrirlo para asegurar que se produzca el mismo c\u00f3digo binario. Utilizar para DEPURACION de FFDec solamente!
config.name.dumpTags = Volcar etiquetas a la consola
config.description.dumpTags = Volcar etiquetas a la consola cuando se lee el archivo SWF
config.name.decompilationTimeoutSingleMethod = AS3: Tiempo de espera para la decompilaci\u00f3n del m\u00e9todo \u00fanico (segundos)
config.description.decompilationTimeoutSingleMethod = El decompilador detendr\u00e1 la decompilaci\u00f3n del ActionScript al alcanzar este tiempo en un m\u00e9todo
config.name.lastRenameType = (Interno) Ultimo tipo renombrado
config.description.lastRenameType = Ultimo tipo de identificador utilizado renombrado
config.name.lastSaveDir = (Interno) Ultimo directorio guardado
config.description.lastSaveDir = Ultimo directorio de guardado utilizado
config.name.lastOpenDir = (Interno) Ultimo directorio abierto
config.description.lastOpenDir = Ultimo directorio abierto utilizado
config.name.lastExportDir = (Interno) Ultimo directorio de exportaci\u00f3n
config.description.lastExportDir = Ultimo directorio de exportaci\u00f3n utilizado
config.name.locale = Lenguaje
config.description.locale = Identificador de lugares
config.name.registerNameFormat = Registrar formato de variable
config.description.registerNameFormat = Formato de registro de nombres de variables locales. Utilice %d para n\u00famero de registro.
config.name.maxRecentFileCount = Contador m\u00e1ximo reciente
config.description.maxRecentFileCount = N\u00famero m\u00e1ximo de archivo recientes
config.name.recentFiles = (Interno) Archivos recientes
config.description.recentFiles = Archivos abiertos recientemente
config.name.fontPairingMap = (Interno) Pares de caracteres para la importaci\u00f3n
config.description.fontPairingMap = Pares de fuentes para la importaci\u00f3n de nuevos caracteres
config.name.lastUpdatesCheckDate = (Interno) Fecha del \u00faltimo chequeo de actualizaci\u00f3n
config.description.lastUpdatesCheckDate = Fecha del \u00faltimo chequeo de actualizaciones en el servidor
config.name.gui.window.width = (Interno) Ultima anchura de la ventana
config.description.gui.window.width = Ultimo anchura de la ventana guardada
config.name.gui.window.height = (Interno) Ultima altura de la ventana
config.description.gui.window.height = ultima altura de la ventana guardada
config.name.gui.window.maximized.horizontal = (Interno) Ventana maximizada horizontalmente
config.description.gui.window.maximized.horizontal = Ultimo estado de la ventana - maximizada horizontalmente
config.name.gui.window.maximized.vertical = (Interno) Ventana maximizada verticalmente
config.description.gui.window.maximized.vertical = Ultimo estado de la ventana - maximizada verticalmente
config.name.gui.avm2.splitPane.dividerLocationPercent=(Interno) Ubicaci\u00f3n del divisor del AS3
config.description.gui.avm2.splitPane.dividerLocationPercent=
config.name.gui.actionSplitPane.dividerLocationPercent = (Interno) Ubicaci\u00f3n del divisor del AS1/2
config.description.gui.actionSplitPane.dividerLocationPercent = 
config.name.gui.previewSplitPane.dividerLocationPercent = (Interno) Previsualizar la ubicaci\u00f3n del divisor
config.description.gui.previewSplitPane.dividerLocationPercent = 
config.name.gui.splitPane1.dividerLocationPercent=(Interno) Ubicaci\u00f3n del divisor 1
config.description.gui.splitPane1.dividerLocationPercent=
config.name.gui.splitPane2.dividerLocationPercent=(Interno) Ubicaci\u00f3n del divisor 2
config.description.gui.splitPane2.dividerLocationPercent=
config.name.saveAsExeScaleMode = Guardar como modo de escala EXE
config.description.saveAsExeScaleMode = Modo de escala para la exportaci\u00f3n EXE
config.name.syntaxHighlightLimit = M\u00e1ximo de caracteres para el resaltado de sintaxis
config.description.syntaxHighlightLimit = N\u00famero m\u00e1ximo de caracteres para ejecutar el resaltado de sintaxis
config.name.guiFontPreviewSampleText = (Interno) Ultimo texto de la vista preliminar de la fuente
config.description.guiFontPreviewSampleText = Ultimo \u00edndice de lista del texto de la vista preliminar de la fuente
config.name.gui.fontPreviewWindow.width = (Interno) Ultimo ancho de la vista preliminar de la fuente
config.description.gui.fontPreviewWindow.width = 
config.name.gui.fontPreviewWindow.height = (Interno) Ultima altura de la vista preliminar de la fuente
config.description.gui.fontPreviewWindow.height = 
config.name.gui.fontPreviewWindow.posX = (Interno) Ultima X de la ventana de vista previa de la fuente
config.description.gui.fontPreviewWindow.posX = 
config.name.gui.fontPreviewWindow.posY = (Interno) Ultima Y de la ventana de vista previa de la fuente
config.description.gui.fontPreviewWindow.posY = 
config.name.formatting.indent.size = Caracteres por indentaci\u00f3n
config.description.formatting.indent.size = N\u00famero de espacios (o tabs) para una indentaci\u00f3n
config.name.formatting.indent.useTabs = Tabs para indentaci\u00f3n
config.description.formatting.indent.useTabs = Utilizar tabs en lugar de espacios para indentaci\u00f3n
config.name.beginBlockOnNewLine = Llave en la nueva l\u00ednea
config.description.beginBlockOnNewLine = Comenzar bloque con llave en la nueva l\u00ednea
config.name.check.updates.delay = Retraso de chequeo de actualizaci\u00f3n
config.description.check.updates.delay = Tiempo m\u00ednimo entre chequeos autom\u00e1ticos de actualizaciones cuando la aplicaci\u00f3n arranca
config.name.check.updates.stable = Comprobar por versiones estables
config.description.check.updates.stable = Comprobar por actualizaciones de versiones estables
config.name.check.updates.nightly = Comprobar por versiones de construciones de noche
config.description.check.updates.nightly = Comprobar por actualizaciones de versiones de consutrcciones de noche
config.name.check.updates.enabled = Comprobaci\u00f3n de actualizaciones activado
config.description.check.updates.enabled = Comprobaci\u00f3n autom\u00e1tica de actualizaciones cuando la aplicaci\u00f3n arranca
config.name.export.formats = (Interno) Formatos de exportaci\u00f3n
config.description.export.formats = Ultimos formatos de exportaciones utilizados
config.name.textExportSingleFile = Exportar textos en un solo archivo
config.description.textExportSingleFile = Exportando textos en un archivo en lugar de m\u00faltiples
config.name.textExportSingleFileSeparator = Separador de textos en la exportaci\u00f3n de texto en un archivo
config.description.textExportSingleFileSeparator = Texto a insertar entre los textos en la exportaci\u00f3n de texto en un \u00fanico archivo
config.name.textExportSingleFileRecordSeparator = Separador de registros de exportaci\u00f3n en un \u00fanico archivo
config.description.textExportSingleFileRecordSeparator = Texto para insertar entre los textos en la exportaci\u00f3n de texto en un \u00fanico archivo
config.name.warning.experimental.as12edit=Alertar en la edici\u00f3n directa de AS1/2
config.description.warning.experimental.as12edit=Muestra un alerta en la edici\u00f3n experimental directa de AS1/2
config.name.warning.experimental.as3edit=Alertar en la edici\u00f3n directa de AS3
config.description.warning.experimental.as3edit=Muestra un alerta en la edici\u00f3n experimental directa de AS3
config.name.packJavaScripts = Empaquetar JavaScripts
config.description.packJavaScripts = Ejecutar empaquetadores de JavaScript en scripts creados con exportaci\u00f3n de Canvas.
config.name.textExportExportFontFace = Utilizar font-face al exportar SVG
config.description.textExportExportFontFace = Incrustar archivos de fuente en el SVG utilizando font-face en lugar de formas
config.name.lzmaFastBytes = Bytes r\u00e1pidos de LZMA (valores v\u00e1lidos: 5-255)
config.description.lzmaFastBytes = Par\u00e1metro de bytes r\u00e1pidos del codificador LZMA
config.name.pluginPath = Directorio de Plugin
config.description.pluginPath = -
config.name.showMethodBodyId = Mostrar identificador el cuerpo del m\u00e9todo
config.description.showMethodBodyId = Mostrar el identificador del methodbody para la importaci\u00f3n de linea de comandos
config.name.export.zoom = (Interna) Exportar enfoque
config.description.export.zoom = Last used export zoom
config.name.debuggerPort = Puerto de depuraci\u00f3n
config.description.debuggerPort = Puerto utilizado para depuraci\u00f3n por socke
config.name.displayDebuggerInfo = (Interna) Mostrar informaci\u00f3n del depurador
config.description.displayDebuggerInfo = Mostrar informaci\u00f3n acerca del depurador antes de cambiarlo
config.name.randomDebuggerPackage = Utilizar nombre de paquete aleatorio para el Debugger
config.description.randomDebuggerPackage = Esto renombra el paquete de depurador a una cadena aleatoria que hace dificil detectar la presencia del depurador por ActionScript
config.name.lastDebuggerReplaceFunction = (Interna) Ultima sustituci\u00f3n de traza seleccionada
config.description.lastDebuggerReplaceFunction = Nombre de funci\u00f3n que fue la \u00faltima seleccionada en reemplazo de funci\u00f3n de rastreo con el depurador
config.name.getLocalNamesFromDebugInfo = AS3: Tomar nombres de registros locales desde la informaci\u00f3n de depuraci\u00f3n
config.description.getLocalNamesFromDebugInfo = Si hay informaci\u00f3n de depuraci\u00f3n disponible, renombrar registros locales de _loc_x_ a los nombres reales. Esta opci\u00f3n se puede desactivar dado que algunos ofuscadores utilizan nombres de registros inv\u00e1lidos
config.name.tagTreeShowEmptyFolders = Mostrar carpetas vac\u00edas
config.description.tagTreeShowEmptyFolders = Mostrar carpetas vac\u00edas en el \u00e1rbol de etiqueta.
config.name.autoLoadEmbeddedSwfs = Auto cargar SWFs embebidos
config.description.autoLoadEmbeddedSwfs = Cargar automaticamente los archivos SWF embebidos en las etiquetas DefineBinaryData.
config.name.overrideTextExportFileName = Override text export filename
config.description.overrideTextExportFileName = Puede personalizar el nombre de archivo del texto exportado. Utilice {filename} el marcador de posici\u00f3n para utilizar el nombre del SWF actual.
config.name.showOldTextDuringTextEditing = Mostrar texto viejo durante la edici\u00f3n de texto
config.description.showOldTextDuringTextEditing = Mostrar el texto original de la etiqueta de texto on color gris en el \u00e1rea de vista preliminar.
config.group.name.import = Importar
config.group.description.import = Configuraci\u00f3n de importaciones
config.name.textImportResizeTextBoundsMode = Modo de cambiar el tama\u00f1o de los l\u00edmites del text
config.description.textImportResizeTextBoundsMode = Modo de reajuste de l\u00edmites de texto luego de la edici\u00f3n de texto.
config.name.showCloseConfirmation = Mostrar nuevamente la confirmaci\u00f3n de cerrado
config.description.showCloseConfirmation = Mostrar nuevamente la confirmaci\u00f3n de Cerrar para los archivos modificados.
config.name.showCodeSavedMessage = Mostrar nuevamente mensaje de c\u00f3digo guardado
config.description.showCodeSavedMessage = Mostrar nuevamente el mensaje de c\u00f3digo guardado
config.name.showTraitSavedMessage = Mostrar nuevamente el mensaje de rasgo guardado
config.description.showTraitSavedMessage = Mostrar otra vez mensaje de rasgo guardado
config.name.updateProxyAddress = Http Proxy address for checking updates
config.description.updateProxyAddress = Direcci\u00f3n del Proxy Http para comprobar actualizaciones. Formato: example.com:8080
config.name.editorMode = Modo Editor
config.description.editorMode = Hacer editables las \u00e1reas de texto autmaticamente cuando un Texto o Script es seleccionado
config.name.autoSaveTagModifications = Auto guardar modificaciones de etiquetas
config.description.autoSaveTagModifications = Guardar los cambios cuando una nueva etiqueta es seleccionada en el \u00e1rbol
config.name.saveSessionOnExit = Guardar sesi\u00f3n al salir
config.description.saveSessionOnExit = Guardar la sesi\u00f3n actual y reabrirla una vez que FFDec reinicia (solo funciona con archivos reales)
config.name._showDebugMenu=Mostrar men\u00fa de depuraci\u00f3n FFDec
config.description._showDebugMenu=Mostrar el men\u00fa de depuraci\u00f3n en el men\u00fa de cinta para la depuraci\u00f3n del decompilador.
config.name.allowOnlyOneInstance = Permitir solo una instancia de FFDec (Solo Windows)
config.description.allowOnlyOneInstance = FFDec puede ser ejecutado solo una vez, todos los archivos abiertos ser\u00e1n agregados a una sola ventana. Solo funciona con el sistema operativo Windows.
config.name.scriptExportSingleFile = Exportar scripts a un solo archivo
config.description.scriptExportSingleFile = Exportando scripts a un solo archivo en lugar de m\u00faltiples archivos
config.name.setFFDecVersionInExportedFont = Colocar el n\u00famero de versi\u00f3n de FFDec en las fuentes exportadas
config.description.setFFDecVersionInExportedFont = Cuando este ajuste es deshabilitado, FFDec no agregar\u00e1 el n\u00famero de versi\u00f3n actual a la fuente exportada.
config.name.gui.skin = Piel de interfaz de usuario
config.description.gui.skin = Apariencia de piel
config.name.lastSessionFiles = Ultimos archivos de sesi\u00f3n
config.description.lastSessionFiles = Contiene los archivos abiertos desde la \u00faltima sesi\u00f3n
config.name.lastSessionSelection = Ultima selecci\u00f3n de sesi\u00f3n
config.description.lastSessionSelection = Contiene la selecci\u00f3n desde la \u00faltima sesi\u00f3n
config.name.loopMedia = Bucle de sonidos y sprites
config.description.loopMedia = Reiniciar automaticamente la reproducci\u00f3n the sonidos y sprites
config.name.gui.timeLineSplitPane.dividerLocationPercent = (Internal) Ubicaci\u00f3n cronol\u00f3gica de Splitter
config.description.gui.timeLineSplitPane.dividerLocationPercent = 
config.name.cacheImages = Cach\u00e9 de im\u00e1genes
config.description.cacheImages = Colocar en cach\u00e9 los objetos de im\u00e1gen decodificados
config.name.swfSpecificConfigs = Configuraciones espec\u00edficas del SWF
config.description.swfSpecificConfigs = Contiene las configuraciones espec\u00edficas del SWF
config.name.exeExportMode = Modo de exportaci\u00f3n de EXE
config.description.exeExportMode = Modo de exportaci\u00f3n de EXE
config.name.ignoreCLikePackages = Ignorar paquetes FlashCC / Alchemy o similares
config.description.ignoreCLikePackages = Los paquetes FlashCC/Alchemy usualmente no pueden ser decompilados correctamente. Puede deshabilitarlos para acelerar la decompilaci\u00f3n de otros paquetes.
config.name.overwriteExistingFiles = Sobreescribir los archivos existentes
config.description.overwriteExistingFiles = Sobreescribir los archivos existentes durante la exportaci\u00f3n.  Actualmente solo para scripts AS2/3
config.name.smartNumberFormatting = Usar formato de n\u00famero inteligente
config.description.smartNumberFormatting = Formato de n\u00fameros especiales (por ejemplo colores y tiempos)
config.name.enableScriptInitializerDisplay = (REMOVED) Visualizar inicializadores de script
config.description.enableScriptInitializerDisplay = Habilitar pantalla de inicializadores de script y edici\u00f3n. Este ajuste puede agregar una nueva l\u00ednea a cada archivo de clase para destacar.
config.name.autoOpenLoadedSWFs = Abrir SWFs cargados durante la ejecuci\u00f3n (Visor externo = Solo WINDOWS)
config.description.autoOpenLoadedSWFs = Abre autom\u00e1ticamente todos los SWFs cargados por el cargador de clases AS3 ejecutando SWF cuando se reproduce en el reproductor externo de FFDec. Esta caracter\u00edstica es solo para Windows.
config.name.lastSessionFileTitles = T\u00edtulos de archivo de la \u00faltima sesi\u00f3n
config.description.lastSessionFileTitles = Contiene los t\u00edtulos de archivoabiertos de la \u00faltima sesi\u00f3n (por ejemplo cuando son cargados desde una URL etc.)
config.group.name.paths = Rutas
config.group.description.paths = Ubicaci\u00f3n de los archivos necesarios
#config.group.tip.paths = Usted puede bajar estos archivos desde la p\u00e1gina web de Adobe
#TODO: translate again:
config.group.tip.paths = Download projector and Playerglobal on <a href="%link1%">adobe webpage</a>. Flex SDK can be downloaded on <a href="%link2%">apache web</a>.
config.group.link.paths = https://web.archive.org/web/20220401020702/https://www.adobe.com/support/flashplayer/debug_downloads.html https://flex.apache.org/download-binaries.html
config.name.playerLocation = 1) Ruta de Flash Player projector
config.description.playerLocation = Ubicaci\u00f3n del ejecutable aut\u00f3nomo de Flash Player. Utilizado para la acci\u00f3n Correr.
config.name.playerDebugLocation = 2) Ruta de depuraci\u00f3n de contenido del proyector Flash Player
config.description.playerDebugLocation = Ubicaci\u00f3n del ejecutable de depuraci\u00f3n aut\u00f3nomo de Flash Player. Utilizado para la acci\u00f3n de Debugger.
config.name.playerLibLocation = 3) Ruta de PlayerGlobal (.swc)
config.description.playerLibLocation = Ubicaci\u00f3n de la librer\u00eda playerglobal.swc de Flash Player. Utilizada mayormente para compilaci\u00f3n de AS3.
config.name.debugHalt = Interrumpir ejecuci\u00f3n cuando durante el inicio de depuraci\u00f3n
config.description.debugHalt = Pausar SWF al inicio de la depuraci\u00f3n.
config.name.gui.avm2.splitPane.vars.dividerLocationPercent=(Internal) Ubicaci\u00f3n del men\u00fa divisor de Depuraci\u00f3n
config.description.gui.avm2.splitPane.vars.dividerLocationPercent=
tip = Consejo:
config.name.gui.action.splitPane.vars.dividerLocationPercent = (Internal) AS1/2 Ubicaci\u00f3n del divisor del men\u00fa de Depuraci\u00f3n
config.description.gui.action.splitPane.vars.dividerLocationPercent = 
config.name.setMovieDelay = Retrasar antes de intercambiar el SWF en el reproductor externo en ms
config.description.setMovieDelay = No es recomendable cambiar el valor por debajo de los 1000ms
config.name.warning.svgImport = Advertir en la importaci\u00f3n de SVG
config.description.warning.svgImport = 
config.name.shapeImport.useNonSmoothedFill = Utilice relleno no alisada cuando una forma se sustituye con una imagen
config.description.shapeImport.useNonSmoothedFill = 
config.name.internalFlashViewer.execute.as12=AS1/2 en un visor de Flash propio (Experimental)
config.description.internalFlashViewer.execute.as12=Intentar ejecutar ActionScript 1/2 durante la reproducci\u00f3n del SWF utilizando el visor de FFDec
config.name.warning.hexViewNotUpToDate = Mostrar advertencia de Vista Hexadecimal no al d\u00eda
config.description.warning.hexViewNotUpToDate = 
config.name.displayDupInstructions = Mostrar \u00a7\u00a7dup instrucciones
config.description.displayDupInstructions = Mostrar \u00a7\u00a7dup instrucciones en el c\u00f3digo. Sin ellas, el c\u00f3digo puede ser facilmente compilado pero alg\u00fan c\u00f3digo con efectos secundarios puede ser ejecutado dos veces.
config.name.useRegExprLiteral = Decompilar RegExp como /patr\u00f3n/mod literal.
config.description.useRegExprLiteral = Uitilzar sint\u00e1xis /patr\u00f3n/mod al decompilar expresiones regulares. De otra manera RegExp("pat","mod") es utilizado
config.name.handleSkinPartsAutomatically = Manejar metadata [SkinPart] automaticamente
config.description.handleSkinPartsAutomatically = Decompila y edita directamente metadata [SkinPart] automaticamente. Cuando est\u00e1 apagada, el atributo _skinParts y su m\u00e9todo getter es visible y manualmente editable.
config.name.simplifyExpressions = Simplificar expresiones
config.description.simplifyExpressions = Evaluar y simplificar expresiones para hacer el c\u00f3digo m\u00e1s legible
config.name.resetLetterSpacingOnTextImport = Reestablecer espaciado entre l\u00edneas en la importaci\u00f3n de texto
config.description.resetLetterSpacingOnTextImport = Util para las fuentes cil\u00edrico, porque son mas anchas
