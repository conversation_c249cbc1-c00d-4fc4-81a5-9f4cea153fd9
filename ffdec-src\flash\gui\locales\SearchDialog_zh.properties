# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
checkbox.ignorecase = \u5ffd\u7565\u5927\u5c0f\u5199
checkbox.regexp = \u6b63\u5219\u8868\u8fbe\u5f0f
button.ok = \u786e\u5b9a
button.cancel = \u53d6\u6d88
label.searchtext = \u641c\u7d22\u6587\u672c:
label.replacementtext = \u66ff\u6362\u6587\u672c
#dialog.title = ActionScript search
dialog.title = \u6587\u672c\u641c\u7d22
dialog.title.replace = \u6587\u672c\u66ff\u6362
error = \u9519\u8bef
error.invalidregexp = \u65e0\u6548\u7684\u8868\u8fbe\u5f0f
checkbox.searchText = \u5728\u6587\u672c\u4e2d\u641c\u7d22
checkbox.searchAS = \u5728AS\u4e2d\u641c\u7d22
checkbox.replaceInParameters = \u66ff\u6362\u53c2\u6570
checkbox.searchPCode = \u5728P-Code\u4e2d\u641c\u7d22
#after 13.0.3
label.scope = \u8303\u56f4:
scope.currentFile = \u5f53\u524dSWF
scope.selection = \u9009\u4e2d(%selection%)
scope.allFiles = \u6240\u6709\u6253\u5f00\u7684SWF
scope.selection.items = %count%\u9879
#after 16.3.1
scope.currentFile.abc = \u5f53\u524d\u5b57\u8282\u7801
