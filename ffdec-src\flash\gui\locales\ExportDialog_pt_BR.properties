# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
shapes = Formas
shapes.svg = SVG
shapes.png = PNG
shapes.bmp = BMP
shapes.canvas = Canvas HTML5
shapes.swf = SWF
texts = Textos
texts.plain = Texto simples
texts.formatted = Texto formatado
texts.svg = SVG
images = Imagens
images.png_gif_jpeg=PNG/GIF/JPEG
images.png = PNG
images.jpeg = JPEG
images.bmp = BMP
movies = Filmes
movies.flv = FLV (Sem \u00e1udio)
sounds = Sons
sounds.mp3_wav_flv=MP3/WAV/FLV
sounds.flv = FLV (Somente \u00e1udio)
sounds.mp3_wav=MP3/WAV
sounds.wav = WAV
scripts = Scripts
scripts.as = ActionScript
scripts.pcode = P-code
scripts.pcode_hex=P-code com Hex
scripts.hex = Hex
scripts.constants = Constantes
scripts.as_method_stubs=Stubs do m\u00e9todo ActionScript
scripts.pcode_graphviz=P-code GraphViz
binaryData = Dados bin\u00e1rios
binaryData.raw = Dados brutos
dialog.title = Exportar...
button.ok = OK
button.cancel = Cancelar
morphshapes = Morfshapes
morphshapes.gif = GIF
morphshapes.svg = SVG
morphshapes.canvas = Canvas HTML5
morphshapes.swf = SWF
morphshapes.bmp_start_end=BMP (in\u00edcio, fim)
morphshapes.png_start_end=PNG (in\u00edcio, fim)
morphshapes.svg_start_end=SVG (in\u00edcio, fim)
frames = Quadros
frames.png = PNG
frames.gif = GIF
frames.avi = AVI
frames.svg = SVG
frames.canvas = Canvas HTML5
frames.pdf = PDF
frames.bmp = BMP
frames.swf = SWF
sprites = Sprites
sprites.png = PNG
sprites.gif = GIF
sprites.avi = AVI
sprites.svg = SVG
sprites.canvas = Canvas HTML5
sprites.pdf = PDF
sprites.bmp = BMP
sprites.swf = SWF
buttons = Bot\u00f5es
buttons.png = PNG
buttons.svg = SVG
buttons.bmp = BMP
buttons.swf = SWF
fonts = Fontes
fonts.ttf = TTF
fonts.woff = WOFF
zoom = Zoom
zoom.percent = %
zoom.invalid = Valor de zoom inv\u00e1lido..
symbolclass = Mapeamento de Symbol-Class
symbolclass.csv = CSV
#after 18.0.0
images.png_gif_jpeg_alpha=PNG/GIF/JPEG+alpha
#after 18.5.0
fonts4=DefineFont4
fonts4.cff=CFF
embed = Exportar ativos incorporados atrav\u00e9s de [Embed]
