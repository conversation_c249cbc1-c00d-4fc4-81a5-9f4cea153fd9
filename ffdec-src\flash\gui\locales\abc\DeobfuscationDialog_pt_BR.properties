# Copyright (C) 2010-2016 JPEXS
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
processallclasses = Processar todas as classes
dialog.title = Desofusca\u00e7\u00e3o de P-Code
deobfuscation.level = Nivel de desofusca\u00e7\u00e3o do c\u00f3digo:
deobfuscation.removedeadcode = Remover c\u00f3digo morto
deobfuscation.removetraps = Remover armadilhas
deobfuscation.restorecontrolflow = Restaurar o fluxo de controle
button.ok = OK
button.cancel = Cancelar
deobfuscation.scope = Escopo:
deobfuscation.scope.method = M\u00e9todo atual
deobfuscation.scope.script = Script atual
deobfuscation.scope.swf = SWF inteiro
warning.modify = AVISO: Esta a\u00e7\u00e3o modificar\u00e1 o arquivo SWF.\r\nSe voc\u00ea deseja apenas desofusca\u00e7\u00e3o para exibi\u00e7\u00e3o, use\r\na op\u00e7\u00e3o "Desofusca\u00e7\u00e3o autom\u00e1tica" em Configura\u00e7\u00f5es\r\nno pequeno \u00edcone de p\u00edlula acima do editor de script.
